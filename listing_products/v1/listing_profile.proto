syntax = "proto3";

package listing_products.v1;

option go_package = "github.com/vendasta/generated-protos-go/listing_products/v1;listing_products_v1";
option java_outer_classname = "ListingProfileProto";
option java_package = "com.vendasta.listingproducts.v1.generated";


import "vendasta_types/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "listing_products/v1/date.proto";
import "listing_products/v1/dayofweek.proto";
import "listing_products/v1/timeofday.proto";
import "google/protobuf/struct.proto";

// Represents a listing profile
message ListingProfile {
  string business_id = 1;
  google.protobuf.Timestamp deleted = 2;
  google.protobuf.Timestamp created = 3;
  google.protobuf.Timestamp updated = 4;
  int64 version = 5;
  ExternalIdentifiers external_identifiers = 6;
  SocialURLs social_urls = 8;
  // Deprecated: Use BusinessHours instead
  HoursOfOperation hours_of_operation = 9 [deprecated = true];
  RichData rich_data = 10;
  Location nap_data = 11;
  repeated BusinessHours business_hours = 12;
  repeated GoogleAttribute google_attributes = 13;
  repeated GoogleAttributeMetaData google_attributes_metadata = 14;
  repeated MoreHoursType more_hours_types = 15;
  repeated BingAttribute bing_attributes = 16;
  repeated BingAttributeMetaData bing_attributes_metadata = 17;
  LegacyProductDetails legacy_product_details = 18;
  MarketingInfo marketing_info = 19;
}

message ExternalIdentifiers {
  string partner_id = 1;
  repeated string tax_ids = 2;
  repeated string v_category_ids = 3;
  string market_id = 4;
  string sales_person_id = 5;
  repeated string additional_sales_person_ids = 6;
  repeated string tags = 7;
  string customer_identifier = 8;
  string origin = 9;
  string social_profile_id = 10;
  repeated string job_id = 11;
  repeated string action_lists = 12;
  string update_origin = 13;
}

message SocialURLs {
  string googleplus_url = 1;
  string linkedin_url = 2;
  string foursquare_url = 3;
  string twitter_url = 4;
  string facebook_url = 5;
  string rss_url = 6;
  string youtube_url = 7;
  string instagram_url = 8;
  string pinterest_url = 9;
}

message HoursOfOperation { // Deprecated
  message Span {
    repeated string day_of_week = 1;
    string opens = 2;
    string closes = 3;
    string description = 4;
  }
  repeated Span hours_of_operation = 1;
}

message RichData {
  enum PaymentMethods {
    AMERICAN_EXPRESS = 0;
    ANDROID_PAY = 1;
    APPLE_PAY = 2;
    CASH = 3;
    CHECK = 4;
    DEBIT = 5;
    DINERS_CLUB = 6;
    DISCOVER = 7;
    MASTERCARD = 8;
    PAYPAL = 9;
    SAMSUNG_PAY = 10;
    STORE_CARD = 11;
    TRAVELERS_CHECK = 12;
    VISA = 13;
    CCS = 14;
    SODEXO = 15;
    GOPAY = 16;
    V_PAY = 17;
    FINANCING = 18;
    INVOICE = 19;
    PAYSEC = 20;
    BITCOIN = 21;
  }
  string toll_free_number = 1;
  string description = 2;
  string short_description = 3;
  repeated string services_offered = 4;
  repeated string brands_carried = 5;
  string landmark = 6;
  repeated PaymentMethods payment_methods = 7;
  HealthCareProfessionalInformation health_care_professional_information = 8;
  ServiceAvailability service_availability = 9;
  string cell_number = 10;
  string fax_number = 11;
  string email = 12;
  repeated string seo_keywords = 13;
  string booking_url = 14;
  repeated ConditionalField conditional_fields = 15;
  repeated string syncing_seo_keywords = 16;
  repeated string inferred_attributes = 17;
}

message ConditionalField {
  string id = 1;
  string value = 2;
}

message DoctorDotComCategory {
  int64 id = 1;
  string full_name = 2;
  string nucc_taxonomy_code = 3;
}

message HealthCareProfessionalInformation {
  enum Gender {
    NotSpecified = 0;
    Female = 1;
    Male = 2;
    Other = 3;
  }

  enum IsProvider {
    IsProviderNotSpecified = 0;
    IsProviderTrue = 1;
    IsProviderFalse = 2;
  }

  google.protobuf.Timestamp date_of_birth = 1;
  string email = 2;
  repeated string fellowship = 3;
  string first_name = 4;
  Gender gender = 5;
  string initials = 6;
  repeated string insurances_accepted = 7;
  string last_name = 9;
  string medical_license_number = 10;
  string national_provider_identifier = 11;
  string office = 12;
  repeated string professional_credential = 13;
  repeated string residency = 14;
  repeated string school = 15;
  repeated string specialty = 16;
  string standardized_title = 17;
  string state_license = 18;
  google.protobuf.BoolValue is_taking_patients = 19;
  repeated DoctorDotComCategory doctor_dot_com_categories = 20;
  repeated string hospital_affiliations = 21;
  IsProvider is_provider = 22;
  reserved 8;
}

enum IsAvailable {
  UNSET = 0;
  YES = 1;
  NO = 2;
}

// ClosedStatus describes if the business is permanently or temporarily closed, or in a limited state
enum ClosedStatus {
  UNSPECIFIED = 0;
  OPEN = 1;
  LIMITED = 2;
  TEMPORARY = 3;
  PERMANENT = 4;
}

message ServiceAvailability {
  IsAvailable delivery = 1 [deprecated = true];
  IsAvailable no_contact_delivery = 2 [deprecated = true];
  IsAvailable in_store_pickup = 3 [deprecated = true];
  IsAvailable curbside_pickup = 4 [deprecated = true];
  IsAvailable appointments_only = 5 [deprecated = true];
  IsAvailable ecommerce_only = 6;
  ClosedStatus closed_status = 7;
  google.protobuf.Timestamp closed_status_date = 8;
  google.protobuf.Timestamp reopening_date = 10;
  IsAvailable serves_dine_in = 9 [deprecated = true];
}

// Represents a geo point location.
message Geo {
  double latitude = 1;
  double longitude = 2;
}

// Based on https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#Location.BusinessType
enum ServiceAreaBusinessType {
  // Not specified.
  SERVICE_AREA_BUSINESS_TYPE_UNSPECIFIED = 0;
  // Offers service only in the surrounding area (not at the business address).
  SERVICE_AREA_BUSINESS_TYPE_CUSTOMER_LOCATION_ONLY = 1;
  // Offers service at the business address and the surrounding area.
  SERVICE_AREA_BUSINESS_TYPE_CUSTOMER_AND_BUSINESS_LOCATION = 2;
}

// Based on https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#Location.PlaceInfo
message GooglePlace {
 // The ID of the place
  string place_id = 1;
  // The localized name of the place. For example, Scottsdale, AZ.
  string place_name = 2;
  // The city or locality the Google Place is located in
  string city = 3;
}

message ServiceArea {
  ServiceAreaBusinessType business_type = 1;
  repeated GooglePlace places = 2;
}

message Location {
  string company_name = 1;
  string address = 2;
  string address2 = 3;
  string city = 4;
  string state = 5;
  string zip = 6;
  string country = 7;
  string website = 8;
  repeated string work_number = 9;
  repeated string call_tracking_number = 10;
  Geo location = 11;
  string timezone = 12;
  bool service_area_business = 13;
  ServiceArea service_area = 14;
}

message RegularHoursPeriod {
  // open_day indicates the day of the week that this period starts on.
  DayOfWeek open_day = 1;

  // open_time valid values are 00:00-23:59
  TimeOfDay open_time = 2;

  // close_day indicates the day of the week that this period ends on. It should be the same as
  // openDay except when the period goes over midnight.
  DayOfWeek close_day = 3;

  // close_time valid values are 00:00-24:00, where 24:00 represents midnight at the end of the
  // specified day field.
  TimeOfDay close_time = 4;
}

// Describes the different modes that apply to special hour periods
enum SpecialHoursStatus {
  // Indicates that the field was not sent on the API request. This is not valid to be used.
  SPECIAL_HOURS_STATUS_INVALID= 0;

  // Indicates the business is open during the period indicated by
  // `start_date`, `start_time`, `end_date`, and `end_time`
  SPECIAL_HOURS_STATUS_OPEN = 1;

  // Indicates that the business is closed for the entire period from `start_date` to `end_date`.
  // `start_time` and `end_time` will be ignored.
  SPECIAL_HOURS_STATUS_CLOSED = 2;

  // NOT IMPLEMENTED The UI for this status is not included in the first slice.
  // Indicates that there is a holiday during the specified period and the business has not confirmed their hours.
  // These periods should not be sent to listing sites
  SPECIAL_HOURS_STATUS_UNCONFIRMED_HOLIDAY = 3;
}

message SpecialHoursPeriod {
  // status describes the behaviour of the special hours period. See enum options for required special hours fields.
  SpecialHoursStatus status = 1;

  // start_date is the calendar date this special hour period starts on.
  GoogleDate start_date = 2;

  // start_time must be specified if status is open. Valid values are 00:00-24:00 where
  // 24:00 represents midnight at the end of the specified day field.
  // TODO: what is returned with unconfirmed holiday status?
  TimeOfDay start_time = 3;

  // end_date (optional) is the calendar date this special hour period ends on. If endDate field is
  // not set, default to the date specified in startDate. If set, this field must be equal to or after startDate.
  GoogleDate end_date = 4;

  // end_time must be specified if status is open. Valid values are 00:00-24:00, where 24:00
  // represents midnight at the end of the specified day field.
  // TODO: what is returned with unconfirmed holiday status?
  TimeOfDay end_time = 5;
}

message BusinessHours {
  // hours type IDs are based on the Google hours types
  // https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#morehours
  //
  // The hours types are based on the type of business so we don't give an exhaustive list of hours
  // types here. We get the hours types for the business's category using the Google endpoint
  // https://developers.google.com/my-business/reference/businessinformation/rest/v1/categories/batchGet
  string hours_type_id = 1;

  // RegularHours is a collection of times that this location is open for business. Each
  // period represents a range of hours when the location is open during the week. You may specify
  // multiple entries for the same date if you are open for multiple periods (Example lunch and supper)
  repeated RegularHoursPeriod regular_hours = 2;

  // SpecialHours represents a set of time periods when a location's operational hours differ from
  // its regular business hours. This may be a holiday or special event. These hours replace the
  // all regular hours for the day. The openTime and startDate must predate the closeTime and endDate.
  repeated SpecialHoursPeriod special_hours = 3;
}
enum ValueType {
  ATTRIBUTE_VALUE_TYPE_UNSPECIFIED = 0;
  BOOL = 1;
  ENUM = 2;
  URL = 3;
  REPEATED_ENUM = 4;
  INTEGER = 5;
}


message VendorAttributeMetaData {
  string vendor_id = 1;
  oneof vendor_attribute_metadata {
    BingAttributeMetaDataList bing_attribute_metadata = 2;
    GoogleAttributeMetaDataList google_attribute_metadata = 3;
  }
}

message GoogleAttributes {
  repeated GoogleAttribute google_attribute = 1;
}

message BingAttributes {
  repeated BingAttribute bing_attribute = 1;
}

// Represents a single Google attribute
// reference docs:
// https://developers.google.com/my-business/reference/businessinformation/rest/v1/attributes/list#attributemetadata
// https://developers.google.com/my-business/reference/businessinformation/rest/v1/Attributes
message  GoogleAttribute {
  // The name of the Google attribute in the form "attributes/is_owned_by_women"
  string name = 1;

  // Contains the value of the attribute. This can be of 4 types:
  //   bool
  //   URL (represented as a string google.protobuf.Value)
  //   enum (represented as a string google.protobuf.Value)
  //   repeated_enum (represented as a struct google.protobuf.Value)
  // https://developers.google.com/my-business/reference/businessinformation/rest/v1/AttributeValueType
  google.protobuf.Value value = 2;
}

message  BingAttribute {
  // The name of the Bing attribute in the form "attributes/is_owned_by_women"
  string name = 1;

  // Contains the value of the attribute. This can be of 4 types:
  //   bool
  //   URL (represented as a string google.protobuf.Value)
  //   enum (represented as a string google.protobuf.Value)
  //   repeated_enum (represented as a struct google.protobuf.Value)
  // https://bpprodpublicstorage.blob.core.windows.net/bingplacesapi/BingPlaces_API_Latest.pdf
  google.protobuf.Value value = 2;
}

message GoogleAttributeMetaDataList {
  repeated GoogleAttributeMetaData google_attribute_metadata = 1;
}

message GoogleAttributeMetaData {
  // The name of the Google Attribute in the form "attributes/is_owned_by_women"
  string parent = 1;
  ValueType value_type = 2;
  string display_name = 3;
  bool repeatable = 4;
  // For some types of attributes (for example, enums), a list of supported values and corresponding display names for those values is provided.
  repeated string value_display_names = 5;
  repeated string values = 6;
}

message BingAttributeMetaDataList {
  repeated BingAttributeMetaData bing_attribute_metadata = 1;
}

message BingAttributeMetaData {
  // The name of the Bing Attribute in the form "attributes/is_owned_by_women"
  string parent = 1;
  ValueType value_type = 2;
  string display_name = 3;
  bool repeatable = 4;
  // For some types of attributes (for example, enums), a list of supported values and corresponding display names for those values is provided.
  repeated string value_display_names = 5;
  repeated string values = 6;
}

// Information about a Google "More Hours" type https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#morehourstype
message MoreHoursType {
  string hours_type_id = 1;
  string display_name = 2;
  string localized_display_name = 3;
}

// Update operation allows updating a specific piece of the listing profile
message UpdateOperation {
  oneof operation {
    Location nap = 1;
    ExternalIdentifiers external_identifiers = 2;
    SocialURLs social_urls = 4;
    HoursOfOperation hours_of_operation = 5; // Deprecated
    RichData rich_data = 6;
    BusinessHours business_hours = 7;
    GoogleAttributes google_attributes = 8;
    BingAttributes bing_attributes = 9;
    LegacyProductDetails legacy_product_details = 10;
    MarketingInfo marketing_info = 11;
  }
  vendastatypes.FieldMask field_mask = 20;
}

message CreateListingProfileRequest {
  Location nap = 1;
  // ID of the business location that the listing profile represents
  string business_location_id = 2;
  // The set of update operations to be done to the extended account group data after the create.
  repeated UpdateOperation update_operations = 3;
  // The set of create operations to be done to the extended account group on create
  repeated CreateOperation create_operations = 4;
}

message CreateListingProfileResponse {
  string business_id = 1;
}

// ProjectionFilter controls which sets of extended account group data is returned for each account group.
message ProjectionFilter {
  // Controls if external_identifiers are returned
  bool external_identifiers = 1;
  // Controls if social_urls is returned
  bool social_urls = 2;
  // Controls if hours_of_operation are returned
  bool hours_of_operation = 3; // Deprecated
  // Controls if rich_data is returned
  bool rich_data = 4;
  // Controls if nap data is returned
  bool nap_data = 5;
  // Controls if business hours are returned
  bool business_hours = 6;
  bool google_attributes = 7;
  bool google_attributes_metadata = 8;
  bool bing_attributes = 9;
  bool bing_attributes_metadata = 10;
  bool legacy_product_details = 11;
}

// ReadFilter can be used for filtering at the repository layer
message ReadFilter {
  // Whether or not to include deleted profiles on the read
  bool include_deleted = 1;
}

// MarketingInfo store marketing properties for an account group
message MarketingInfo {
  string conversion_point = 1;
  MarketingClassification marketing_classification = 2;
  LifecycleStage lifecycle_stage = 3;
}

enum MarketingClassification {
  MARKETING_CLASSIFICATION_UNUSED = 0;
  MARKETING_CLASSIFICATION_SMB = 1;
  MARKETING_CLASSIFICATION_SPAM = 2;
  MARKETING_CLASSIFICATION_DUPLICATE = 3;
  MARKETING_CLASSIFICATION_BLACKLISTED = 4;
  MARKETING_CLASSIFICATION_OUT_OF_MARKET = 5;
}

// Where an account is at in the lifecycle stage
// The sales domain has the same concept of a lifecycle stage. Updating this enum
// most likely means you need to update the one in sales as well.
enum LifecycleStage {
  LIFECYCLE_STAGE_UNSET = 0;
  LIFECYCLE_STAGE_LEAD = 1;
  LIFECYCLE_STAGE_PROSPECT = 2;
  LIFECYCLE_STAGE_CUSTOMER = 3;
}

// The full identifier for a marketplace app.
message AppKey {
  string app_id = 1;
  string edition_id = 2;
}

// The information needed to activate an app
message Activation {
  AppKey app_key = 1;
  bool is_trial = 2;
}

message ActivateAccounts {
  repeated Activation activations = 2;
}

// CreateOperation allows asynchronous operations to take place as a side effect of account creation
message CreateOperation {
  oneof operation {
    ActivateAccounts activate_accounts = 1;
    CreateCompetitors create_competitors = 2;
  }
}

message CompetitorLocation {
  string name = 1;
  string url = 2;
  // The Place ID for a place in the Google Places database and on Google Maps.
  // See https://developers.google.com/places/place-id for more about Place IDs.
  string place_id = 3;
}

message CreateCompetitors {
  repeated CompetitorLocation location = 1;
}



message GetMultiListingProfileRequest {
  repeated string business_ids = 1;
  ProjectionFilter projection_filter = 2;
  ReadFilter read_filter = 3;
  string language_code = 4;
}

message GetMultiListingProfileResponse {
  message ListingProfileContainer {
    ListingProfile listing_profile = 1;
  }
  repeated ListingProfileContainer listing_profiles = 1;
}

// BulkUpdateRequest allows updating all of the pieces of the account group and runs them in order.
message UpdateListingProfileRequest {
  string business_id = 1;
  repeated UpdateOperation update_operations = 2;
  // If unmodified since will fail any update is older than current updated time. Much like https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Unmodified-Since
  google.protobuf.Timestamp if_unmodified_since = 3;
  string language_code = 4;
}

message DeleteListingProfileRequest {
  string business_id = 1;
}

message GetMoreHoursTypesRequest {
  string business_id = 1;
  string language_code = 2;
}

message GetMoreHoursTypesResponse {
  repeated MoreHoursType more_hours_types = 1;
}

message GetAttributeMetadataRequest {
  string business_id = 1;
  // Optional Vendor ID if we only want to return a specific vendor's attributes
  string vendor_id = 2;
  string language_code = 3;
}

message GetAttributeMetadataResponse {
  repeated VendorAttributeMetaData attribute_medata = 1;
}

message LegacyProductDetails {
repeated string common_name = 1;
  string admin_notes = 2;
  repeated string competitor = 3;
  repeated string share_of_voice_service = 4;
}

message LegacyAPICreateRequest {
  string business_id = 1;
  string args = 2;
}

message LegacyAPIUpdateRequest {
  string args = 1;
}

message GetMultiAccountGroupRequest {
  repeated string business_ids = 1;
  ProjectionFilter projection_filter = 2;
  ReadFilter read_filter = 3;
  string language_code = 4;
}

message GetMultiAccountGroupResponse {
  repeated ConsolidatedDataContainer consolidated_data = 1;
}

message ConsolidatedDataContainer {
  string business_id = 1;
  ListingProfile listing_profile = 2;
  AccountGroupData account_group = 3;
  ActivationStatusData activation_status = 4;
  SEOAddonData seo_addons = 5;
  SEOSettingsData seo_settings = 6;
}

message AccountGroupData {
  string account_group_id = 1;
  Location nap_data = 2;
  ExternalIdentifiers external_identifiers = 3;
  SocialURLs social_urls = 4;
  RichData rich_data = 5;
}
enum AppAndAddonActivationStatus {
  NOT_SPECIFIED = 0;
  ACTIVATED = 1;
  PENDING = 2;
  CANCELED = 3;
  DEACTIVATED = 4;
}

message ActivationStatusData {
  repeated AppAndAddonActivationStatus activation_statuses = 1;
}

message SEOAddonData {
  repeated SEOAddonActivation addons = 1;
}

message SEOAddonActivation {
  enum SEOAddonActivationStatus {
    SEO_ADDON_ACTIVATION_STATUS_UNSPECIFIED = 0;
    SEO_ADDON_ACTIVATION_STATUS_ACTIVE = 1;
    SEO_ADDON_ACTIVATION_STATUS_CANCELLED = 2;
    SEO_ADDON_ACTIVATION_STATUS_DEACTIVATED = 3;
  }
  
  string business_id = 1;
  string app_id = 2;
  string addon_id = 3;
  string activation_id = 4;
  google.protobuf.Timestamp activated = 5;
  google.protobuf.Timestamp deactivated = 6;
  SEOAddonActivationStatus status = 7;
  bool is_suspended = 8;
  string activation_descriptor = 9;
}

message SEOSettingsData {
  double local_search_radius = 1;
  repeated string favorite_keywords = 2;
  bool is_full_search_enabled = 3;
}
message AuditRequest {
  string brand_name = 1;
  string website_url = 2;
  string business_id = 3;
}

message AuditResponse {
  string message = 1;
  string status = 2;
  string audit_id = 3;
  string business_id = 4;
}
