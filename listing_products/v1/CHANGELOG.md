# 1.151.0
- Add `TriggerAuditWorkflow` endpoint
# 1.150.0
- Add `GetMultiAccountGroup` endpoint
# 1.149.0
- Deprecate `FetchAndSubmitSourceURLsToCS` endpoint
# 1.148.0
- Add `ADDON_TYPE_YEXT_FOOD` enum
# 1.147.0
- Add `GetCitationSummary` endpoint
# 1.146.0
- Add `ListCitationDomains` endpoint
# 1.145.0
- Add `field_mask` to endpoints for fetching Listing Sources
# 1.144.0
- Add new endpoint `GetSEODataSummary` and `GetLocalSearchData` to replace `GetSEOData` to grpc memory limitations
# 1.143.0
- Add `SYNC_STATUS_SUBMITTED` for `GetSyncData` endpoint

# 1.142.0
- Add `YEXT_STANDALONE` addon type
# 1.141.0
- Add `GetNeustarPublictions` endpoint
# 1.140.0
- Add `SyncRequirement` enum to source configuration
# 1.139.0
- Adding new Endpoint `TriggerBingInsightsBackFill` to BackFill the bingInsights data during the initial successful sync
# 1.138.0
- Add specific fields to the `suggestion` message
# 1.137.0
- Add access check to `DeleteListingSource` and `UndeleteListingSource`
# 1.136.0
- Add `UndeleteListingSource` endpoint
# 1.135.0
- Add `GetSyncData` endpoint
# 1.134.0
- Add `hidden` field to `DirectSyncSource` message
# 1.133.0
- Add `last_scraped` timestamp to `SourceAccuracy` message
# 1.132.0
- Add `SetSuggestion` endpoint
# 1.131.4
- Update trigger field type in `SyncToSourcesRequest` message
# 1.131.3
- Add `SyncToSources` rpc for `SyncToSourcesRequest` message
# 1.131.2
- Add `is_listing_verified` param to DirectSyncSource
# 1.131.1
- Add `activation` time to LD endpoint
# 1.131.0
- Add `GetLDData` endpoint
# 1.130.1
- Clarify `hours_of_operation` field in `ListingProfile`
# 1.130.0
- Add `is_sync_keywords_disabled` scope to `UpsertPartnerSettingsRequest` , `GetPartnerSettingsResponse`
# 1.129.2
- Add `is_full_search_enabled` scope to `SaveSEOSettingsRequest` , `SEOSettingsResponse`
# 1.129.1
- Replace `business` and `business:read` scopes with `business-app` for `GetSEOData` rpc
# 1.129.0
- Add `business-app` scope to `Update`, `SaveSEOSettings`, `GetDirectSyncSourceInfo`
# 1.128.0
- Add `business-app` scope to `GetSEOSettings`, `GetActiveSEOAddons`, `GetOrGenerateSEOSuggestedKeywords`, `GetProductSettings`
# 1.127.1
- Deprecate `HoursOfOperation` message instead of removing
# 1.127.0
- Removed usage for `HoursOfOperation` message
# 1.126.0
- Remove unused and replaced `ListVendorListings` and `ListSocialServices` endpoints
# 1.125.0
- Add `is_syncing_enabled` flag to `DirectSyncSource`
# 1.124.0
- Add `business:read` and `business` scopes to `GetSEOData` rpc
# 1.123.0
- Add `additional_accounts` field to `DirectSyncSource`
# 1.122.0
- Add `GetDirectSyncSourceInfo` RPC to `ListingProductsService`
# 1.121.0
- Add `IsLocalSEOProActiveForAccount` RPC to `ListingProfileService`
# 1.120.0
- Add `GetBusinessProfileFieldStatus` RPC to `ListingProfileService`
# 1.119.0
- Add `GetDataForSEOCategory` RPC to `SEOService`
# 1.118.0
- Add `GetConfiguration` RPC to `PartnerSettingsService`
# 1.117.0
- Add `IsPartnerUser` endpoint
# 1.116.0
- Add `total_count` to `ListSuggestionResponse`
# 1.115.0
- Add `share_of_voice_service` to `legacy_product_details` field
# 1.114.0
- Add new value type for attributes
# 1.113.2
- Fix hasmore field type for suggestions
# 1.113.1
- Fix pagesize field type for suggestions
# 1.113.0
- Implement cursor-based pagination for suggestions
# 1.112.1
- Fix `ListSuggestionRequest` field(s)
# 1.112.0
- Add `List` Endpoint for suggestions
# 1.111.0
- Add `Delete` Endpoint for suggestions
# 1.110.0
- Add `GetBingPlacesInfo` endpoint
# 1.109.0
- Rename `GetMulti` request and response for suggestions
# 1.108.0
- Add `GetMulti` endpoint for suggestions
# 1.107
- Add `UpdateOrigin` to `ExternalIdentifiers`
# 1.106.0
- Add `business-app` scope to `GetMulti`, `GetMoreHoursTypes`, `GetAttributeMetadata`
# 1.105.0
- Rename `Date` to `GoogleDate` to fix typescript SDK generation
- Add `inferred_attributes`, `create_operations`, `competitor` and `marketing_info` to `ListingProfile`
# 1.104.0
- Add `syncing_seo_keywords` to `RichData` of the Listing Profile
- Add `StartSEOCategoryWorkflow` endpoint
- Add `StartBusinessDataScoreWorkflow` endpoint
# 1.103.0
- Add `LegacyProductDetails` to Listing Profile projection filter
# 1.102.0
- Add `LegacyProductDetails` to `ListingProfile`
- Add several account group fields to `ExternalIdentifiers`
# 1.101.0
- Add `LegacyAPICreate` and `LegacyAPIUpdate` endpoints to handle legacy Account Group calls
# 1.100.0
- Add `refresh` flag to `ListSocialServicesRequest`
# 1.99.0
- Add `GetAppleBusinessConnectInfo` endpoint
# 1.98.0
- Revert 1.97.0 as CS has to get all deleted sources
# 1.97.0
- Add `include_deleted` to GetListingSourcesRequest
# 1.96.0
- Add `FetchAndSubmitSourceURLsToCS` endpoint
# 1.95.0
- Remove `practice_name` to `HealthCareProfessionalInformation`
# 1.94.0
- Add new Addon Type `ADDON_TYPE_LSP_EXTENSION`
# 1.93.0
- Add new doctor.com fields to `HealthCareProfessionalInformation`
# 1.92.0
- Add `DoctorDotComCategoryRequest` to `ListingProfileService`
# 1.91.0
- Add `DoctorDotComCategory` to `HealthCareProfessionalInformation`
# 1.90.0
- Add `conditional_fields` to Rich Data of the Listing Profile
# 1.89.0
- Add `booking_url` to Rich Data of the Listing Profile
# 1.88.0
- Add `DoctorDotComCategory` proto, add `GetDoctorDotComCategoriesRequest` endpoint
# 1.87.0
- Add `ignore_data_lake_results` flag to SEO Workflow endpoint
# 1.86.0
- Add separate force flags to SEO Workflow endpoint
# 1.85.0
- Add ADDON_TYPE_KEYWORD_TRACKING to AddonType
# 1.84.0
- Add `is_favorite_new_keywords_disabled` to `PartnerSettings`
# 1.83.0
- Add `favorite_keywords` to `SEOSettings` endpoints
# 1.82.0
- Adding `GetSEOSuggestedKeywords` endpoint
# 1.81.0
- Add `DeleteCitations` endpoint to `Citations` service
# 1.80.0
- Changing `GetOrGenerateSEOSuggestedKeywords` response
# 1.79.0
- Added `language_code` to `GetMultiListingProfileRequest`
# 1.78.0
- Added `language_code` to `UpdateListingProfileRequest`
# 1.77.0
- Added `language_code` to `GetMoreHoursTypesRequest`
- Added `language_code` to `GetAttributeMetadataRequest`
# 1.76.0
- Add `force` flag to turbo lister request
# 1.75.0
- Add `GetOrGenerateSEOSuggestedKeywords` endpoint `SEOSuggestedKeywords`
# 1.74.0
- Added `language_code` to `SuggestionRequests`
# 1.73.0
- Added listing source addon type
# 1.72.0
- Add `GetCitationData` endpoint
# 1.71.0
- Add `GetProductSettings` endpoint
# 1.70.3
- Fix typing for `BingAttributeMetaDataList`
# 1.70.2
- Fix type name for `GoogleAttributeMetaData`
# 1.70.1
- Add repeated data type to `GoogleAttributeMetaData` and `BingAttributeMetaData`
# 1.70.0
- Rename `VendorAttributeMeta` to `VendorAttributeMetaData`
- Add `vendor_id` property to `VendorAttributeMetaData`
# 1.69.0
- Add `date` parameter to `StartLocalSEODataWorkflowRequest`
# 1.68.0
- Add `GetAttributeMedata` endpoint
- Remove `GetAttributes` endpoint
# 1.67.0
- Add `GetAttributes` endpoint
# 1.66.0
- Add `PartnerSettings` endpoints
# 1.65.0
- Add `BingAttributeMetadata` to `ProjectionFilter`
# 1.64.0
- Add `BingAttributeMetadata` to `ListingProfile`
# 1.63.0
- Add `workflow_url` to `SEOData`
# 1.62.0
- Add `BingAttributes` to listing profile
- Add `BingAttributes` to update endpoint
# 1.61.1
- Add business ID to UpdateVendorSyncFlag request
# 1.61.0
- Refactor `UpdateBingSyncFlag` endpoint to `UpdateVendorSyncFlag`
- Rename `ListDataListingsVendor` to `ListVendorListings`
- Adds `search_radius` field to `SEOData`
# 1.60.2
- Update `GetSEOAddonsResponse` to match response from accounts proto
# 1.60.1
- Fix typo in `UpdateBingSyncFlagRequest`
# 1.60.0
- Rename `SubmitBingLocations` to `UpdateBingSyncFlag` and pass only one business_id in the request
# 1.59.0
- Add `GetSEOAddons` endpint
# 1.58.1
- Remove `profile_url` from `ListDataListingsVendor` endpoint
# 1.58.0
- Add `ListDataListingsVendor` endpoint
# 1.57.0
- Add `previous_data` field to `GetSEOData` endpoint
# 1.56.0
- Add `GetFacebookPageInfoRequest` endpoint
# 1.55.0
- Added `GetSEOSettings` and `SaveSEOSettings` endpoint
# 1.54.0
- Added Google's Civil Time protos directly here instead of importing them so that MSCLI can find them
# 1.53.0
- Add `Citations` service and `StartCitationWorkflow` endpoint
# 1.52.0
- Add `GetMoreHoursTypes` endpoint
# 1.51.0
- Add `GetSyndicationInfo` endpoint
# 1.50.0
- Add `StartLocalSEODataWorkflow` endpoint to SEO service
- Add additional filtering parameters to `GetSEOData` endpoint
# 1.49.0
- Add `SEOService` RPC and `GetSEOData` endpoint to ListingProducts
- Add `seo_keywords` field to `ListingProfile`'s `RichData` object
# 1.48.0
- Add `SuggestionService` RPC to ListingProducts
# 1.47.0
- Add Listing ID to SocialService object
# 1.46.0
- Add Listing URL and Source ID to SocialService object
# 1.45.0
- Add Accuracy and score data to SocialService object
# 1.44.0
- Add `GetInfoGroupId` endpoint
# 1.43.0
- Add ListSocialServices endpoint
# 1.42.0
- Mark old Service Availability fields as deprecated
# 1.41.0
- Remove redundant `more_hours` field from `ListingProfile`
# 1.40.0
- Remove `is_gmb_verified`, `has_gmb_location`, `reconnect_link` and `social_token_broken` in `GetGoogleMyBusinessInsightsDataResponse` and `GetGoogleMyBusinessInsightsDataBucketedResponse`
# 1.39.0
- Add `social_token_broken` to `GetGoogleMyBusinessInsightsDataResponse` and `GetGoogleMyBusinessInsightsDataBucketedResponse`
# 1.38.0
- Add `is_gmb_verified`, `has_gmb_location`, `reconnect_link` to `GetGoogleMyBusinessInsightsDataResponse` and `GetGoogleMyBusinessInsightsDataBucketedResponse`
# 1.37.0
- Add `more_hours` and `more_hours_types` to listing profile
# 1.36.0
- Add admin scope to the `CreateListingSource` endpoint
# 1.35.0
- Add `ServiceArea` object to the `ListingProfile` `Location` object
# 1.34.0
- Add `business_email` to the `RichData` object of the `ListingProfile`
# 1.33.0
- Add `values` to GoogleAttributeMetaData
# 1.32.0
- Remove `ValueType` enum from `GoogleAttribute`. Leaving it outside the `GoogleAttributeMetaData` definition so it
  is easy to use in the frontend outside the sdk.
# 1.31.0
- Extract `ValueType` enum out of `GoogleAttributeMetaData` and use it in `GoogleAttribute` too
# 1.31.0
- Removed `List` from `ListingProfileService`
# 1.30.0
- Add `SocialUrls` to `ListingProfile`
- Remove `ContactDetails` as it is a legacy object
- Add fax number and cell number to rich data
# 1.29.2
- Add `GoogleAttributes`
# 1.29.1
- Actually add the metadata to the listing profile
# 1.29.0
- Add Google attributes metadata.
# 1.28.3
- Add `is_repeatable_value` to the `Attributes in ListingProfile`
# 1.28.2
- Add `Attributes` to the `ListingProfile`
# 1.28.1
- Also remove `SocialUrls` from `ProjectionFilter`
# 1.28.0
- Remove `SocialUrls` from `ListingProfile`
# 1.27.1
- Actually add the new `BusinessHours` to the `ListingProfile`
# 1.27.0
- Added new listing profile messages for `BusinessHours`. Includes both regular and special hours. Based on https://vendasta.jira.com/wiki/spaces/API/pages/**********/Hours+of+Operation+V2+-+API+data+model
- For now we will continue to use `HoursOfOperation` in favor of `RegularHours` which will be implemented after `SpecialHours` and More Hours.
# 1.26.3
- Align enums with the account group protos
# 1.26.2
- Remove market identifier from `ListingProfile`
# 1.26.1
- Rework the `ListingProfile` messages to only have one identifier
# 1.26.0
- Add `ListingProfile` rpcs and messages
# 1.25.0
- Modified `BulkConnectLocationsForGoogleUser` endpoint so that many account groups can be used to connect in one call.
# 1.24.0
- Modified `BulkConnectLocationsForGoogleUser` endpoint to only take the account group ID that will copy it's Google user to the other accounts on the PID that need the Google user
# 1.23.0
- Add `BulkConnectLocationsForGoogleUser` endpoint to connect all Google Business Profile locations on a Google user to account groups/social profiles
# 1.22.0
- Add `GetListingDistributionActivationStatus` endpoint
# 1.21.0
- Add `CheckSubmissionStatus` endpoint
# 1.20.0
- Add `GetGoogleBusinessInfo` endpoint for human SoD usage only
# 1.19.0
- Add admin scope to the `UpdateListingSource` endpoint
# 1.18.0
- Add `ManuallyProvisionUberall` endpoint
# 1.17.0
- Add `ManuallyDeactivateUberallProvisioning` endpoint
# 1.16.0
- Add `ManuallyFixYextProvisioning` endpoint
# 1.15.0
- Add `GetMultiAddonAttributes` endpoint
# 1.14.1
- Fix enum typo
# 1.14.0
- Add `ADDON_TYPE_BRING_YOUR_OWN_YEXT` addon type for Bring Your Own Yext addons
# 1.13.1
- Add `ADDON_TYPE_YEXT_HEALTHCARE` addon type for Yext Healthcare addons
# 1.13.0
- Remove `addon_id` from `AddonAttributes`
- Add `ADDON_TYPE_UNSPECIFIED` to `AddonType`
- Add `fieldMask` to `UpdateAddonAttributesRequest`
# 1.12.1
- Add `AddonAttributes` message to proto and update `CreateAddonAttributesRequest`, `UpdateAddonAttributesRequest` and
- `GetAddonAttributes` to use `AddonAttributes`
# 1.12.0
- Add `GetAddonAttributes`, `CreateAddonAttributes`, `UpdateAddonAttributes`,
and `DeleteAddonAttributes` endpoints
# 1.11.1
- Fix scope issue by moving request and response into api.proto
# 1.11.0
- Add admin scope to the `GetListingSources` endpoint
# 1.10.1
- Add admin scope to the `GetListingSourceById` endpoint
# 1.10.0
- Add `available_for_service_area_business` which indicates if a listings source supports listings for service area businesses
# 1.9.0
- Remove Data-axle reporting functions
# 1.8.1
- Add flag indicating if a source has presence and accuracy reporting, `has_listing_reporting`
# 1.8.0
- no changes, version incremented by mistake
# 1.7.4
- Add flags indicating if a source is:
  1. being submitted listings directly from Vendasta
  2. part of Listing Distribution
# 1.7.3
- Add `GetListingAccuracy`
# 1.7.2
- Add comments to `GetPartnerSourcesRequest`
# 1.7.1
- Add `market_id` to `GetPartnerSourcesRequest`
# 1.7.0
- Remove all bulk action endpoints. These no longer work with the new activations workflow
and are not needed by support since they have endpoints in `accounts` to do activations/deactivations.
# 1.6.2
- Remove `FoursquareSubmitVenues` endpoint, Foursquare now uses SFTP file transfers
# 1.6.1
- Update `SubmitTurboLister` endpoint to use vendor_id instead of vendor_name
# 1.6.0
- Add `SubmitTurboLister` endpoint
# 1.5.0
- Add `BulkDeactivateAddon` endpoint
# 1.4.0
- Rename `CreateBingLocations` endpoint to `SubmitBingLocations`
    - rename request to `SubmitBingLocationsRequest`
    - rename response to `SubmitBingLocationsResponse`
# 1.3.0
- Add `CreateBingLocations` endpoint
# 1.2.0
- Change field names of `GetGoogleMyBusinessInsightsDataBucketedResponse`
    - actions_driving_directions_sum is now actions_driving_directions
    - actions_phone_sum is now actions_phone
- `InsightBucket.bucket_label` is now just a string instead of a oneof property
# 1.1.0
- Change `GetGoogleMyBusinessInsightsDataBucketed` response.
    - Timestamps for the buckets don't make much sense.
        - Is bucket #1's timestamp the most recent Monday in the response?
        - Is bucket #4's timestamp the oldest Thursday?
    - They are now labels for the buckets.
        - e.g. Monday, Tuesday...
# 1.0.0
- Add GetGoogleMyBusinessInsightsData, GetGoogleMyBusinessInsightsDataBucketed endpoints
# 0.0.0
 - Empty proto
