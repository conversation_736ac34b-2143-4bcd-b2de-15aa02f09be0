syntax = "proto3";

package listing_products.v1;

option go_package = "github.com/vendasta/generated-protos-go/listing_products/v1;listing_products_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.listingproducts.v1.generated";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "listing_products/v1/addon_attributes.proto";
import "vendasta_types/annotations.proto";
import "listing_products/v1/insights.proto";
import "listing_products/v1/turbolister.proto";
import "listing_products/v1/listing_sources.proto";
import "listing_products/v1/listing_profile.proto";
import "listing_products/v1/suggestions.proto";
import "listing_products/v1/seo.proto";
import "listing_products/v1/citations.proto";
import "listing_products/v1/partnersettings.proto";
import "listing_products/v1/seosuggestedkeywords.proto";
import "listing_products/v1/syncdata.proto";

message ManuallyFixYextProvisioningRequest {
    // business ID (aka account group ID)
    string business_id = 1;
    string addon_id = 2;
    string activation_id = 3;
}

message ManuallyDeactivateUberallProvisioningRequest {
  // business ID (aka account group ID)
  string business_id = 1;
}

message ManuallyProvisionUberallRequest {
  // business ID (aka account group ID)
  string business_id = 1;
  string addon_id = 2;
  string activation_id = 3;
}

message GetGoogleBusinessInfoRequest {
  // business ID (aka account group ID)
  string business_id = 1;
}

message GetGoogleBusinessInfoResponse {
  // Dump the json response we get from Google into this field. It will contain all the data we request from Google.
  string text_dump = 1;
}

message GetFacebookPageInfoRequest {
  // business ID (aka account group ID)
  string business_id = 1;
}

message GetFacebookPageInfoResponse {
  // Dump the json response we get from Facebook into this field. It will contain all the data we request from Facebook.
  string text_dump = 1;
}

message GetAppleBusinessConnectInfoRequest {
  // business ID
  string business_id = 1;
}

message GetAppleBusinessConnectInfoResponse {
  // Dump the json response we get from Apple into this field. It will contain all the data we request from Apple.
  string text_dump = 1;
}

message GetBingPlacesInfoRequest {
  // business ID
  string business_id = 1;
}

message GetBingPlacesInfoResponse {
  // Dump the json response we get from Bing into this field. It will contain all the data we request from Bing.
  string text_dump = 1;
}

message TriggerBingInsightsBackFillRequest{
  string business_id = 1;
  bool first_successful_sync = 2;
  string start_date = 3;
  string end_date = 4;
}

message CheckSubmissionStatusRequest {
  string business_id = 1;
}

message GetInfoGroupIdRequest {
  string business_id = 1;
}

message GetInfoGroupIdResponse {
  string infogroup_id = 1;
}

message GetListingDistributionActivationStatusRequest {
  string account_group_id = 1;
}

message GetListingDistributionActivationStatusResponse {
  // The account to retrieve LD activation status for
  string account_group_id = 1;
  // Whether LD is active or not
  bool is_active = 2;
  // The LD addon that is currently active for the account
  string addon_id = 3;
  // The date LD was activated
  google.protobuf.Timestamp activation = 4;
}

message BulkConnectLocationsForGoogleUserRequest {
  // In order for this endpoint to work the Google user must already have been used to connect an account in our system. This AG ID is then
  // used to get the google user ID and google user from the social profile in order to copy that to the other businesses, and then connect
  // a location to those businesses.
  repeated string business_id = 1;
}

message BulkConnectLocationsForGoogleUserResponse {
  // connections is a map of the Google Business Profile locations that were connected, to the account group they were connected to.
  map<string,string> connections  = 1;
  // locations_not_connected is a list of the GBP locations on the input Google user that were not connected to a social profile.
  string locations_not_connected = 2;
}

message GetProductSettingsRequest {
  string partner_id = 1;
  string market_id = 2;
}

message GetProductSettingsResponse {
  string partner_id = 1;
  string market_id = 2;
  bool can_edit_keywords = 3;
}

message GetDirectSyncSourceInfoRequest {
  string business_id = 1;
  // if set to true, connected account credentials will be refreshed
  bool refresh = 2;
}

message DirectSyncSource {
  // numeric ID of the source
  int64 source_id = 1;
  // The name of the source
  string name = 2;
  // The connected account information for this direct submit source
  ConnectedDirectSyncAccount account = 3;
  string connect_link = 4;
  // The accuracy data for this direct submit source
  SourceAccuracy accuracy = 5;
  // The current listing score for this direct submit source
  int64 score = 6;
  // The maximum possible listing score for this direct submit source
  int64 max_score = 7;
  // The URL of the listing for this direct submit source
  string listing_url = 8;
  // The CS listing id of the direct submit listing
  string listing_id = 9;
  // The syndication status for this direct submit source
  SyndicationStatus sync_status = 10;
  // Other accounts that are connected for this source, and could be synced to
  repeated ConnectedDirectSyncAccount additional_accounts = 11;
  // Whether syncing to this source is enabled.  Usually this will correspond to the account's is_syncing_enabled flag,
  // but for sources without accounts, it will be populated by the models where we store direct sync setting for the business.
  bool is_syncing_enabled = 12;
  //Whether the listing has been verified as accurate by the business
  bool is_listing_verified = 13;
  // Whether the source is hidden from the UI
  bool hidden = 14;
}

message GetDirectSyncSourceInfoResponse {
  repeated DirectSyncSource direct_submit_sources = 1;
}

message StartBusinessDataScoreWorkflowRequest {
  repeated string business_ids = 1;
}


message IsPartnerUserRequest {
  string partner_id = 1;
}
message IsPartnerUserResponse {
  bool is_partner_user = 1;
}

message GetLDDataRequest {
  string business_id = 1;
}

message GetLDDataResponse {
  DirectSyncSource neustar = 1;
  DirectSyncSource data_axle = 2;
  DirectSyncSource foursquare = 3;
}

message SyncToSourcesRequest {
  string business_id = 1;
  string trigger = 2;
}

message GetNeustarPublicationsRequest {
  string business_id = 1;
}

message NeustarPublication {
  int64 id = 1;
  int64 parent_id = 2;
  string platform = 3;
  string description = 4;
  string message = 5;
  google.protobuf.Timestamp last_delivery_time = 6;
  google.protobuf.Timestamp last_modified = 7;
  string disposition = 8;
  string link = 9;
}

message GetNeustarPublicationsResponse {
  repeated NeustarPublication publications = 1;
}

service ListingProductsService {
  // GetGoogleMyBusinessInsightsData gets Google My Business insights for a business
  rpc GetGoogleMyBusinessInsightsData(GetGoogleMyBusinessInsightsDataRequest) returns (GetGoogleMyBusinessInsightsDataResponse);
  // GetGoogleMyBusinessInsightsDataBucketed gets bucketed Google My Business insights for a business
  rpc GetGoogleMyBusinessInsightsDataBucketed(GetGoogleMyBusinessInsightsDataBucketedRequest) returns (GetGoogleMyBusinessInsightsDataBucketedResponse);

  // UpdateVendorSyncFlag sets the syncing property for a Vendor such as Bing or Apple.
  rpc UpdateVendorSyncFlag(UpdateVendorSyncFlagRequest) returns (google.protobuf.Empty);

  // SubmitTurboLister initiates a workflow to submit listing data for a specified vendor
  // This endpoint is async and returns success when the task has been added to the Cadence workflow list.
  rpc SubmitTurboLister(SubmitTurboListerRequest) returns (SubmitTurboListerResponse);

  // GetAddonAttributes fetches Addon attributes using the AddonID in request
  rpc GetAddonAttributes(GetAddonAttributesRequest) returns (GetAddonAttributesResponse);
  // CreateAddonAttributes creates new record for Addon attributes
  rpc CreateAddonAttributes(CreateAddonAttributesRequest) returns (google.protobuf.Empty);
  // UpdateAddonAttributes updates existing record for Addon attributes
  rpc UpdateAddonAttributes(UpdateAddonAttributesRequest) returns (google.protobuf.Empty);
  // DeleteAddonAttributes deletes existing record for Addon attributes
  rpc DeleteAddonAttributes(DeleteAddonAttributesRequest) returns (google.protobuf.Empty);
  // GetMultiAddonAttributes gets a list of Addon attributes based on included filters
  rpc GetMultiAddonAttributes(GetMultiAddonAttributesRequest) returns (GetMultiAddonAttributesResponse);
  // BulkConnectLocationsForGoogleUser will take all the Google Business Profile locations for the given Google user and attempt
  // to connect them to account groups in the given partner. It will return a list of the connections made and the list of
  // GBP locations that could not be connected.
  rpc BulkConnectLocationsForGoogleUser(BulkConnectLocationsForGoogleUserRequest) returns (BulkConnectLocationsForGoogleUserResponse);

  // Manual* IF YOU USE THESE ENDPOINTS IN CODE YOU WILL BREAK EVERYTHING AND LIKELY DIE
  // ManuallyFixYextProvisioning bypasses all of our Marketplace duplicate activation checks and can make things much much worse.
  // ManuallyFixYextProvisioning is a one off to fix a broken system state. Interactions with Yext should be done in the
  //    Yext microservice.
  //  This will look up the plan ID for the activation and pass the account to the Yext microservice to try to provision it
  //    again since the automated process is broken. Please try to fix the bug that resulted in the account getting into this
  //    state in addition to running this endpoint.
  rpc ManuallyFixYextProvisioning(ManuallyFixYextProvisioningRequest) returns (google.protobuf.Empty);
  // ManuallyProvisionUberall bypasses all of our Marketplace duplicate activation checks and can make things much much worse.
  //    This will try to provision the account again since the automated process is broken. Please try to fix the bug that
  //    resulted in the account getting into this state in addition to running this endpoint.
  rpc ManuallyProvisionUberall(ManuallyProvisionUberallRequest) returns (google.protobuf.Empty);
  // ManuallyDeactivateUberallProvisioning bypasses all standard operations regarding Uberall location cancelling and deactivation.
  //    It bypasses all Marketplace checks and immediately ends all service on an Uberall location.
  rpc ManuallyDeactivateUberallProvisioning(ManuallyDeactivateUberallProvisioningRequest) returns (google.protobuf.Empty);

  // GetGoogleBusinessInfo is used for human Support on Demand interaction only. Google is very strict about how their data is used
  //    and it should only be pulled when a user or agent working on behalf of the user has taken action to request it.
  //    This endpoint pulls the business information directly from Google's API using the tokens stored in Core Services.
  rpc GetGoogleBusinessInfo(GetGoogleBusinessInfoRequest) returns (GetGoogleBusinessInfoResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc GetFacebookPageInfo(GetFacebookPageInfoRequest) returns (GetFacebookPageInfoResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc GetAppleBusinessConnectInfo(GetAppleBusinessConnectInfoRequest) returns (GetAppleBusinessConnectInfoResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc GetBingPlacesInfo(GetBingPlacesInfoRequest) returns (GetBingPlacesInfoResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc TriggerBingInsightsBackFill(TriggerBingInsightsBackFillRequest) returns (google.protobuf.Empty);


  // CheckSubmissionStatus takes in business_id in request and using this business_id we fetch the latest data-axle submission_id for that business(account) from vtsore.
  // The submission_id obtained is then used to fetch the latest submission status from data-axle.
  rpc CheckSubmissionStatus(CheckSubmissionStatusRequest) returns (google.protobuf.Empty);

  // GetInfoGroupId uses business_id and returns InfoGroupID from DataAxleLocation model if available
  rpc GetInfoGroupId(GetInfoGroupIdRequest) returns (GetInfoGroupIdResponse);

  // GetListingDistributionActivationStatus gets the status of Listing Distribution for the account provided in the request
  rpc GetListingDistributionActivationStatus(GetListingDistributionActivationStatusRequest) returns (GetListingDistributionActivationStatusResponse);

  // GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business
  rpc GetDirectSyncSourceInfo(GetDirectSyncSourceInfoRequest) returns (GetDirectSyncSourceInfoResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };

  // GetDirectSyncSourceInfo returns information about the directly submitted sources for a given business
  rpc GetSyncData(GetSyncDataRequest) returns (GetSyncDataResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };

  // GetSyndicationInfo returns information about the last syndication workflows performed for a given business
  rpc GetSyndicationInfo(GetSyndicationInfoRequest) returns (GetSyndicationInfoResponse);

  // GetProductSettings returns information about the partner settings and checks permissions for a given partnerID / marketID
  rpc GetProductSettings(GetProductSettingsRequest) returns (GetProductSettingsResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };

  // GetDoctorDotComCategories returns the list of categories for Doctor.com
  rpc GetDoctorDotComCategories(GetDoctorDotComCategoriesRequest) returns (GetDoctorDotComCategoriesResponse);

  // Deprecated: no more usage of this.
  rpc FetchAndSubmitSourceURLsToCS(FetchAndSubmitSourceURLsToCSRequest) returns (google.protobuf.Empty);

  // StartBusinessDataScoreWorkflow starts a workflow to calculate the business data score for one or more businesses
  rpc StartBusinessDataScoreWorkflow(StartBusinessDataScoreWorkflowRequest) returns (google.protobuf.Empty);

  // TriggerAuditWorkflow initiates an audit workflow for a business with the specified brand name and website URL
  rpc TriggerAuditWorkflow(AuditRequest) returns (AuditResponse);

  // IsPartnerUser checks if the user making the request is a partner
  rpc IsPartnerUser(IsPartnerUserRequest) returns (IsPartnerUserResponse);

  // IsLocalSEOProActiveForAccount checks if the account has LSEO Pro active
  rpc IsLocalSEOProActiveForAccount(IsLocalSEOProActiveForAccountRequest) returns (IsLocalSEOProActiveForAccountResponse);

  // GetLDData returns the DirectSyncSource information for Neustar, Data Axle, and Foursquare
  rpc GetLDData(GetLDDataRequest) returns (GetLDDataResponse);

  rpc SyncToSources(SyncToSourcesRequest) returns (google.protobuf.Empty);

  rpc GetNeustarPublications(GetNeustarPublicationsRequest) returns (GetNeustarPublicationsResponse);
}

message GetDoctorDotComCategoriesRequest {}

message GetDoctorDotComCategoriesResponse {
  repeated DoctorDotComCategory categories = 1;
}

message GetBusinessProfileFieldStatusRequest {
  // The business id
  string business_id = 1;
}

message FieldStatus {
  string update_origin = 1;
  google.protobuf.Timestamp updated = 2;
}

message GetBusinessProfileFieldStatusResponse {
  string business_id = 1;
  FieldStatus company_name = 2;
  FieldStatus address = 3;
  FieldStatus address2 = 4;
  FieldStatus city = 5;
  FieldStatus state = 6;
  FieldStatus zip = 7;
  FieldStatus country = 8;
  FieldStatus work_number = 9;
  FieldStatus call_tracking_number = 10;
  FieldStatus website = 11;
  FieldStatus location = 12;
  FieldStatus sales_person_id = 13;
  FieldStatus additional_sales_person_id = 14;
  FieldStatus vcategory_ids = 15;
  FieldStatus customer_identifier = 16;
  FieldStatus lifecycle_stage = 17;
  google.protobuf.Timestamp created = 18;
  google.protobuf.Timestamp updated = 19;
  google.protobuf.Timestamp deleted = 20;
}

message UpdateVendorSyncFlagRequest {
  string business_id = 1;
  string vendor_id = 2;
  bool syncing = 3;
}

message IsLocalSEOProActiveForAccountRequest {
  string account_group_id = 1;
}

message IsLocalSEOProActiveForAccountResponse {
  string account_group_id = 1;
  bool is_active = 2;
  string edition_id = 3;
}

service ListingSourceService {
  // GetListingSources fetches all listing sources, optionally filtered by provider
  rpc GetListingSources(GetListingSourcesRequest) returns (GetListingSourcesResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // GetListingSourceById fetches details for a source based on its numeric ID
  rpc GetListingSourceById(GetListingSourceByIdRequest) returns (GetListingSourceByIdResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // GetListingSourceByProviderId fetches details for a source based on its provider ID
  rpc GetListingSourceByProviderId(GetListingSourceByProviderIdRequest) returns (GetListingSourceByProviderIdResponse);
  // GetPartnerSources gets all sources that have not been disabled by the partner
  rpc GetPartnerSources(GetPartnerSourcesRequest) returns (GetPartnerSourcesResponse);
  // CreateListingSource creates a new source
  rpc CreateListingSource(CreateListingSourceRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // DeleteListingSource sets a source to deleted
  rpc DeleteListingSource(DeleteListingSourceRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // UndeleteListingSource restores a deleted source
  rpc UndeleteListingSource(UndeleteListingSourceRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // UpdateListingSource updates fields of a source
  rpc UpdateListingSource(UpdateListingSourceRequest) returns (UpdateListingSourceResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
}

// Listing Profile Service
service ListingProfileService {
  rpc Create(CreateListingProfileRequest) returns (CreateListingProfileResponse);
  rpc GetMulti(GetMultiListingProfileRequest) returns (GetMultiListingProfileResponse){
    option (vendastatypes.access) = {
        scope: "business-app",
      };
  };
  rpc Update(UpdateListingProfileRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc Delete(DeleteListingProfileRequest) returns (google.protobuf.Empty);
  rpc GetMoreHoursTypes(GetMoreHoursTypesRequest) returns (GetMoreHoursTypesResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc GetAttributeMetadata(GetAttributeMetadataRequest) returns (GetAttributeMetadataResponse){
    option (vendastatypes.access) = {
    scope: "business-app",
    };
  };
  rpc GetDoctorDotComCategories(GetDoctorDotComCategoriesRequest) returns (GetDoctorDotComCategoriesResponse);
  rpc LegacyAPICreate(LegacyAPICreateRequest) returns (google.protobuf.Empty);
  rpc LegacyAPIUpdate(LegacyAPIUpdateRequest) returns (google.protobuf.Empty);
  rpc GetBusinessProfileFieldStatus(GetBusinessProfileFieldStatusRequest) returns (GetBusinessProfileFieldStatusResponse);
  rpc GetMultiAccountGroup(GetMultiAccountGroupRequest) returns (GetMultiAccountGroupResponse);
}

service SuggestionService {
  rpc Upsert(UpsertSuggestionRequest) returns (google.protobuf.Empty);
  rpc GetSuggestion(GetSuggestionRequest) returns (GetSuggestionResponse);
  rpc GetMulti(GetMultiSuggestionRequest) returns (GetMultiSuggestionResponse);
  rpc Delete(DeleteSuggestionRequest) returns (google.protobuf.Empty);
  rpc List(ListSuggestionRequest) returns (ListSuggestionResponse);
  rpc SuggestFieldUpdate(SuggestFieldUpdateRequest) returns (SuggestFieldUpdateResponse);
}

// SEO service
service SEOService {
  // Deprecated: use SEODataSummary & LocalSearchData
  rpc GetSEOData(GetSEODataRequest) returns (GetSEODataResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  //Get SEO summary data except Vicinity Data for keywords
  rpc GetSEODataSummary(GetSEODataSummaryRequest) returns (GetSEODataSummaryResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  //Get Vicinity Data for keywords
  rpc GetLocalSearchSEOData(GetLocalSearchSEODataRequest) returns (GetLocalSearchSEODataResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc StartLocalSEODataWorkflow(StartLocalSEODataWorkflowRequest) returns (google.protobuf.Empty);
  rpc SaveSEOSettings(SaveSEOSettingsRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc GetSEOSettings(GetSEOSettingsRequest) returns (SEOSettingsResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc GetActiveSEOAddons(GetActiveSEOAddonsRequest) returns (GetActiveSEOAddonsResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc StartSEOCategoryWorkflow(StartSEOCategoryWorkflowRequest) returns (google.protobuf.Empty);
  rpc GetDataForSEOCategory(GetDataForSEOCategoryRequest) returns (GetDataForSEOCategoryResponse);
}

// Citation service
service Citations {
  rpc StartCitationWorkflow(StartCitationWorkflowRequest) returns (google.protobuf.Empty);
  rpc GetCitationData(GetCitationDataRequest) returns (GetCitationDataResponse);
  rpc DeleteCitations(DeleteCitationsRequest) returns (google.protobuf.Empty);
  rpc GetCitationSummary(GetCitationSummaryRequest) returns (GetCitationSummaryResponse);
  rpc ListCitationDomains(ListCitationDomainsRequest) returns (ListCitationDomainsResponse);
}

service PartnerSettingsService {
  // CreatePartnerSettings will create a new PartnerSettings
  rpc CreatePartnerSettings(CreatePartnerSettingsRequest) returns (google.protobuf.Empty);

  // GetPartnerSettings returns information about the partner settings for a given partnerID / marketID
  rpc GetPartnerSettings(GetPartnerSettingsRequest) returns (GetPartnerSettingsResponse);

  // UpsertPartnerSettings will create a new PartnerSettings if it does not exist yet, or update the existing PartnerSettings if it does.
  rpc UpsertPartnerSettings(UpsertPartnerSettingsRequest) returns (google.protobuf.Empty);

  // GetConfiguration returns the configuration for a given partner
  rpc GetConfiguration(GetConfigurationRequest) returns (GetConfigurationResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
}

service SEOSuggestedKeywordsService {
  // GetOrGenerateSEOSuggestedKeywords will get or generate SEO suggested keywords for a given business
  rpc GetOrGenerateSEOSuggestedKeywords(GetOrGenerateSEOSuggestedKeywordsRequest) returns (GetOrGenerateSEOSuggestedKeywordsResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };

  // GetSEOSuggestedKeywords returns SEO suggested keywords for a given business
  rpc GetSEOSuggestedKeywords(GetSEOSuggestedKeywordsRequest) returns (GetSEOSuggestedKeywordsResponse);
}

