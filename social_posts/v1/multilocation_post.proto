syntax = "proto3";

package socialposts.v1;

option go_package = "github.com/vendasta/generated-protos-go/social-posts/v1;socialposts_v1";
option java_outer_classname = "MultilocationPostProto";
option java_package = "com.vendasta.socialposts.v1.generated";

import "google/protobuf/timestamp.proto";
import "social_posts/v1/social_posts.proto";

enum RemoveReason {
  // Used if the post is edited.
  REMOVE_REASON_EDIT = 0;

  // Used if the post is deleted.
  REMOVE_REASON_DELETE = 1;
}

message Location {
  // List of SSID for the location
  repeated string social_service_ids = 1;

  // AGID for the location
  string account_group_id = 2;
}

// Contains extra configurations of a post, like events and actions
message PostCustomization {
  // Event associated with this post. For example, a GMB event that should be created with the post
  PostEvent event = 1;
  // Actions that can be triggered within the posts when the network allows CTA
  PostAction action = 2;
}

// Represents the action associated with the post when the network allows CTA
message PostAction {
  // Represents the type of the CTA
  string type = 1;
  // The link where the CTA will lead to
  string link_url = 2;
}

// Represents an event that should be associated/created with a post
message PostEvent {
  // Title of the event related to the post
  string title = 1;
  // The timestamp when the event will start
  google.protobuf.Timestamp start = 2;
  // The timestamp when the event will end
  google.protobuf.Timestamp end = 3;
}

message MultilocationPost {
  // BrandID for the post
  string brand_id = 1;

  // MultilocationID
  string multilocation_id = 2;

  // Original post text
  string original_text = 3;

  // Date that the original post was scheduled to go
  google.protobuf.Timestamp original_scheduled_date = 4;

  // Original media links (images only)
  repeated string original_media = 5;

  // List of locations in the original post
  repeated Location original_locations = 6;

  // List of edited(in SL) locations
  repeated Location edited_locations = 7;

  // List of deleted(in SL) locations
  repeated Location deleted_locations = 8;

  // Original gif links
  repeated string original_gifs = 9;

  // Original video links
  repeated string original_videos = 10;

  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 11;

  // Metadata about the post, could be anything
  repeated MetaData meta_data = 12;

  // Tags required to differentiate posts
  repeated string tags = 13;

  // Extra specific configurations for the post
  PostCustomization customization = 14;

  //The short code of the link in this post.
  string link_short_code = 15;

  // The PostType for each of the SSIDs associated with this post
  repeated SSIDPostType post_types = 16;

  // post_category is used to differentiate between draft and hidden draft posts
  MLPostCategory post_category = 17;

  // Current state of the post: draft, hidden, scheduled, or published.
  MLPostState post_state = 18;

}

message MultilocationPostError {
  // SSID for which the error happened
  string social_service_id = 1;

  // AGID for which the error happened
  string account_group_id = 2;

  // Actual error message
  string error_reason = 3;
}

//This message for create BulkUpload in Multilocation
message BulkUploadMultilocation{
    // Original post text
    string text = 1;

    // Original media links
    repeated string media = 2;

    // Date that the original post was scheduled to go
    google.protobuf.Timestamp scheduled_date = 3;

    // Original gif links
    repeated string gifs = 4;

    // Original video links
    repeated string videos = 5;

    // Array of media content when uploading multiple media
    repeated MediaEntry media_entries = 6;

    // Metadata about the post, could be anything
    repeated MetaData meta_data = 7;

    // Tags required to differentiate posts
    repeated string tags = 8;

    // Extra specific configurations for the post
    PostCustomization customization = 9;

    //The short code of the link in this post.
    string link_short_code = 10;

    // The type of post for each SSID e.g., Image/video/reel/gif etc
    repeated SSIDPostType post_types = 11;

    // Multilocation ID is the key for bulk upload post in multilocation
    string multilocation_id = 12;
  // Locations for each uploaded post
    repeated Location locations = 13;
    // post_category is used to differentiate between draft and Scheduled Posts
    MLPostCategory post_category = 14;
}

message MultilocationServices{
    //The Array of service_ids hold the Ml service Id's for a ag-id
    repeated string service_ids =1;

    //Business Id or Account group Id
    string account_group_id = 2;
}
