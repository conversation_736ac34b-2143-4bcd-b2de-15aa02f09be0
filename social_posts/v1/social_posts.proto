syntax = "proto3";

package socialposts.v1;

option go_package = "github.com/vendasta/generated-protos-go/social-posts/v1;socialposts_v1";
option java_outer_classname = "SocialPostsProto";
option java_package = "com.vendasta.socialposts.v1.generated";

import "google/protobuf/timestamp.proto";

enum PostingStatus {
  POSTING_IN_PROGRESS = 0;
  POSTING_FAILED = 1;
  POSTING_COMPLETED = 2;
}

enum MessageLength {
  SHORT_FORM = 0;
  LONG_FORM = 1;
}

enum TemplateType {
  TEMPLATE_UNSET = 0;
  TEMPLATE_CUSTOM = 1;
  TEMPLATE_SOCIAL_POST = 2;
}

enum PostType {
  POST_TYPE_INVALID = 0;
  POST_TYPE_IMAGE = 1;
  POST_TYPE_VIDEO = 2;
  POST_TYPE_GIF = 3;
  POST_TYPE_REEL = 4;
  POST_TYPE_CAROUSEL = 5;
  POST_TYPE_STORIES = 6;
  POST_TYPE_TEXT = 7;
}

// Metadata message type for saving properties in instagram µservice
message MetaData {
  string property_name = 1;
  string property_value = 2;
}

// SSIDPostType represents the post type for each social service id
message SSIDPostType {
  string social_service_id = 1;
  PostType post_type = 2;
}

message MediaEntry {
  string media_entry_id = 1;
  string media_url = 2;
  string media_type = 3;
  string container_id = 4;
  repeated MetaData meta_data = 5;
  string media_path = 6;
}

message SocialCampaign {
  // The name of the campaign
  string name = 1;
  // Unique ID of the campaign
  string id = 2;
}

// The information about a social post. It does not necessarily need to have
// been posted yet
message SocialPost {
  // The unique identifier of the business this post is related to
  string business_id = 1;
  string social_post_id = 2;
  string post_text = 3;
  // The date time that this post was posted to it's corresponding social
  // account
  google.protobuf.Timestamp posted = 4;
  bool is_error = 5;
  enum DeletionStatus {
    NONE = 0;
    FAILED = 1;
    IN_PROGRESS = 2;
  }
  DeletionStatus deletion_status = 6;
  enum Service {
    TWITTER = 0;
    FACEBOOK = 1;
    LINKED_IN = 2;
    GOOGLE_PLUS = 3;
    GOOGLE_MY_BUSINESS = 4;
    UNKNOWN = 5;
    INSTAGRAM = 6;
    CURATED_CONTENT = 7;
    YOUTUBE = 8;
    TIKTOK = 9;
    WORDPRESS = 10;
  }
  Service service = 7;
  // The link to the post on the social page (e.g. an actual link to Facebook)
  string permalink = 8;
  // The date time that this entity was created in our system (not the time that
  // it was posted)
  google.protobuf.Timestamp created = 9;
  string profile_url = 10;
  string profile_image_url = 11;
  // The date time that this post is scheduled to be posted. If it's in the past
  // that means it we have or are attempting to post this You can see if it was
  // successful based on the status
  google.protobuf.Timestamp scheduled = 12;
  // The status of attempting to post this social post to the social account
  PostingStatus status = 13;
  // The image posted
  string image_url = 14;
  // The name of the social account (may not always be populated)
  string name = 15;
  // The username of the social account (may not always be populated)
  string username = 16;
  // The correlation id for posts that have been posted as a group
  string parent_id = 17;
  // The id of the social service this post was created with
  string social_service_id = 18;
  // The event for a Google My Business post
  Event event = 19;
  // The call to action for a Google My Busines post
  CallToAction call_to_action = 20;
  // The video posted
  string video_url = 21;
  // The reason why there was an error scheduling the post. Error will be nil if
  // it was successful
  Error error = 22;
  // A list of images for this post
  repeated string image_urls = 23;
  // An image that appears as a preview to a URL link.
  string link_preview_image_url = 24;
  // A unique identifer for a brand (if this is a multilocation post)
  string brand_id = 25;
  // A unique identifer if this is a multilocation post
  string multilocation_post_id = 26;
  // The type of post e.g., Image/video/reel/gif etc
  PostType post_type = 27;
  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 28;
  // Metadata about the media, could be anything
  repeated MetaData meta_data = 29;
  // The title of the post
  string title = 30;
  // Customization for youtube posts
  YoutubeCustomization youtube_customization = 31;
  // Customization for tiktok posts
  TikTokCustomization tiktok_customization = 32;
  // Customization for blog posts
  BlogPostCustomization blog_post_customization = 33;
  // Campaign details of a post
  SocialCampaign campaign = 34;
}

message Error {
  string reason = 1;
  string category = 2;
}

message SchedulePostStatus {
  // The ID of this post that can be used to get it again
  string social_post_id = 1;
  // The ID of the social page this post was posted to
  string social_service_id = 2;
  // The Name or Username of the social page this post was posted to (may not
  // always be populated)
  string social_service_label = 3;
  // The reason why there was an error scheduling the post. Error will be nil if
  // it was successful
  Error error = 4;
}

message SocialPostData {
  // The text to post, must be provided for all twitter and google plus page
  // posts
  string post_text = 1;
  // The image to post
  string image_url = 2;
  // When this social post should be posted. If not provided the social  will be
  // posted asap
  google.protobuf.Timestamp schedule_for = 3;
}

message Event {
  // The title of the event
  string title = 1;
  // The start timestamp of the event
  google.protobuf.Timestamp start_datetime = 2;
  // The end timestamp of the evet
  google.protobuf.Timestamp end_datetime = 3;
}

message CallToAction {
  // The url for the CTA
  string url = 1;
  // The type of the CTA
  enum CallToActionType {
    LEARN_MORE = 0;
    BOOK = 1;
    ORDER = 2;
    SHOP = 3;
    SIGN_UP = 4;
    GET_OFFER = 5;
    CALL = 6;
  }
  CallToActionType action_type = 2;
}

message PostTemplate {
  // Unique id signifying account group this belongs to.
  string account_group_id = 1;
  // Unique id signifying template this is.
  string template_id = 2;
  // Title of Template.
  string title = 3;
  google.protobuf.Timestamp created = 4;
  google.protobuf.Timestamp updated = 5;
  // Text contained in template.
  string post_text = 6;
  google.protobuf.Timestamp post_date_time = 7;
  string gmb_post_customization = 8;
  string image_url = 9;
  string image_path = 10;
  string image_size = 11;
  string previews = 12;
  repeated string options = 13;
  string video_url = 14;
  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 15;
  // Metadata about the media, could be anything
  repeated MetaData meta_data = 16;
  // The type of post e.g., Image/video/reel/gif etc
  PostType post_type = 17;
  // Youtube customization
  YoutubeCustomization youtube_customization = 18;
  // Tiktok customization
  TikTokCustomization tiktok_customization = 19;
}

message YoutubeCustomization {
  enum PrivacyStatus {
    YOUTUBE_PRIVACY_STATUS_INVALID = 0;
    YOUTUBE_PRIVACY_STATUS_PUBLIC = 1;
    YOUTUBE_PRIVACY_STATUS_PRIVATE = 2;
    YOUTUBE_PRIVACY_STATUS_UNLISTED = 3;
  }
  // Title of Youtube video
  string title = 1;
  PrivacyStatus privacy_status = 2;
}

message TikTokCustomization{
  bool allow_comment = 1;
  bool allow_duet = 2;
  bool allow_stitch = 3;
}

message Author {
  // The name of the author
  string name = 1;
  // The id of the author
  int64 id = 2;
}

message Category {
  // The categoryId
  int64 id = 1;
  // The category name
  string name = 2;
}

message BlogPostCustomization {
  // The site type of the blog post
  string site_type = 1;
  // The author of the blog post
  Author author = 2;
  // The categories of the blog post
  repeated Category categories = 3;
  // The tags of the blog post
  repeated string tags = 4;
}

message MLPostCategory {
  // is the post a draft
  bool is_draft = 1;

  // is the post a hidden draft
  bool is_hidden = 2;
}

// Represents the current state of a multi-location post.
// Indicates whether the post is a draft, hidden draft, scheduled, or published.
enum MLPostState {
  DRAFT = 0;
  HIDDEN_DRAFT = 1;
  SCHEDULED = 2;
  PUBLISHED = 3;
}