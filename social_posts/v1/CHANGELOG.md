### CHANGELOG

## 1.32.0
- Add `MLPostCategory` to `EditMultilocationPostRequest` to support the edit ML posts functionality

## 1.31.8
- Add `isDraft` to `ListMultilocationPostsForBrandRequest`

## 1.31.7
- Add `MLPostState` to `MultilocationPost`

## 1.31.6
- Add `MLPostCategory` to `BulkUploadMultilocation` and `BulkCreateMultilocationPostRequest`

## 1.31.5
- Add `MLPostCategory` to `MultilocationPost` and `CreateMultilocationPostRequest`

## 1.31.4
- update `EditMultilocationPostRequest` to hold list of `new_services` and `deleted_services`

## 1.31.3
- Add `new_services` and `deleted_services` parameters in `EditMultilocationPostRequest`

## 1.31.2
- Add Location in `BulkUploadMultilocation` and deprecate it from `BulkCreateMultilocationPostRequest`

## 1.31.1
- Update `BulkCreateMultilocationPostRequest` for  BulkCreateMultilocationPost

## 1.31.0
- Added `BulkCreateMultilocationPost` in `MultilocationPostService`

## 1.30.2
- Add `Campaign` (id, name) in `SocialPost`

## 1.30.1
- Add `BlogPostCustomization` in `SocialPost`

## 1.30.0
- Parent folder was renamed from `social-posts` to `social_posts`

## 1.29.10
- Add `TikTok` customization to `SocialPost`

## 1.29.9
- Add `TikTok` as a network.

## 1.29.8
- Add local download url in `ListPixabay` endpoint.

## 1.29.7
- Add post types to `MultilocationPost`

## 1.29.6
- Add `youtube_customization` to `SocialPost`

## 1.29.5
- Add `privacy_status` to `youtube_customization`

## 1.29.4
- Add Post Type Stories

## 1.29.3
- Add `title` to SocialPost v1 response. It'll be surfaced on `List` and `GetMulti`

## 1.29.2
- Add `youtube` to Service enum

## 1.29.1
- Add `youtube_customization` to Create, Update and Read PostTemplate RPCs

## 1.29.0
- Add `metadata` to AI content suggestors request

## 1.28.1
- Add new `Curated_Post` Service.

## 1.27.1
- Rename `TenorGif` `media` map to avoid conflicts with `MediaEntry`

## 1.27.0
- Add tags, customization and link_short_code to MultiLocationPost

## 1.26.0
- Change Version to 1.X.X to fix versioning issues

## 2.26.0
- Add MediaEntry and MetaData to MultilocationPostRequests

## 2.25.0
- Adds `PromptType` to SuggestMessage `RPC`

## 2.24.0
- Remove circular dependencies on social templates

## 2.23.0
- Added new fields for media entries inside templates

## 2.22.0
- Added new fields for media upload

## 2.21.0
- Added new fields

## 2.20.0
- Change engagement field from int64 to float on PostData

## 2.19.0
- Adds post-performance endpoints for csv generation

## 2.18.0
- Moving tenor Gif functionality to social-posts from composer

## 2.17.0
- Adds locations as a field that can be edited for ML posts

## 2.16.0
- Adds CALL enum, get offer has been removed from APIs but leaving it here so that existing get offer posts continue to work

## 2.15.0
- Adds the failed posts to the List ML posts response

## 2.14.0
- Adds GetMultilocationPost rpc, GetMultilocationPostRequest and GetMultilocationPostResponse protos.

## 2.13.0
- Adds gif and video links to the ML post and it's Create and Edit rpcs

## 2.12.0
- Adds the BrandID and MultilocationPostID to the SocialPost object

## 2.11.0
- Add multilocation_post protos and rpc

## 2.10.0
- Add 'page_size' parameter to PartnerListScheduledSocialPostsRequest

## 2.9.1
- Add 'page_size' parameter to ListSocialPostsRequest

## 2.9.0
- Add 'link_preview_image_url' to SocialPost message

## 2.8.0
- Add 'image_urls' to SocialPost message

## 2.7.0
- Add 'tags' parameter on ListSocialPostsRequest


## 2.6.0
- Add call_to_action, event, social_service_id, and video_url to SocialPost

## 2.5.0
- Add GetScheduledPostCount rpc

## 2.4.0
- optional repeated social_service_ids param for ListSocialPostRequest

## 2.3.0
- Add DeleteSocialPost rpc

## 2.2.0
- Add GetSocialPost rpc and SocialPostStats proto

## 2.1.1
- Fix imports to support multi package compilation

## 2.1.0
- Added GMB and UNKNOWN to service types

## 2.0.0
- PartnerListScheduledSocialPostsRequest now has filters and can be filtered by scheduled or created datetime
- Added parent id to social post

## 1.0.0
- Initial commit of social post protos
