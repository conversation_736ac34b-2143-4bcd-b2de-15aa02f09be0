syntax = "proto3";

package socialposts.v1;

option go_package = "github.com/vendasta/generated-protos-go/social-posts/v1;socialposts_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.socialposts.v1.generated";

import "social_posts/v1/pixabay_image.proto";
import "social_posts/v1/social_post_stats.proto";
import "social_posts/v1/multilocation_post.proto";
import "social_posts/v1/field_mask.proto";
import "social_posts/v1/tenor_gif.proto";
import "social_posts/v1/hashtag.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "social_posts/v1/social_posts.proto";

// Enum to indicate the status of each post creation
enum BulkPostStatus {
  SUCCESS = 0;
  FAILURE = 1;
}

message ListSocialPostsRequest {
  // If a start time is provided, a page of the posts that are newer than it
  // will be returned If a start time of the current date time is provided you
  // will get a page of posts that are scheduled to be posted
  google.protobuf.Timestamp start = 1;
  // If an end time is provided, a page of the posts older than it will be
  // returned If an end time of the current date time is provided you will not
  // get any posts that are scheduled to be posted
  google.protobuf.Timestamp end = 2;
  // The business to list the social posts for
  string business_id = 3;
  // The partner the social posts belong to
  string partner_id = 4;
  // A cursor that can be provided to retrieve the next page of results
  string cursor = 5;
  // A list of social service IDs to request posts for
  repeated string social_service_ids = 6;
  // A list of tags to request posts for
  repeated string tags = 7;
  // Number of results to grab
  int64 page_size = 8;
}

message ListSocialPostsResponse {
  // A page of social posts
  repeated SocialPost social_posts = 1;
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 2;
  // Whether or not more results exist
  bool has_more = 3;
}

message DeleteSocialPostRequest {
  // The business to delete the social post for
  string business_id = 1;
  // The post id that going to delete
  string social_post_id = 2;
}

message ScheduleToAllPagesRequest {
  // The social post to post
  SocialPostData social_post = 1;
  string partner_id = 2;
  // The business to post the social post to all of it's connected pages
  // (twitter user, linkedin company, google plus page, facebook page)
  string business_id = 3;
}

message ScheduleToAllPagesResponse {
  // The statuses of the posts that were attempted to be scheduled
  repeated SchedulePostStatus statuses = 1;
}

message SchedulePostRequest {
  // The social post to post
  SocialPostData social_post = 1;
  // The social accounts to post this post to
  repeated string social_service_ids = 2;
  string partner_id = 3;
  // The business that has the social accounts connected to it
  string business_id = 4;
}

message SchedulePostResponse {
  // The statuses of the posts that were attempted to be scheduled
  repeated SchedulePostStatus statuses = 1;
}

message GetMultiSocialPostsRequest {
  // The business ID
  string business_id = 1;
  // The internal post IDs
  repeated string internal_post_ids = 2;
}

message GetMultiSocialPostsResponse {
  // The requested social posts
  repeated SocialPost social_posts = 1;
}

message GetMultiSocialPostStatsRequest {
  // Internal post id
  repeated string internal_post_ids = 1;
  // account group id
  string business_id = 2;
  // partner id
  string partner_id = 3;
}

message GetMultiSocialPostStatsResponse {
  // Post stats entity
  repeated SocialPostStats social_post_stats = 1;
}

// Begin and end of date range
message DateRangeFilter {
  google.protobuf.Timestamp begin_range = 1;
  google.protobuf.Timestamp end_range = 2;
}

message PartnerListScheduledSocialPostsRequest {
  message Filters {
    // Scheduled posts that are scheduled to be posted between begin and end
    // Only one of scheduled_date_filter or created_date_filter can be provided
    DateRangeFilter date_range = 1;
    string partner_id = 3;
  }
  // A cursor that can be provided to retrieve the next page of results
  string cursor = 5;
  Filters filters = 6;
  // Number of results to grab
  int64 page_size = 7;
}

message PartnerListScheduledPostsResponse {
  // A page of social posts
  repeated SocialPost social_posts = 1;
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 2;
  // Whether or not more results exist
  bool has_more = 3;
}

message GetScheduledPostCountRequest {
  // The partner ID
  string partner_id = 1;
  // The business ID
  string business_id = 2;
  // The social service IDs
  repeated string social_service_ids = 3;
}

message GetScheduledPostCountResponse {
  // The total count of scheduled posts
  int64 count = 1;
}

message CreatePostTemplateRequest {
  // The account group id
  string account_group_id = 1;
  // The title of the template
  string title = 2;
  // Fields covering the contents of the Template
  string post_text = 3;
  google.protobuf.Timestamp post_date_time = 4;
  string gmb_post_customization = 5;
  string image_url = 6;
  string image_path = 7;
  string image_size = 8;
  string previews = 9;
  repeated string options = 10;
  string video_url = 11;
  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 12;
  // Metadata about the media, could be anything
  repeated MetaData meta_data = 13;
  // The type of post e.g., Image/video/reel/gif etc
  PostType post_type = 14;
  // Youtube customization for this template
  YoutubeCustomization youtube_customization = 15;
}

message CreatePostTemplateResponse {
  // A brand new unique internal identifier for a PostTemplate object
  string template_id = 1;
}

message UpdatePostTemplateRequest {
  // The account group id
  string account_group_id = 1;
  // The internal id of the template
  string template_id = 2;
  // The title of the template
  string title = 3;
  // Fields covering the contents of the template
  string post_text = 4;
  google.protobuf.Timestamp post_date_time = 5;
  string gmb_post_customization = 6;
  string image_url = 7;
  string image_path = 8;
  string image_size = 9;
  string previews = 10;
  repeated string options = 11;
  string video_url = 12;
  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 13;
  // Metadata about the post, could be anything
  repeated MetaData meta_data = 14;
  // The type of post e.g., Image/video/reel/gif etc
  PostType post_type = 15;
  // Youtube customization for this template
  YoutubeCustomization youtube_customization = 16;
}

message GetPostTemplateRequest {
  // The account group id
  string account_group_id = 1;
  // The internal id of the template
  string template_id = 2;
}

message GetPostTemplateResponse {
  // The obtained PostTemplate
  PostTemplate post_template = 1;
}

message ListPostTemplatesRequest {
  // The account group id
  string account_group_id = 1;
  // Cursor to more templates if it is greater then the page size
  string cursor = 2;
  // Number of results to grab
  int64 page_size = 3;
}

message ListPostTemplatesResponse {
  // List of obtained Templates
  repeated PostTemplate post_template = 1;
  // Cursor to more templates if it is greater then the page size
  string next_cursor = 2;
  // Boolean if there are more templates to get
  bool has_more = 3;
}

message DeletePostTemplateRequest {
  // The account group id
  string account_group_id = 1;
  // The internal id of the template
  string template_id = 2;
}

message CreateMultilocationPostRequest {
  // BrandID for the post
  string brand_id = 1;

  // Original post text
  string text = 2;

  // Original media links
  repeated string media = 3;

  // Date that the original post was scheduled to go
  google.protobuf.Timestamp scheduled_date = 4;

  // List of locations in the original post
  repeated Location locations = 5;

  // Original gif links
  repeated string gifs = 6;

  // Original video links
  repeated string videos = 7;

  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 8;

  // Metadata about the post, could be anything
  repeated MetaData meta_data = 9;

  // Tags required to differentiate posts
  repeated string tags = 10;

  // Extra specific configurations for the post
  PostCustomization customization = 11;

  //The short code of the link in this post.
  string link_short_code = 12;

  // The type of post for each SSID e.g., Image/video/reel/gif etc
  repeated SSIDPostType post_types = 13;

  // post_category is used to differentiate between draft and hidden draft posts
  MLPostCategory post_category = 14;
}

message CreateMultilocationPostResponse {
  // The successfully created Multilocation post
  MultilocationPost post = 1;

  // Multilocation posts with errors
  repeated MultilocationPostError errors = 2;
}

message RemoveFromMultilocationPostRequest {
  // The reason for removing the post.
  RemoveReason reason = 1;

  // BrandID for the post
  string brand_id = 2;

  // MultilocationID
  string multilocation_id = 3;

  // Locations to remove
  repeated Location locations = 4;
}

message ListMultilocationPostsForBrandRequest {
  // BrandID for the post
  string brand_id = 1;

  // Start Date for the multilocation post
  google.protobuf.Timestamp start_date = 2;

  // End Date for the multilocation post
  google.protobuf.Timestamp end_date = 3;

  // A cursor that can be provided to retrieve the next page of results
  string cursor = 4;

  // Number of results to grab
  int64 page_size = 5;

  // This is to filter only drafts. if is_draft is true, then start_date and end_date would be filtering created date.
  bool is_draft = 6;
}

message ListMultilocationPostsForBrandResponse {
  // List of MultilocationPost
  repeated MultilocationPost multilocation_posts = 1;

  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 2;

  // Whether or not more results exist
  bool has_more = 3;

  // A list of failed social posts in this range
  repeated SocialPost failed_social_posts = 4;
}

message EditMultilocationPostRequest {
  // BrandID for the post
  string brand_id = 1;

  // MultilocationID
  string multilocation_id = 2;

  // post text
  string text = 3;

  // media links
  repeated string media = 4;

  // Date that the post is scheduled to go
  google.protobuf.Timestamp scheduled_date = 5;

  // Map of changed fields
  FieldMask field_mask = 6;

  // gif links
  repeated string gifs = 7;

  // video links
  repeated string videos = 8;

  // List of locations, either added, removed or original
  repeated Location locations = 9;

  // Array of media content when uploading multiple media
  repeated MediaEntry media_entries = 10;

  // Metadata about the post, could be anything
  repeated MetaData meta_data = 11;

  // Tags required to differentiate posts
  repeated string tags = 12;

  // Extra specific configurations for the post
  PostCustomization customization = 13;

  //The short code of the link in this post.
  string link_short_code = 14;

  // The type of post for each SSID e.g., Image/video/reel/gif etc
  repeated SSIDPostType post_types = 15;

  //new_services field holds the new services updated during editing workflow
  repeated MultilocationServices new_services = 16;

  // deleted_services field holds the deleted services updated during editing workflow
  repeated MultilocationServices deleted_services = 17 ;

  // post_category is used to differentiate between draft and scheduled posts
  MLPostCategory post_category = 18;

}


message EditMultilocationPostResponse {
  // The successfully created Multilocation post
  MultilocationPost post = 1;

  // Any errors that may have occured on individual posts
  repeated MultilocationPostError errors = 2;
}

message DeleteMultilocationPostRequest {
  // BrandID for the post
  string brand_id = 1;

  // MultilocationID
  string multilocation_id = 2;
}

message GetMultilocationPostRequest {
  // BrandID for the post
  string brand_id = 1;

  // MultilocationID
  string multilocation_id = 2;
}

message GetMultilocationPostResponse {
  // The requested multilocation post
  MultilocationPost multilocation_post = 1;
}

message ListTenorGifsRequest {
  //  Query string for the gifs
  string query = 1;
  // A cursor to specify where to start listing tenor gifs
  string cursor = 2;
  // The number of tenor gifs to return
  int64 page_size = 3;
  // Language of the response
  string locale = 4;
  // Anonymous id required to make calls to tenor apis
  string anonymous_id = 5;
  // Applied filters
  string media_filter = 6;
  // The aspect ratio of gifs to list
  string aspect_ratio_range = 7;
  // Content filter
  string content_filter = 8;
}

message ListTenorGifsResponse {
  // next cursor
  string next = 1;
  // resulting list of tenor gifs
  repeated TenorGif results = 2;
}

message GetTenorAnonymousIdRequest {}

message GetTenorAnonymousIdResponse {
  // Anonyous id to be used for calling tenor
  string anonymous_id = 1;
}

message ListPixabayImagesRequest {
  // Query string for pixabay images
  string query = 1;
  // A cursor to specify where to start listing pixabay images
  string cursor = 2;
  // The number of pixabay images to return
  int64 page_size = 3;
  // to get google storage
  bool include_local_url = 4;

  string agid = 5;
}

message ListPixabayImagesResponse {
  // total number
  int64 total = 1;
  // hit number
  int64 total_hits = 2;
  // Hit pixabay images response list
  repeated PixabayImage hits = 3;
}

message ReplaceHashtagsRequest {
  // Hashtag keyword for create or update for used count
  repeated string keyword = 1;
  // The business id of the account
  string business_id = 2;
  // The partner id of the account
  string partner_id = 3;
}

message DeleteHashtagsRequest {
  // Hashtag keyword to be deleted
  repeated string keyword = 1;
  // The business id of the account
  string business_id = 2;
  // The partner id of the account
  string partner_id = 3;
}

message SearchHashtagRequest {
  // search term for hashtags
  string search_term = 1;
  // Limit size for hashtags
  int64 limit = 2;
  // The business id of the account
  string business_id = 3;
  // The partner id of the account
  string partner_id = 4;
}

message SearchHashtagResponse {
  // Hashtag response list
  repeated Hashtag hashtags = 1;
}

message SuggestMessageRequest {
  // prompt that will be used to suggest the message
  string prompt = 1;
  // business_id is the id of the business fo which this message is being generated to
  string business_id = 2;
  // length indicates whether the consumer wants to build a long or short message
  MessageLength length = 3;
  // type indicates whether the consumer wants to use a specific template or a custom format
  TemplateType type = 4;
  // metadata is a list of key value pairs that can be used to pass additional information to the suggester
  repeated MetaData metadata = 5;
}

message SuggestMessageResponse {
  string message = 1;
}

//This is for Bulk upload for multilocation
message BulkCreateMultilocationPostRequest{
  // BrandID for the post
  string brand_id = 1;
  // List of locations in the original post
  repeated Location locations = 2; // deprecated
  //Array of CreateMultilocationPostRequest
  repeated BulkUploadMultilocation request = 3;

}

message BulkCreateMultilocationPostResponse{
  //The status of each post creation
  BulkPostStatus status = 1;
  //Array of MultilocationPostError
  repeated MultilocationPostError error = 2;
}

service HashTags {
  // Bulk create or update hashtag model
  rpc ReplaceHashtags(ReplaceHashtagsRequest) returns (google.protobuf.Empty);
  // Bulk delete hashtag model
  rpc DeleteHashtags(DeleteHashtagsRequest) returns (google.protobuf.Empty);
  // Search for hashtags based on provided filter
  rpc SearchHashtag(SearchHashtagRequest) returns (SearchHashtagResponse);
}

service PixabayImages {
  // List pixabay images for a given query
  rpc ListPixabayImages(ListPixabayImagesRequest)
      returns (ListPixabayImagesResponse);
}

service TenorGifs {
  // Gets the anonymous id required to make calls to the tenor api
  rpc GetTenorAnonymousId(GetTenorAnonymousIdRequest)
      returns (GetTenorAnonymousIdResponse);
  // list tenor gifs for a given query
  rpc ListTenorGifs(ListTenorGifsRequest) returns (ListTenorGifsResponse);
}

service SocialPosts {
  // List the social posts
  rpc List(ListSocialPostsRequest) returns (ListSocialPostsResponse);
  // Delete a social post
  rpc DeleteSocialPost(DeleteSocialPostRequest) returns (google.protobuf.Empty);
  // Will schedule a post to go out to all company social services connected to
  // the account This includes all twitter accounts, Facebook pages (not
  // facebook users), Google plus pages (not google plus users) and LinkedIn
  // company pages (not linkedin users)
  rpc ScheduleToAllPages(ScheduleToAllPagesRequest)
      returns (ScheduleToAllPagesResponse);
  // Schedule a post to the specified social services
  rpc Schedule(SchedulePostRequest) returns (SchedulePostResponse);
  // Get multiple social posts from a list of IDs
  rpc GetMultiSocialPosts(GetMultiSocialPostsRequest)
      returns (GetMultiSocialPostsResponse);
  // Get multi socialPostStats of a list of facebook post or twitter post
  rpc GetMultiSocialPostStats(GetMultiSocialPostStatsRequest)
      returns (GetMultiSocialPostStatsResponse);
  // Get scheduled post count total for a partner and business
  rpc GetScheduledPostCount(GetScheduledPostCountRequest)
      returns (GetScheduledPostCountResponse);
  // Suggest a social message
  rpc SuggestMessage(SuggestMessageRequest) returns (SuggestMessageResponse);
}

service PartnerSocialPosts {
  // List all the scheduled social posts of all accounts of a partner scheduled
  // for a certain time period
  rpc ListScheduled(PartnerListScheduledSocialPostsRequest)
      returns (PartnerListScheduledPostsResponse);
  // List all the scheduled social posts of all accounts of a partner created
  // during a certain time period
  rpc ListScheduledByCreated(PartnerListScheduledSocialPostsRequest)
      returns (PartnerListScheduledPostsResponse);
}

service PostTemplates {
  // Updates an already existing PostTemplate
  rpc UpdatePostTemplate(UpdatePostTemplateRequest)
      returns (google.protobuf.Empty);
  // Creates a new PostTemplate
  rpc CreatePostTemplate(CreatePostTemplateRequest)
      returns (CreatePostTemplateResponse);
  // Gets an existing PostTemplate
  rpc GetPostTemplate(GetPostTemplateRequest) returns (GetPostTemplateResponse);
  // Lists Post Templates for a given AGID
  rpc ListPostTemplates(ListPostTemplatesRequest)
      returns (ListPostTemplatesResponse);
  // Deletes a Post Template
  rpc DeletePostTemplate(DeletePostTemplateRequest)
      returns (google.protobuf.Empty);
}

service MultilocationPostService {
  // CreateMultilocationPost is called to create a multilocation post
  rpc CreateMultilocationPost(CreateMultilocationPostRequest)
      returns (CreateMultilocationPostResponse);

  // EditMultilocationPost is called to edit a multilocation post
  rpc EditMultilocationPost(EditMultilocationPostRequest)
      returns (EditMultilocationPostResponse);

  // RemoveFromMultilocationPost is called to remove(edit/delete) a
  // multilocation post
  rpc RemoveFromMultilocationPost(RemoveFromMultilocationPostRequest)
      returns (google.protobuf.Empty);

  // ListMultilocationPostsForBrand is called to retrieve a list multilocation
  // post based on a date range
  rpc ListMultilocationPostsForBrand(ListMultilocationPostsForBrandRequest)
      returns (ListMultilocationPostsForBrandResponse);

  // DeleteMultilocationPost is called to delete multilocation post
  rpc DeleteMultilocationPost(DeleteMultilocationPostRequest)
      returns (google.protobuf.Empty);

  // GetMultilocationPost is called to get a single multilocation post based on
  // brand_id and multilocation_id
  rpc GetMultilocationPost(GetMultilocationPostRequest)
      returns (GetMultilocationPostResponse);

  // BulkCreateMultilocationPost is called to create a bulk upload in multilocation
  rpc BulkCreateMultilocationPost(BulkCreateMultilocationPostRequest)
      returns (BulkCreateMultilocationPostResponse);
}

message PostData {
  string post_id = 1;
  int64 reach = 2;
  float engagement = 3;
  int64 views = 4;
  int64 clicks = 5;
  int64 comments = 6;
  int64 shares = 7;
  int64 reactions = 8;
  int64 saves = 9;
  int64 likes = 10;
  int64 video_views = 11;
  int64 retweets = 12;
  int64 favourites = 13;
  int64 impressions = 14;
  int64 comments_count = 15;
  int64 like_count = 16;
}

message GenerateCSVForPerformanceStatsRequest {
  // business_id for the stats (e.g., ABC)
  string business_id = 1;

  // array of maps of post_id, reach and engagement to merge
  repeated PostData post_data = 2;

  // array of string for getting the headers to be included
  repeated string include_headers = 3;
}

message GenerateCSVForPerformanceStatsResponse {
  // returns just a url string for now
  // reason for calling it a generated id is so
  // that in future if we are supporting caching,
  // we can use a id instead of URL
  string generated_id = 1;
}

message GetGeneratedCSVForPerformanceStatsRequest {
  // Same as generated_id from GenerateCSVForPerformanceStatsResponse
  string generated_id = 1;
}

message GetGeneratedCSVForPerformanceStatsResponse {
  // The csv storage url from google storage (or any other)
  string url = 1;
}

service PostPerformance {
  // Trigger csv generation  for performance stats
  rpc GenerateCSVForPerformanceStats(GenerateCSVForPerformanceStatsRequest)
      returns (GenerateCSVForPerformanceStatsResponse);

  // This endpoint is currently just a placeholder and would be in use if we
  // implement caching for multiple downloads etc
  rpc GetGeneratedCSVForPerformanceStats(
      GetGeneratedCSVForPerformanceStatsRequest)
      returns (GetGeneratedCSVForPerformanceStatsResponse);
}

