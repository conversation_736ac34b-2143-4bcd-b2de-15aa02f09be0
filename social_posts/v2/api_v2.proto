syntax = "proto3";

package socialposts.v2;

option go_package = "github.com/vendasta/generated-protos-go/social-posts/v2;socialposts_v2";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.socialposts.v2.generated";

import "vendasta_types/annotations.proto";

import "social_posts/v2/social_post_v2.proto";
import "social_posts/v2/linkedin_v2.proto";
import "social_posts/v2/unsplash_image.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

// The complete request of the ScheduleSocialPosts RPC
message SocialPostRequest {
  // List of posts to be created and scheduled
  repeated SocialPostV2 social_posts = 1;
}

message RepostSocialPostRequest {
  // Social post id to repost
  string social_post_id = 1;
  // Account group id of the post to repost
  string account_group_id = 2;
}

message UpdateSocialPostRequest {
  // Social post to update
  SocialPostV2 social_post = 1;
  // is post is an error
  bool is_error = 2;
  // errors in case any
  string errors = 3;
  // error description
  string error_category = 4;
  // post id, e.g. urn:share:12345
  string post_id = 5;

  // Contains metadata if any associated with the post
  repeated MetadataV2 metadata = 6;

  // permalink, e.g. https://www.facebook.com/***************/posts/***************
  string permalink = 7;
}


message UpdateSocialPostResponse {
  // Social post to update
  SocialPostV2 social_post = 1;
  // is post is an error
  bool is_error = 2;
  // errors in case any
  string errors = 3;
  // error description
  string error_category = 4;
  // post id, e.g. urn:share:12345
  string post_id = 5;

  // Contains metadata if any associated with the post
  repeated MetadataV2 metadata = 6;

}

// The complete response of the ScheduleSocialPosts RPC
message SocialPostResponse {
  // List of the created/scheduled posts containing internal identifiers
  repeated SocialPostOutput social_posts = 1;
}

// Contains all the information of a post plus internal identifier and metadata
message SocialPostOutput {
  // Internal identifier of the post
  string internal_post_id = 1;
  // All the post content and information
  SocialPostV2 social_post = 2;
  // Contains metadata if any associated with the post
  repeated MetadataV2 metadata = 3;
  // Represents the status of each post, since we allow partial schedule for bundles
  PostStatusV2 status = 4;
  // A message related to the post. Can be the error message in case of an error status, for example
  string status_message = 5;
}

message CreateImageRequest{
  // The business id of the account
  string business_id = 1;
  // search term for image generator
  string prompt = 2;
  // number of images needed 1-10 images
  int32 image_amount = 3;
  // size of the image. For DALL-E models: "256x256", "512x512", "1024x1024". For GPT-image-1: "1024x1024", "1536x1024", "1024x1536", "auto"
  string size = 4;
  // images format : "url" , "b64_json"
  string response_format = 5;
  // metadata is a list of key value pairs that can be used to pass additional information to the suggester
  repeated MetadataV2 metadata = 6;
  // The model to use for image generation. Can be "dall-e-2", "dall-e-3", or "gpt-image-1"
  string model = 7;
  // The style of the generated images. Can be "vivid" or "natural" (DALL-E-3 only)
  string style = 8;
  // The quality of the generated images. Can be "standard" or "hd" for DALL-E models, or "high", "medium", "low" for GPT-image-1
  string quality = 9;
}

message CreateImageResponse{
  // request id from DALLE
  uint64 created_id=1;
  // List of images
  repeated ImageCreated generated_images=2;
}

message ImageCreated{
  string url = 1;
  string b64_json = 2;
  string revised_prompt = 3;
}

message ImageUrl {
  string url = 1;
}

message ImageBlob {
  bytes blob = 1;
}

message ListPostableSocialServiceRequest {
  // The business id of the account
  string business_id = 1;
}

message SocialService {
  // Social service ID
  string ssid = 1;
  // Social profile ID
  string spid = 2;
  // Account group ID
  string account_group_id = 3;
  // Whether the service is authenticated
  bool is_authenticated = 4;
  // Profile URL
  string profile_url = 5;
  // Profile image URL
  string profile_image_url = 6;
  // Type of the social service, eg: TW_USER, FB_PAGE, LI_COMPANY, etc.
  string service_type = 7;
  // Social profile identifier
  string social_profile_id = 8;
  // Whether the service is currently syncing
  bool is_syncing = 9;
  // Tags associated with the client, eg: "SM", "RM", etc.
  repeated string client_tags = 10;
  // Service identifier
  string service_id = 11;
  // Whether the service is disabled
  bool is_disabled_flag = 12;
  // Whether the social token is broken
  bool social_token_broken = 13;
  // For Twitter, this will be the Twitter handle, otherwise this will be just the name
  string name = 14;
  // For Twitter, this will be the User display name, otherwise this will be same as the name
  string full_name = 15;
  // Whether to collect posts
  bool collect_posts_flag = 16;
  // Whether to collect stats
  bool collect_stats_flag = 17;
}

message ListPostableSocialServiceResponse {
  // List of postable social services
  repeated SocialService  postable_social_service= 1;
}

service SocialPostsV2 {
  // Schedule social posts
  rpc ScheduleSocialPosts(SocialPostRequest) returns (SocialPostResponse);
  // Update social posts
  rpc UpdateSocialPosts(UpdateSocialPostRequest) returns (UpdateSocialPostResponse);
  // Generate images from AI
  rpc CreateImages(CreateImageRequest) returns (CreateImageResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Generate images from AI
  rpc GetImageByUrl(ImageUrl) returns (ImageBlob)  {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Generate Variations
  rpc GenerateVariations(ImageUrl)  returns (CreateImageResponse);
  // Repost a failed post
  rpc RepostSocialPost(RepostSocialPostRequest) returns (google.protobuf.Empty);
  // List postable social services
  rpc ListPostableSocialServices(ListPostableSocialServiceRequest) returns (ListPostableSocialServiceResponse);
}

message MediaUploadRequest {
  // LinkedIn user's access token
  string access_token = 1;

  // owner e.g., it could either be a person(in case of a personal profile)
  // or a organization (in case of a business page maintained)
  // urn:li:organization:24141830 this would get translated to
  // registerUploadRequest. owner	schema inside linkedin vector API
  // https://docs.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/vector-asset-api?view=li-lms-2022-08&tabs=http#schema
  string owner = 3;

  // video url, it should be a public url. In case of SM, it would be the gogole
  // storage public url of the resource
  repeated Media medias = 2;
}

message MediaUploadResponse {
  // upload_urn would be a resouce identifier returned by LinkedIn service which
  // would be needed to add iamge/video in a post
  repeated string upload_urns = 1;
}

message DeletePostRequest {
  // Linkedin user's access token
  string access_token = 1;
  // A Linkedin post urn
  string post_id = 2;
}

// Linkedin is a service responsible for linkedin domain logic
service Linkedin {
  // Deletes a post on the Linkedin platform
  rpc DeletePost(DeletePostRequest) returns (google.protobuf.Empty);
  // Upload media rpc, to upload any video/gif/image media to linkedIn
  rpc UploadMedia(MediaUploadRequest) returns (MediaUploadResponse);
}


message AiInstructions{
  string common_ai_instructions = 1;
  bool data_usage_status = 2;
  string ai_blog_instructions = 3;
  google.protobuf.Timestamp created = 4;
  google.protobuf.Timestamp updated = 5;

}

message CreateCommonAiInstructionsRequest{
  string business_id = 1;
  string partner_id = 2;
  AiInstructions ai_instructions = 3;
}


message CreateCommonAiInstructionsResponse{
  AiInstructions ai_instructions = 1;
}

message UpdateCommonAiInstructionsRequest{
  string business_id = 1;
  string partner_id = 2;
  AiInstructions ai_instructions = 3;
}

message UpdateCommonAiInstructionsResponse{
  AiInstructions ai_instructions = 1;
}

message GetCommonAiInstructionsRequest{
  string business_id = 1;
  string partner_id = 2;
}

message GetCommonAiInstructionsResponse{
  AiInstructions ai_instructions = 1;
}


service CommonAiInstructions {
  rpc CreateCommonAiInstructions(CreateCommonAiInstructionsRequest) returns (CreateCommonAiInstructionsResponse);
  rpc UpdateCommonAiInstructions(UpdateCommonAiInstructionsRequest) returns (UpdateCommonAiInstructionsResponse);
  rpc GetCommonAiInstructions(GetCommonAiInstructionsRequest) returns (GetCommonAiInstructionsResponse);
}



message ListUnsplashImagesRequest{
  // Query string for unsplash images
  string query = 1;
  // page to be retrieved. By default 1
  int64 page = 2;
  // number of images per page. By default 10
  int64 per_page = 3;
  //  how to sort the images. By default "relevant"
  string order_by = 4;
  // collection id to narrow the search. Optional
  repeated string collection = 5;
  // filter content bu safety. Optional
  string content_filter = 6;
  // filter result by color. Optional
  string color = 7;
  // filter result by image orientation. Optional
  string orientation = 8;
  // if set to true, use the random endpoint instead of search endpoint
  bool random = 9;

  // to get google storage
  bool include_local_url = 10;
}


message ListUnsplashImagesResponse{
  int64 total = 1;
  int64 total_pages = 2;
  repeated Photo results = 3;
}


message UploadToStorageRequest {
  string url = 1;
  MediaType media_type = 2;
  string agid = 3;
}

message UploadToStorageResponse {
  string url = 1;
}

service Unsplash {
  rpc ListUnsplashImages(ListUnsplashImagesRequest) returns (ListUnsplashImagesResponse);
  rpc UploadToStorage(UploadToStorageRequest) returns (UploadToStorageResponse);
}

message GeneratePostsRequest {
  // The number of posts to generate.
  int64 number_of_posts = 1;
  // The topic the posts should be about.
  string topic = 2;
  // Common instructions to use when generating the posts.
  string common_instructions = 3;
  // The tone of the posts.
  string tone = 4;
  // The max character length of the posts.
  int64 post_length = 5;
  // Meta data to exchange info like AGID/PartnerId as  flexible for AI knowledge
  repeated MetadataV2 metadata = 6;

}


message GeneratePostsResponse {
  // Below Image Response is used to expose the image details
  message ImageResponse{
    string type = 1;
    string web_format_url = 2;
    string full_hd_url = 3;
    string storage_url = 4;
  }
  message Post {
    // The  post returned from the AI
    string text= 1;
    // The  Images returned from  any  image provider
    ImageResponse image_response = 2;
  }

  repeated Post posts = 1;
  string keyword = 2;

}

//This request and response for AI purpose
enum GenerateType {
  TITLE = 0;
  OUTLINE = 1;
  BLOG = 2;
}

// The length of the content to be generated
enum ContentLength {
  SHORT_FORM = 0;
  MEDIUM_FORM = 1;
  LONG_FORM = 2;
  OPTIMIZED_FORM = 3;
}

//This request message for generate the content
message GenerateAiRequest {
  // This goal is to create a title and content
  string goal = 1;
  // Instructions for how the content should be generated
  string instruction = 2;
  // Keyword for generating the content
  string keyword = 3;
  // Tone indicates the desired style or type of content
  string tone = 4;
  // Length refers to the number of characters in the content
  ContentLength length = 5;
  // generate_type determines the response format based on the enum value
  GenerateType generate_type = 6;
    // contains key value pair to utilise additional details.
    repeated MetadataV2 metadata = 7;
}

// Response message for Title and Outline(Array of string)
message MultiResponse {
  repeated string items = 1;
}

// Response message for BLOG (String)
message Response {
  string content = 1;
}

// This is Wrapper message for multiple response types
message GenerateAiResponse {
  oneof response {
    // (For TITLE and OUTLINE)
    MultiResponse multi_result = 1;
    // (For BLOG)
    Response result = 2;
  }
}
message GenerateContentMetaDataRequest {
   // Account group ID
  string business_id = 1;

  oneof generation_type {
    TitleGeneration title_input = 2;
    KeywordGeneration keyword_input = 3; //deprected use FetchLibraryImagesRequest endpoint
  }
}

message TitleGeneration {
  // Title of the blog.
  string blog_title = 1;
}

message KeywordGeneration {
  // Unique identifier for the blog post
  string blog_post_id = 1;
  // Number of keywords to generate
  uint32 keyword_count = 2;
  // Type of the blog post
  PostCategory post_type = 3;
  // User prompt for generating keywords
  repeated string user_prompt = 4;
}

message GenerateContentMetaDataResponse {
  oneof result {
    // Generated title suggestion
    string generated_title = 1;
    // List of generated keywords
    KeywordList generated_keywords = 2; //deprected use FetchLibraryImagesResponse endpoint
  }
}

message KeywordList {
  repeated string keywords = 1;
}

message GenerateBlogPostCampaignRequest {
   // Account group ID
   string business_id = 1;
   // List of social networks
   repeated Network network_type = 2;
    // Campaigns from a blog post
    BlogPostCampaign blog_campaign = 3;
    // Partner ID to fetch the common instructions
    string partner_id = 4;
}

message BlogPostCampaign {
  // Unique identifier for the blog post
  string blog_post_id = 1;
  // Type of the blog post
  PostCategory blog_post_type = 2;
  // No. of posts to generate
  uint32 no_of_posts = 3;

}

message GeneratePostCampaignRequest {
  // Account group ID
  string business_id = 1;
  // List of social networks
  repeated Network network_type = 2;
   // Campaigns from user's input
   PostsGeneration post_generation = 3;
   // Partner ID to fetch the common instructions
   string partner_id = 4;
  }

message PostsGeneration {
  // The topic the posts should be about
  repeated string topic = 1;
  // The goal of the post
  string goal = 2;
  // Tone of the post
  Tone tone = 3;
  // Length of the post
  ContentLength content_length = 4;
  // No. of posts to generate
  uint32 no_of_posts = 5;
  // Whether or not use common instructions
  bool use_common_instr = 6;
}

enum Tone {
  INVALID_TONE = 0;
  DEFAULT = 1;
  FRIENDLY = 2;
  LUXURIOUS = 3;
  PROFESSIONAL = 4;
  RELAXED = 5;
  BOLD = 6;
  ADVENTUROUS = 7;
  WITTY = 8;
  PERSUASIVE = 9;
  EMPATHETIC = 10;
}

enum Network {
  INVALID_NETWORK = 0;
  FACEBOOK = 1;
  INSTAGRAM = 2;
  LINKEDIN = 3;
  TWITTER = 4;
  GMB = 5;
  TIKTOK = 6;
  YOUTUBE = 7;
  CUSTOM_FEED = 8;
  WORDPRESS = 9;
}

enum PostCategory {
  INVALID_CATEGORY = 0;
  DRAFT = 1;
  SCHEDULED = 2;
  PUBLISHED = 3;
}

message GeneratePostCampaignResponse {
    // List of posts generated
    repeated Post posts = 1;
}

message Post {
  //Topic to which the post generated
  string topic = 1;
  //Post content by topic
  repeated PostContent post_content = 2;
}

message PostContent {
  //Network to which the post generated
  Network social_network = 1;
  //Post generated
  string post = 2;
}

message FetchLibraryImagesRequest {
// Account group ID to fetch images
string business_id = 1;
//Input to generate keywords
KeywordGeneration keyword_input = 2;
//Number of images required
uint32 image_count = 3;
// to get google storage
bool include_local_url = 4;
// Page number for paginated results
uint32 page_number = 5;
// Sorting order of results: "popular" or "latest" (default is "popular")
string order = 6;
}

message FetchLibraryImagesResponse {
  //List of images fetched
  repeated ImageResponse images = 1;
}

message ImageResponse {
  //pixabay image type
  string type = 1;
  //pixabay image webformat URL
  string webformat_url = 2;
  //pixabay image full HD URL
  string full_hd_url = 3;
  // Local storage url
  string storage_url = 4;
}

service ContentGeneration {
  rpc GeneratePosts(GeneratePostsRequest) returns (GeneratePostsResponse);
  rpc Generate(GenerateAiRequest) returns (GenerateAiResponse);
  rpc GenerateContentMetaData(GenerateContentMetaDataRequest) returns (GenerateContentMetaDataResponse);
  rpc GenerateBlogPostCampaign(GenerateBlogPostCampaignRequest) returns (GeneratePostCampaignResponse);
  rpc GeneratePostCampaign(GeneratePostCampaignRequest) returns (GeneratePostCampaignResponse);
  rpc FetchLibraryImages(FetchLibraryImagesRequest) returns (FetchLibraryImagesResponse);
}

message ListCuratedContentRequest {
  // Account ID of the business
  string business_id = 1;
  // Feed Identifier of the website(Integration)
  string feed_id = 2;
  // Page size
  int32 page_size = 3;
  // Next page cursor
  string cursor = 4;
}

message UploadedMedia {
  string type = 1;
  string url = 2;
}

message CuratedContentPost {
  // Unique identifier of the post
  string id = 1;
  // The message of the post
  string text = 2;
  // Both the image and video details are stored in this field
  repeated UploadedMedia media = 3;
  // The error message if the post is an error
  string errors = 4;
  // The business ID of the post
  string business_id = 5;
  // The date the post was created
  string created_at = 6;
  // The date the post was posted
  string posted_at = 7;
}

message ListCuratedContentResponse {
  repeated CuratedContentPost posts = 1;
  string external_id = 2;
  bool has_more = 3;
  string next_cursor = 4;
}

service CuratedContent {
  rpc List(ListCuratedContentRequest) returns (ListCuratedContentResponse);
}
