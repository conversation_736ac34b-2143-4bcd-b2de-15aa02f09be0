### CHANGELOG

# 2.59.0
- added `Image resposne` in post campaign response.

# 2.58.0
- Add `SocialServices` Service to Social Posts v2.
- Add `ListPostable` API in `SocialServices` Service.
- Add `ListPostableSocialServiceRequest`, `SocialService` &`ListPostableSocialServiceResponse` messages.

# 2.57.4
- Updated `CreateImageRequest` message comments to include GPT-image-1 parameters.

# 2.57.3
- removed `per_page` param to `FetchLibraryImagesRequest` message.

# 2.57.2
- Add `page_number` and `per_page` and `order` param to `FetchLibraryImagesRequest` message.

# 2.57.1
- Add `user_prompt` param to `FetchLibraryImages` message.
- Add `include_local_url & localstorage_url` properties to `FetchLibraryImagesRequest & Response` messages.

# 2.57.0
- Add `FetchLibraryImages` service to `ContentGeneration`.

# 2.56.1
- Adjust `GeneratePostCampaignResponse` structure.

# 2.56.0
- Add `UpdateCampaignId` API in `CampaignService`.

# 2.55.0
- Add `GetMulti` API in `CampaignService`.

# 2.54.2
- Add  `campaign_id` in Social post message

# 2.54.1
- Add `partner_id` to `GeneratePostCampaignRequest`.
- Add `partner_id` to `GenerateBlogPostCampaignRequest`.

# 2.54.0
- Add `delete` API in `CampaignService`.

# 2.53.1
- Add `business_id` in `GetCampaign` request.

# 2.53.0
- Add `GeneratePostCampaign` endpoint to `CampaignService`.

# 2.52.0
- Add `GenerateBlogPostCampaign` endpoint to `CampaignService`.

# 2.51.0
- Add `GetCampaign` endpoint to `CampaignService`.

# 2.50.0
- Added API for `update Campaign Title`

# 2.49.0
- Add `GenerateContentMetaData` service to `ContentGeneration`.

# 2.48.0
- Added `CampaignService` in Social Posts v2.

# 2.47.2
- Updated `created` and `updated` field type to time in AI instruction.

# 2.47.1
- Added `created` and `updated` fields in AI instruction.

# 2.47.0
- Added `RepostBlogPostRequest` Blog API.

# 2.46.2
- Added blog instruction in AI Common Instruction.

# 2.46.1
- Exclude post_tags from `UpdateBlogPostRequest`.

# 2.46.0
- Add `DeleteBlogPost` in  `BlogpostsService` API.

# 2.45.0
- Add `UpdateBlogPost` in  `BlogpostsService` API.

# 2.44.0
- Add `BlogpostsService` API.

# 2.43.1
- Add `internal_post_id` in `PublishPost` API payload.

# 2.43.0
- Add `PublishPost` API in Social Posts v2.

# 2.42.0
- Add `GetAuthors` API in Social Posts v2.

# 2.41.0
- Add `GetCategoryRequest` API in Social Posts v2.

# 2.40.0
- Add `DeleteBlogConnectionRequest` API in Social Posts v2.

# 2.39.1
- Add `metadata` property in `GenerateAiRequest` request to get the additional details like AGID. 

# 2.39.0
- Add `length` property in `GenerateAiRequest` request for Blog generation. 

# 2.38.0
- Add `WordpressPluginService` APIs in Social Posts v2.
- Add `Status` API in `WordpressPluginService` to check the status of the plugin.

# 2.37.0
- Add `Generate` API in ContentGeneration Service. 
- The ContentGeneration Service is part of Social Posts v2.

# 2.36.0
- Add `site_url` column in `BlogConnectionService` APIs. 

# 2.35.0
- Add `GetBlogConnection` API in Social Posts v2.

# 2.34.1
- Update `ListBlogConnectionResponse` API to return list of blog connection.
- Update `ListBlogConnectionResponse` API to have cursor and page size to the request and response.

# 2.34.0
- Add `ListBlogConnection` API in Social Posts v2.

# 2.33.0
- Add `UpdateBlogConnection` API in Social Posts v2.

# 2.32.0
- Add `CreateBlogConnection` API in Social Posts v2.

# 2.31.2
- Adding brand id as part of the Send message request.

# 2.31.1
- Add Invalid Role for AI Chatbot using embeddings.

# 2.31.0
- Add SendMessageV2 for AI Chatbot using embeddings.

# 2.30.0
- Parent folder was renamed from `social-posts` to `social_posts`

# 2.29.5
- Add Metadata array for Chatbot request and BulkAI request.

# 2.29.4
- Add Metadata as part of Chatbot request and BulkAI request.

# 2.29.3
- Replace external_id with feed_id in List Curated content API and add external_id in response.

# 2.29.2
- Add API to List Curated content which will be used as publicly accessible.

# 2.29.1
- Add Data usage consent for AI.

# 2.29.0
- Add scope to `CreateImages` and `GetImageByUrl`

# 2.28.1
- Renaming tiktok customization field.

# 2.28.0
- Adding more fields to `SocialPostV2` payload.


# 2.27.6
- Include ChatBot for improve post with AI.

# 2.27.5
- Include `storage_url` in response for unsplash


# 2.27.4
- Add AGID to `UploadToStorageRequest` request


# 2.27.3
- Add keyword to GeneratePostsResponse

# 2.27.2
- Add Upload functionality to unsplash

# 2.27.1
- Add Unsplash service, ListUnsplashImagesRequest and ListUnsplashImagesResponse

# 2.27.0
- Bump 22 times to fix a versioning issue where v1@v2.26.0 is used <NAME_EMAIL>.x

# 2.5.0
- Add ContentGeneration service to generate multiple posts.

# 2.4.2
- Add Protos for Common AI Instructions

# 2.4.1
- Add Postype Stories

# 2.4.0
- add Dalle-3

# 2.3.4
- Add `youtube_customization` to `SocialPostV2`

# 2.3.3
- Add `youtube_customization` to SocialPostV2 customization since we'll need to have the privacy status

# 2.3.2
- Remove `youtube_customization` from SocialPostV2 customization since it is not used yet and the title is already on the proto

# 2.3.1
- Add `youtube_customization` to SocialPostV2 customization, so it can be used when scheduling a post

# 2.3.0
- Add `metadata` to `CreateImageRequest`

# 2.2.0
- Add `RepostSocialPost` RPC to `social-posts/v2`

# 2.1.1
- Add `brand_id` and `multilocation_post_id` to `SocialPostV2`

# 2.1.0
- Add image Variations Endpoint

# 2.0.0
- Rename some v2 protos to avoid conflicting when generating SDKs
- Rename some fields to pluralize repeated fields
- Bump major version to ease the confusion with generated protos 

# 1.9.1
- Update SocialPost to include partner_id which is needed for instagram

# 1.9.0
- Update SocialPostOutput to contain the status of each post being scheduled

# 1.8.1
- Add new field in update endpoint in v2

# 1.8.0
- Add update endpoint in v2

# 1.7.0
- Add metadata in soical-posts payload

# 1.6.1
- Add GetImageByUrl endpoint.
# 1.6.0
- Add internal_post_id in soical-posts payload

# 1.5.0
- Add image generator endpoint 
# 1.4.0
- Adding delete endpoint

# 1.3.0
- Renaming more files.
# 1.2.0
- Renaming file.

# 1.1.0
- Add `MediaUpload` endpoint for `LinkedIn` µService.

# 1.0.0
- First definition of social_posts/v2 containing a new RPC `ScheduleSocialPosts` which will 
orchestrate the scheduling of a post
