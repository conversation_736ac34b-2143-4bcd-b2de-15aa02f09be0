syntax = "proto3";

package vendastatypes;

option go_package = "github.com/vendasta/generated-protos-go/vendasta_types;vendastatypes";
option java_outer_classname = "AnnotationsProto";
option java_package = "com.vendasta.vendasta_types.generated";

import "google/protobuf/descriptor.proto";

// Access option to protect an endpoint with.
//
// If any of the scopes annotated on the endpoint are present in the access token
// access will be granted to the caller. Any number of scopes can be listed.
//
// ----- Example 1: Single Scope
//
// rpc GetMulti(GetMultiRequest) returns (GetMultiResponse) {
//   option (vendastatypes.access) = {
//     scope: "partner.business.manage"
//   };
// }
//
// ----- Example 2: Multiple Scopes
//
// rpc GetMulti(GetMultiRequest) returns (GetMultiResponse) {
//   option (vendastatypes.access) = {
//     scope: "partner.business.manage"
//     scope: "partner.business.read"
//   };
// }
//
// ----- Example 3: Public endpoints
//
// rpc GetMulti(GetMultiRequest) returns (GetMultiResponse) {
//   option (vendastatypes.access) = {
//     public: true
//   };
// }
message Access {
    // scope A list of scopes required to pass the access check. Any singular scope will pass the access check.
    repeated string scope = 1;
    // public Whether or not this endpoint is public
    bool public = 2;
}

// MCP option to control Model Context Protocol (MCP) tool generation.
//
// Use this option to set mark RPCs for inclusion in specific MCP server configurations by specifying server_ids.
// RPCs can have multiple server_ids to be included in different MCP configurations.
//
// ----- Example
//
// rpc GetReviewForBusiness(GetReviewForBusinessRequest) returns (GetReviewForBusinessResponse) {
//   option (vendastatypes.mcp) = {
//     server_id: "partner"
//     server_id: "reviews"
//   };
// }
//
// MCP servers are generated with the `mscli app sdk -l mcp` command.
// It will generate tools for RPCs in the protoPaths in the project's microservice.yaml file.
// The `--mcp-server-id` flag can be used to filter which RPCs are included in the generated MCP server.
// - Only RPCs with that server_id as an mcp option are included in the generated MCP server.
// - If no server_id is specified during generation, ALL RPCs are included.
message MCPOptions {
    // server_id A list of server_ids for this RPC. RPCs can have multiple server_ids to be included in different MCP configurations.
    repeated string server_id = 1;
}

extend google.protobuf.MethodOptions {
    Access access = 50000;
    MCPOptions mcp = 50001;
}
