## 1.11.0
- Add MCP (Model Context Protocol) options for tagging RPCs for selective inclusion in MCP server generation

## 1.10.1
- Regenerate for python protobuf >4.0

## 1.10.0
- Revert 1.9.0

## 1.9.0
- Add FilterType
- Add FilterOperator
- Add DateDefault
- Add Filter
- Add FilterGroupOperator
- Add FilterGroup

## 1.8.0
- Add description to `Property` message.

## 1.7.0
- Move `annotation/option.proto` into vendasta_types

## 1.6.2
- Add paging message types `PagedRequestOptions` and `PagedResponseMetadata` 

## 1.6.1
- Rename PII tag `PROPERTY_PII_TAG_RELATED` to `PROPERTY_PII_TAG_PD`

## 1.6.0
- Add the `pii_tag` field to the `Property` message.

## 1.5.0
- Redefine what the Access Proto is going to be used for.

## 1.4.0
- Change filters to matcher

## 1.3.0
- Filters added

## 1.2.0 
- Add date range

## 1.1.0
- Add Bytes to Property

## 1.0.0
- <PERSON><PERSON><PERSON> added
- <PERSON>eoPoint added
