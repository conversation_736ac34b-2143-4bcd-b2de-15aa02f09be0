syntax = "proto3";

package ai_assistants.v1alpha1;

option go_package = "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1;ai_assistants";
option java_outer_classname = "FunctionProto";
option java_package = "com.vendasta.ai_assistants.v1alpha1.generated";

import "google/protobuf/timestamp.proto";
import "ai_assistants/v1alpha1/namespace.proto";
import "vendasta_types/paging.proto";

message FunctionKey {
  // The ID of the function
  string id = 1;
  // The namespace that the function belongs to
  Namespace namespace = 2;
}

message FunctionParameter {
  // The name of the parameter
  string name = 1;
  // The description of the parameter
  string description = 2;
  // The type of the parameter
  string type = 3;
  // The properties that the parameter contains (if the type is an object)
  repeated FunctionParameter properties = 4;
  // The definition of the items that the array contains (only used if type is array)
  FunctionParameter items = 5;
  // The value of the parameter - used only when the parameters is filled automatically by the system
  string value = 6;
  // The location of the parameter in the function call.
  enum ParameterLocation {
    // Parameter is set on the request body
    LOCATION_BODY = 0;
    // Parameter is set in the URL as a query parameter
    LOCATION_QUERY_PARAM = 1;
  }
  ParameterLocation location = 7;
}

message FunctionAuthStrategy {
  message UnspecifiedFunctionAuthStrategy {}
  message PlatformManagedFunctionAuthStrategy {
    // The scopes required to use the function
    repeated string required_scopes = 1;
  }
  // The auth strategy to use when calling this function
  oneof auth_strategy {
    // The function does not use a defined auth strategy (may be unauthed, or may provide an api key via parameters, headers, etc)
    UnspecifiedFunctionAuthStrategy unspecified = 1;
    // The function uses the platform managed auth strategy
    PlatformManagedFunctionAuthStrategy platform_managed = 2;
  }
}

message Function {
  // The ID of the function
  string id = 1;
  // The namespace that the function belongs to
  Namespace namespace = 2;
  // The description of the function
  string description = 3;
  // The URL that the function will call
  string url = 4;
  // The method type that the function call will use to call the URL
  string method_type = 5;
  // The parameters that the function accepts
  repeated FunctionParameter function_parameters = 6;
  // Functions that generate an answer are not actually executed, instead the request parameters are used as the generated answer
  bool generates_answer = 7;
  // The time the function was last updated
  google.protobuf.Timestamp updated = 8;
  // The headers that the function call will use to call the URL
  repeated FunctionHeader headers = 9;
  // Whether the function is managed outside of its namespace
  // A managed function cannot be edited, it is only updated by the managed configuration
  bool managed = 10;
  // The auth strategy to use when calling this function
  FunctionAuthStrategy auth_strategy = 11;
  // The ID of the MCP this function is created from
  string mcp_id = 12;
}

message FunctionHeader {
  string key = 1;
  string value = 2;
}

// ListMCPToolsRequest is used to retrieve the functions that are available for an MCP using a tools/list request
message ListMCPToolsRequest {
  // The namespace that the MCP and functions will belong to
  Namespace namespace = 1;
  // The base URL of the MCP
  string url = 2;
  // The auth strategy to use when calling the MCP
  FunctionAuthStrategy auth_strategy = 3;
  // The headers required to connect to the MCP
  repeated FunctionHeader headers = 4;
  // The human readable identifier for the MCP
  string mcp_id = 5;
}

message ListMCPToolsResponse {
  // The functions that are available for the MCP
  repeated Function functions = 1;
}

message UpsertMCPRequest {
  // The namespace that the MCP and functions will belong to
  Namespace namespace = 1;
  // The base URL of the MCP
  string url = 2;
  // The human readable identifier for the MCP
  string mcp_id = 3;
  // The MCP functions to update
  repeated Function functions = 4;
}

message ListMCPsRequest {
  message Filters {
    // The namespaces to filter by
    repeated Namespace namespaces = 2;
    // The MCP IDs to filter by
    repeated string mcp_ids = 3;
  }
  // Filtering options for the list of functions
  Filters filters = 1;
  // The options for paging through the list of functions
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message MCP {
  string id = 1;
  string url = 2;
}

message ListMCPsResponse {
  // The MCPs that match the filters
  repeated MCP mcps = 1;
  // The options for paging through the list of functions
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}
