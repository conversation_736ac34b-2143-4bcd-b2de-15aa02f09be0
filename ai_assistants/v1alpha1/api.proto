syntax = "proto3";

package ai_assistants.v1alpha1;
option go_package = "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1;ai_assistants";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.ai_assistants.v1alpha1.generated";

import "google/protobuf/empty.proto";

import "ai_assistants/v1alpha1/connection.proto";
import "ai_assistants/v1alpha1/assistant.proto";
import "ai_assistants/v1alpha1/namespace.proto";
import "ai_assistants/v1alpha1/answer.proto";
import "ai_assistants/v1alpha1/prompt.proto";
import "ai_assistants/v1alpha1/goal.proto";
import "ai_assistants/v1alpha1/function.proto";
import "ai_assistants/v1alpha1/common.proto";
import "ai_assistants/v1alpha1/integration_tests.proto";
import "ai_assistants/v1alpha1/model.proto";

import "vendasta_types/annotations.proto";
import "vendasta_types/paging.proto";
import "vendasta_types/field_mask.proto";


message UpsertAssistantRequest {
  message Options {
    // Automatically apply default configurations for the assistant.
    bool apply_defaults = 1;
  }
  // The assistant to upsert.
  Assistant assistant = 1;
  // The options for upserting the assistant.
  Options options = 2;
}

message UpsertAssistantResponse {
  Assistant assistant = 1;
}

message CreateAssistantRequest {
  message Options {
    // Automatically apply default configurations for the assistant.
    bool apply_defaults = 1;
  }
  // The assistant to create.
  Assistant assistant = 1;
  // The options for creating the assistant.
  Options options = 2;
}

message CreateAssistantResponse {
  Assistant assistant = 1;
}

message UpdateAssistantRequest {
  // The assistant to update.
  Assistant assistant = 1;
  // The fields to update on the assistant
  vendastatypes.FieldMask field_mask = 2;
}

message GetAssistantRequest {
  message Options {
    // Whether to skip hydration of goals in the assistant.
    bool skip_goals_hydration = 1;
  }
  string id = 1;
  Namespace namespace = 2;
  // The options for getting the assistant.
  Options options = 3;
}

message GetAssistantResponse {
  Assistant assistant = 1;
}

message GetMultiAssistantRequest {
  message Options {
    // Whether to skip hydration of goals in the assistant.
    bool skip_goals_hydration = 1;
  }
  repeated AssistantKey assistant_keys = 1;
  Options options = 2;
}

message GetMultiAssistantResponse {
  repeated Assistant assistants = 1;
}

message BuildDefaultAssistantRequest {
  string id = 1;
  Namespace namespace = 2;
  AssistantType type = 3;
}

message BuildDefaultAssistantResponse {
  Assistant assistant = 1;
}

message DeleteAssistantRequest {
  string id = 1;
  Namespace namespace = 2;
}

message ListAssistantRequest {
  message Filters {
    Namespace namespace = 1;
    AssistantType type = 2;
  }
  Filters filters = 1;
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message ListAssistantResponse {
  repeated Assistant assistants = 1;
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message UpsertConnectionRequest {
  Connection connection = 1;
}

message GetConnectionRequest {
  string id = 1;
  string connection_type = 2;
  Namespace namespace = 3;
  bool include_assistant_keys = 4;
}

message GetConnectionResponse {
  Connection connection = 1;
}

message DeleteConnectionRequest {
  string id = 1;
  string connection_type = 2;
  Namespace namespace = 3;
}

message ListConnectionsRequest {
  message Filters {
    Namespace namespace = 1;
    AssistantType assistant_type = 2;
  }
  Filters filters = 1;
  vendastatypes.PagedRequestOptions paging_options = 2;
  bool include_assistant_keys = 3;
}

message ListConnectionsResponse {
  repeated Connection connections = 1;
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message ListAllAssistantsAssociatedToConnectionRequest {
  message Filters {
    AssistantType type = 1;
  }

  ConnectionKey connection_key = 1;
  Filters filters = 2;
  vendastatypes.FieldMask field_mask = 3;
}

message ListAllAssistantsAssociatedToConnectionResponse {
  repeated Assistant assistants = 1;
}

message GenerateChatAnswerRequest {
  message Options {
    // When this option is true, all possible citations for knowledge given to the AI will be given. When false, only citations deemed relevant by the AI will be included.
    bool include_all_citations = 1;
    // When this option is true, the request may return with a pending_job_id that can be used to poll GetChatAnswerFunctionExecutionJob for progress results of functions being called until the final answer is given.
    // When this option is false, the response will wait until all functions have been completed before returning a response
    bool enable_async_functions = 2;
    // the max tokens to generate in the answer. If zero, the default will be used instead.
    int64 max_tokens = 3;
  }

  // The key of the Connection that is being used to ask the question. The Assistant associated with this Connection will be used to determine an answer.
  ConnectionKey connection_key = 1;
  // The history of chat messages in order from oldest to latest. The answer will be generated for the latest message. The latest message MUST have the role of User.
  repeated ChatMessage chat_history = 2;
  // The known information about the user asking the question in the chat. This allows the Assistant to communicate with the user using this terms and may affect the Assistants goals.
  ChatUserInfo chat_user_info = 3;
  // The channel on which the chat is taking place. This may affect the behaviour of the Assistant and/or formatting of the answer.
  ChatChannel chat_channel = 4;
  // The options for generating the chat answer
  Options options = 5;
  // The assistant that should be used to generate the chat answer.
  AssistantKey assistant_key = 6;
  // Additional context information for the AI assistant
  ContextInfo context_info = 7;
}

message GenerateChatAnswerResponse {
  // The generated answer
  string answer = 1;
  // The metadata associated with the answer
  repeated KeyValuePair metadata = 2;
  // If pending_job_id is empty, the answer can just be used directly
  // If enable_async_functions is true, a pending_job_id will be returned if an answer is not yet ready
  // It should be used to poll GetChatAnswerFunctionExecutionJob for progress updates through to the final answer
  string pending_job_id = 3;
}

message GetChatAnswerFunctionExecutionJobRequest {
  // The ID of the function execution job
  string id = 1;
}

message GetChatAnswerFunctionExecutionJobResponse {
  // The function execution job
  ChatAnswerFunctionExecutionJob job = 1;
}

message SetAssistantConnectionsRequest {
  message ConnectionState {
    // The key of the Connection that is being associated/disassociated with the Assistant
    ConnectionKey connection_key = 1;
    // Whether the Assistant should be associated or disassociated with the Connection
    bool is_associated = 2;
  }
  // The list of ConnectionStates to associate/disassociate with the Assistant
  repeated ConnectionState association_states = 1;
  // The Assistant to associate/disassociate the Connections with
  AssistantKey assistant_key = 2;
}

message ExecuteFunctionRequest {
  // The key of the assistant that is executing the function
  AssistantKey assistant_key = 1;
  // The key of the function to execute
  FunctionKey function_key = 2;
  // The json arguments to pass to the function
  string arguments = 3;
}

message ExecuteFunctionResponse {
  // The generated output of the function
  // For functions that generate answers, this will be a full answer. For other functions, this may be a status update message.
  string output = 1;
  // Metadata can include any additional information that a function builds
  // There can also be an entry for `result` which is the data that should be given as a toolCall response to open ai
  repeated KeyValuePair metadata = 2;
}

message ListAvailableModelsRequest {
  message Filters {
    // The vendor of the model
    repeated ModelVendor vendor = 1;
    // The type of the model
    repeated ModelType type = 2;
  }
  // The filters for the list of models
  Filters filters = 1;
}

message ListAvailableModelsResponse {
  // The list of models
  repeated Model models = 1;
}


service AssistantService {
  // Create or update an assistant.
  // If the assistant already exists, it will be updated.
  // Use case: Create a new assistant for a business.
  rpc UpsertAssistant(UpsertAssistantRequest) returns (UpsertAssistantResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  };
  // Create an assistant.
  rpc CreateAssistant(CreateAssistantRequest) returns (CreateAssistantResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  };
  // Update an assistant.
  rpc UpdateAssistant(UpdateAssistantRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  };
  // Get a specific assistant.
  rpc GetAssistant(GetAssistantRequest) returns (GetAssistantResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin",
      scope: "ai-assistant:read",
    };
  };
  // Get multiple assistants.
  rpc GetMultiAssistant(GetMultiAssistantRequest) returns (GetMultiAssistantResponse){
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin",
      scope: "ai-assistant:read",
    };
  };
  // Build a default assistant.
  // Use case: Get the default values for an assistant to use when building a new assistant on the frontend.
  rpc BuildDefaultAssistant(BuildDefaultAssistantRequest) returns (BuildDefaultAssistantResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }
  // Delete an assistant.
  // Use case: When a business no longer wants to use an assistant, delete it.
  rpc DeleteAssistant(DeleteAssistantRequest) returns (google.protobuf.Empty);
  // List assistants.
  // Use case: List all assistants that are associated with a specific namespace to show them in a UI.
  rpc ListAssistant(ListAssistantRequest) returns (ListAssistantResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }
 // List all assistants associated to connection.
  // Use case: List all assistants that are associated with a specific connection to show them in a UI.
  rpc ListAllAssistantsAssociatedToConnection(ListAllAssistantsAssociatedToConnectionRequest) returns (ListAllAssistantsAssociatedToConnectionResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }
  // GenerateChatAnswer will generate an answer to the latest message in a given chat conversation
  rpc GenerateChatAnswer(GenerateChatAnswerRequest) returns (GenerateChatAnswerResponse) {
    option (vendastatypes.access) = {
      scope: "ai-assistant.action",
    };
  }
  // GetChatAnswerFunctionExecutionJob will return the status and results of an async function job. This allows updates to be given in a chat while the AI is still working on a final answer.
  rpc GetChatAnswerFunctionExecutionJob(GetChatAnswerFunctionExecutionJobRequest) returns (GetChatAnswerFunctionExecutionJobResponse) {}

  // SetAssistantConnections will associate or disassociate an assistant with a connection
  rpc SetAssistantConnections(SetAssistantConnectionsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }

  // ExecuteFunction executes an AI Function for the Assistant with the provided arguments
  rpc ExecuteFunction(ExecuteFunctionRequest) returns (ExecuteFunctionResponse) {}

  // ListAvailableModels will list all models that are available for use in the assistant
  rpc ListAvailableModels(ListAvailableModelsRequest) returns (ListAvailableModelsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }
}

service ConnectionService {
  // Create or update a connection.
  // If the connection already exists, it will be updated.
  // Use case: Register an instance of a Social Marketing account as a connection.
  rpc UpsertConnection(UpsertConnectionRequest) returns (google.protobuf.Empty) {}
  // Get a specific connection.
  rpc GetConnection(GetConnectionRequest) returns (GetConnectionResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }
  // Delete a connection.
  // Use case: When a Social Marketing account is deleted, remove the connection so that it can no longer be associated with assistants.
  rpc DeleteConnection(DeleteConnectionRequest) returns (google.protobuf.Empty) {}
  // List connections.
  // Use case: List all connections that are associated with a specific namespace to show them in a UI.
  rpc ListConnections(ListConnectionsRequest) returns (ListConnectionsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
      scope: "admin"
    };
  }
}

service IntegrationTestService {
  rpc ListTestCasesByAssistant(ListTestCasesByAssistantRequest) returns (ListTestCasesByAssistantResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc UpsertTestCases(UpsertTestCasesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc DeleteTestCases(DeleteTestCasesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc RunTests(RunTestsRequest) returns (RunTestsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc ListTestRunsByAssistant(ListTestRunsByAssistantRequest) returns (ListTestRunsByAssistantResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc GetTestRun(GetTestRunRequest) returns (GetTestRunResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc CancelTestRun(CancelTestRunRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

message CreatePromptRequest {
  // The ID of the new prompt
  string id = 1;
  // The content of the new prompt
  string content = 2;
  // A human-readable description of the purpose or use of the prompt
  string description = 3;
}

message GetPromptRequest {
  // The ID of the prompt to get
  string id = 1;
}

message GetPromptResponse {
  // The prompt that matches the supplied ID
  Prompt prompt = 1;
}

message GetPromptVersionRequest {
  // The ID of the prompt to get
  string id = 1;
  // The version of the prompt version to get
  string version = 2;
}

message GetPromptVersionResponse {
  // The prompt version that matches the supplied ID and version
  PromptVersion prompt_version = 1;
}

message GetDeployedPromptVersionRequest {
  // The ID of the prompt to get the deployed version for
  string id = 1;
}

message GetDeployedPromptVersionResponse {
  // The requested prompt
  Prompt prompt = 1;
  // The deployed version of the requested prompt
  PromptVersion deployed_prompt_version = 2;
}

message GetMultiDeployedPromptVersionRequest {
  // The IDs of the prompts to get the deployed versions for
  repeated string ids = 1;
}

message GetMultiDeployedPromptVersionResponse {
  // The requested prompts
  repeated Prompt prompts = 1;
  // The deployed versions of the requested prompts
  repeated PromptVersion deployed_prompt_versions = 2;
}

message UpdatePromptRequest {
  // The ID of the prompt to update
  string id = 1;
  // The content of the new prompt version
  string content = 2;
  // A human-readable description of the purpose or use of the prompt
  string description = 3;
}

message DeployPromptRequest {
  // The ID of the prompt to deploy
  string id = 1;
  // The version of the prompt to deploy
  string version = 2;
}

message ListPromptRequest {
  // The options for paging through the list of prompts
  vendastatypes.PagedRequestOptions paging_options = 1;
}

message ListPromptResponse {
  // The list of prompts
  repeated Prompt prompts = 1;
  // The deployed version of each prompt
  repeated PromptVersion deployed_prompt_versions = 2;
  // The metadata for paging through the list of prompts
  vendastatypes.PagedResponseMetadata metadata = 3;
}

message ListPromptVersionsRequest {
  // The ID of the prompt to list versions for
  string id = 1;
  // The options for paging through the list of prompt versions
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message ListPromptVersionsResponse {
  // The list of prompt versions
  repeated PromptVersion prompt_versions = 1;
  // The metadata for paging through the list of prompt versions
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message DeletePromptRequest {
  // The ID of the prompt to delete
  string id = 1;
}

service PromptService {
  // Creates a new prompt and its first version
  rpc Create(CreatePromptRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets a prompt by ID
  rpc Get(GetPromptRequest) returns (GetPromptResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets a specific PromptVersion
  rpc GetVersion(GetPromptVersionRequest) returns (GetPromptVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets the deployed version of a prompt
  rpc GetDeployedVersion(GetDeployedPromptVersionRequest) returns (GetDeployedPromptVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets multiple deployed versions of prompts
  rpc GetMultiDeployedVersion(GetMultiDeployedPromptVersionRequest) returns (GetMultiDeployedPromptVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Creates a new version of a prompt
  rpc Update(UpdatePromptRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Deploys a prompt version
  rpc Deploy(DeployPromptRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Lists all prompts with the deployed versions
  rpc List(ListPromptRequest) returns (ListPromptResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Lists all versions of a prompt
  rpc ListVersions(ListPromptVersionsRequest) returns (ListPromptVersionsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Deletes a prompt
  rpc Delete(DeletePromptRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

message CreatePromptModuleRequest {
  // The ID of the new prompt module
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The name of the new prompt module that will be displayed on the UI
  string name = 3;
  // The description of the new prompt module that will be displayed on the UI
  string description = 4;
  // The content of the new prompt module
  string content = 5;
  // Whether the prompt module is managed outside of its namespace
  // A managed prompt module cannot be edited, it is only updated by the managed configuration
  bool managed = 6;
}

message CreatePromptModuleResponse {
  // The id of the prompt module that was created
  string id = 1;
}

message CreatePromptModuleVersionRequest {
  message Options {
    // Whether to deploy the prompt module version after it is created
    bool should_deploy = 1;
  }
  // The ID of the prompt module
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The content of the new prompt module version
  string content = 3;
  // The options for creating the prompt module version
  Options options = 4;
}

message GetPromptModuleRequest {
  // The ID of the prompt module to get
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
}

message GetPromptModuleResponse {
  // The prompt module that matches the supplied ID
  PromptModule prompt_module = 1;
}

message GetPromptModuleVersionRequest {
  // The ID of the prompt module to get
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The version of the prompt module version to get
  string version = 3;
}

message GetPromptModuleVersionResponse {
  // The prompt version that matches the supplied ID and version
  PromptModuleVersion prompt_module_version = 1;
}

message GetDeployedPromptModuleVersionRequest {
  // The ID of the prompt module to get the deployed version for
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
}

message GetDeployedPromptModuleVersionResponse {
  // The requested prompt module
  PromptModule prompt_module = 1;
  // The deployed version of the requested prompt module
  PromptModuleVersion deployed_prompt_module_version = 2;
}

message GetHydratedDeployedPromptModuleVersionRequest {
  // The ID of the prompt module to get the deployed version for
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The variables that will be used to hydrate the prompt module
  repeated KeyValuePair variables = 3;
}

message GetHydratedDeployedPromptModuleVersionResponse {
  // The requested hydrated content from the deployed prompt module version
  string content = 1;
}

message GetMultiHydratedDeployedPromptModuleVersionRequest {
  // The keys of the prompt modules to get the deployed version for
  repeated PromptModuleKey keys = 1;
  // The variables that will be used to hydrate the prompt modules
  repeated KeyValuePair variables = 2;
}

message GetMultiHydratedDeployedPromptModuleVersionResponse {
  // The requested hydrated content from the deployed prompt module versions
  repeated string contents = 1;
}

message UpdatePromptModuleRequest {
  // The ID of the prompt module to update
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The name of the prompt module that will be displayed on the UI
  string name = 3;
  // The description of the prompt module that will be displayed on the UI
  string description = 4;
}

message DeployPromptModuleRequest {
  // The ID of the prompt module to deploy
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The version of the prompt module to deploy
  string version = 3;
}

message ListPromptModuleRequest {
  message Filters {
    Namespace namespace = 1;
  }
  // Filtering options for the list of prompt modules
  Filters filters = 1;
  // The options for paging through the list of prompt modules
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message ListPromptModuleResponse {
  // The list of prompt modules
  repeated PromptModule prompt_modules = 1;
  // The deployed version of each prompt module
  repeated PromptModuleVersion deployed_prompt_module_versions = 2;
  // The metadata for paging through the list of prompt module
  vendastatypes.PagedResponseMetadata metadata = 3;
}

message ListPromptModuleVersionsRequest {
  // The ID of the prompt module to list versions for
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
  // The options for paging through the list of prompt module versions
  vendastatypes.PagedRequestOptions paging_options = 3;
}

message ListPromptModuleVersionsResponse {
  // The list of prompt module versions
  repeated PromptModuleVersion prompt_module_versions = 1;
  // The metadata for paging through the list of prompt module versions
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message DeletePromptModuleRequest {
  // The ID of the prompt module to delete
  string id = 1;
  // The namespace of the prompt module
  Namespace namespace = 2;
}

service PromptModuleService {
  // Creates a new prompt module and its first version
  rpc Create(CreatePromptModuleRequest) returns (CreatePromptModuleResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Creates a new version of a prompt module
  rpc CreateVersion(CreatePromptModuleVersionRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets a prompt module by ID and namespace
  rpc Get(GetPromptModuleRequest) returns (GetPromptModuleResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets a specific PromptModuleVersion
  rpc GetVersion(GetPromptModuleVersionRequest) returns (GetPromptModuleVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets the deployed version of a prompt module
  rpc GetDeployedVersion(GetDeployedPromptModuleVersionRequest) returns (GetDeployedPromptModuleVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets the hydrated content of a deployed version of a prompt module
  rpc GetHydratedDeployedVersion(GetHydratedDeployedPromptModuleVersionRequest) returns (GetHydratedDeployedPromptModuleVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Gets multiple hydrated deployed versions of prompt modules
  rpc GetMultiHydratedDeployedVersion(GetMultiHydratedDeployedPromptModuleVersionRequest) returns (GetMultiHydratedDeployedPromptModuleVersionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Updates the prompt module name and/or description
  rpc Update(UpdatePromptModuleRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Deploys a prompt module version
  rpc Deploy(DeployPromptModuleRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Lists all prompt modules with the deployed versions
  rpc List(ListPromptModuleRequest) returns (ListPromptModuleResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Lists all versions of a prompt module
  rpc ListVersions(ListPromptModuleVersionsRequest) returns (ListPromptModuleVersionsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Deletes a prompt module
  rpc Delete(DeletePromptModuleRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

// DEPRECATED: Upsert RPC is deprecated, use create or update instead
message UpsertGoalRequest {
  // The goal to upsert
  Goal goal = 1 [deprecated=true];
}

message CreateGoalRequest {
  // The goal to create
  Goal goal = 1;
}

message CreateGoalResponse {
  // The ID of the created goal
  string id = 1;
}

message UpdateGoalRequest {
  // The goal to update
  Goal goal = 1;
}

message GetGoalRequest {
  // The ID of the goal to get
  string id = 1;
  // The namespace of the goal
  Namespace namespace = 2;
}

message GetGoalResponse {
  // The goal that matches the supplied ID
  Goal goal = 1;
}

message GetMultiGoalRequest {
  // The keys of the goals to get
  repeated GoalKey keys = 1;
}

message GetMultiGoalResponse {
  // The goals that match the supplied keys
  repeated Goal goals = 1;
}

message DeleteGoalRequest {
  // The ID of the goal to delete
  string id = 1;
  // The namespace of the goal
  Namespace namespace = 2;
}

message ListGoalsRequest {
  message Filters {
    // Deprecated: Use namespaces instead.
    Namespace namespace = 1 [deprecated=true];
    GoalType type = 2;
    repeated GoalChannel supported_channels = 3;
    repeated Namespace namespaces = 4;
  }
  // Filtering options for the list of goals
  Filters filters = 1;
  // The options for paging through the list of goals
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message ListGoalsResponse {
  // The list of goals
  repeated Goal goals = 1;
  // The metadata for paging through the list of goals
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message GoalsDisabledForAccountGroupRequest {
  string account_group_id = 1;
}

message GoalsDisabledForAccountGroupResponse {
  bool goals_disabled = 1;
}

service GoalService {
  // Deprecated: Use the Create or Update instead.
  rpc Upsert(UpsertGoalRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  // Create a goal
  rpc Create(CreateGoalRequest) returns (CreateGoalResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  // Update a goal
  rpc Update(UpdateGoalRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  // Get a goal
  rpc Get(GetGoalRequest) returns (GetGoalResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc GetMulti(GetMultiGoalRequest) returns (GetMultiGoalResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Delete a goal
  rpc Delete(DeleteGoalRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // List goals
  rpc List(ListGoalsRequest) returns (ListGoalsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Returns true if the account group ID in the request belongs to a group that is excluded from using goals
  rpc GoalsDisabledForAccountGroup(GoalsDisabledForAccountGroupRequest) returns (GoalsDisabledForAccountGroupResponse) {}
}

message UpsertFunctionRequest {
  // The function to upsert
  Function function = 1;
}

message GetFunctionRequest {
  // The ID of the function to get
  string id = 1;
  // The namespace of the function
  Namespace namespace = 2;
}

message GetFunctionResponse {
  // The function that matches the supplied ID
  Function function = 1;
}

message GetMultiFunctionRequest {
  // The keys of the functions to get
  repeated FunctionKey keys = 1;
}

message GetMultiFunctionResponse {
  // The functions that match the supplied keys
  repeated Function functions = 1;
}

message ListFunctionRequest {
  message Filters {
    // Deprecated: Use namespaces instead.
    Namespace namespace = 1 [deprecated=true];
    // The namespaces to filter by
    repeated Namespace namespaces = 2;
  }
  // Filtering options for the list of functions
  Filters filters = 1;
  // The options for paging through the list of functions
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message ListFunctionResponse {
  // The list of functions
  repeated Function functions = 1;
  // The metadata for paging through the list of functions
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message DeleteFunctionRequest {
  // The ID of the function to delete
  string id = 1;
  // The namespace of the function
  Namespace namespace = 2;
}

service FunctionService {
  rpc Upsert(UpsertFunctionRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc Get(GetFunctionRequest) returns (GetFunctionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc GetMulti(GetMultiFunctionRequest) returns (GetMultiFunctionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc List(ListFunctionRequest) returns (ListFunctionResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc Delete(DeleteFunctionRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc ListMCPTools(ListMCPToolsRequest) returns (ListMCPToolsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc UpsertMCP(UpsertMCPRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc ListMCPs(ListMCPsRequest) returns (ListMCPsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}
