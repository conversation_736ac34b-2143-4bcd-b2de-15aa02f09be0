syntax = "proto3";

package ai_assistants.v1alpha1;
option go_package = "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1;ai_assistants";
option java_outer_classname = "AssistantProto";
option java_package = "com.vendasta.ai_assistants.v1alpha1.generated";
import "ai_assistants/v1alpha1/namespace.proto";
import "ai_assistants/v1alpha1/goal.proto";
import "ai_assistants/v1alpha1/common.proto";
import "ai_assistants/v1alpha1/model.proto";

enum VendorModel {
  VENDOR_MODEL_UNSPECIFIED = 0;
  VENDOR_MODEL_OPEN_AI_REALTIME = 1;
  VENDOR_MODEL_DEEPGRAM = 2;
  VENDOR_MODEL_ELEVEN_LABS = 3;
}

message OpenAIRealtimeConfig {
  // The voice ID to use for OpenAI text-to-speech.
  string voice = 1;

  // TurnDetection contains settings for fine tuning VAD (Turn Detection) when using OpenAI's realtime model
  message TurnDetection {
    // How loud the human must speak to trigger the assistant. Valid values are between 0.0 and 1.0. Higher values indicate the human must speak louder.
    double threshold = 1;
    // The number of milliseconds prior to the human party speaking which are included in the audio passed to the assistant.
    int32 prefix_padding = 2;
    // The number of milliseconds of silence which indicates the end of the human party speaking.
    int32 silence_duration = 3;
  }

  TurnDetection turn_detection = 2;
}

message DeepgramConfig {
  string voice = 1;
}

message ElevenLabsConfig {
  string voice = 1;
}

message ModelConfig {
  OpenAIRealtimeConfig openai_realtime_config = 1;
  DeepgramConfig deepgram_config = 2;
  ElevenLabsConfig eleven_labs_config = 3;
}

// The configuration for the assistant.
// This is a object that can be used to store any configuration that the assistant uses when interacting with the user of a determined service.
message Config {
  message InboxConfig {
    // The lead_capture_enabled is a boolean that indicates if the assistant is able to capture leads.
    // Deprecated: Use configurable_goals instead
    bool lead_capture_enabled = 1 [deprecated = true];

    // Additional instructions for the assistant.
    // Deprecated: Use configurable_goals instead
    string additional_instructions = 2 [deprecated = true];
  }

  message VoiceConfig {
    // Vendor model to use for the voice
    VendorModel vendor_model = 1;
    // Configuration for the voice model
    ModelConfig model_config = 2;
  }

  // Deprecated: These fields were superseded by the use of goals
  InboxConfig inbox_config = 1 [deprecated = true];
  // The configuration for the voice model
  VoiceConfig voice_config = 2;
  // The selected model(s) for the assistant
  // Might be more than one if the assistant is, for example, configured to use voice
  repeated Model models = 3;
}

enum AssistantType {
  ASSISTANT_TYPE_UNSPECIFIED = 0;
  // Admin assistant. Eg. the current Aurora
  ASSISTANT_TYPE_SYSTEM = 1;
  // Inbox assistant. Eg. Jamie
  ASSISTANT_TYPE_INBOX = 2;
  // Social marketing assistant. Eg. Simone
  ASSISTANT_TYPE_SOCIAL_MARKETING = 3;
  // Voice receptionist assistant.
  ASSISTANT_TYPE_VOICE_RECEPTIONIST = 4;
  // Custom assistant
  ASSISTANT_TYPE_CUSTOM = 5;
  // Sales Coach/Assistant
  ASSISTANT_TYPE_SALES_COACH = 6;
  // Reputation management Review assistant
  ASSISTANT_TYPE_REPUTATION_MANAGEMENT_REVIEW_AGENT = 7;
}

message AssistantKey {
  // ID of the assistant
  string id = 1;
  // Namespace that the assistant belongs to
  Namespace namespace = 2;
}

message ConfigurableGoal {
  // The goal that is configurable
  // Only the goal key needs to be set on writes and the entire goal will be returned on fetches
  Goal goal = 1;

  // The configuration is a list of key-value pairs that are used to configure the goal
  // Configuring the goal means injecting variables to the prompt(s) associated with the goal on runtime
  repeated KeyValuePair configuration = 2;
}

message Assistant {
  // ID of the assistant.
  // Must be unique within the namespace.
  string id = 1;
  // Namespace that the assistant belongs to. Can be set only on create.
  Namespace namespace = 2;
  // Name is the name of the assistant.
  // This is the name that will show in the ui whenever the assistant is interacting with something and must be identified.
  // For example: "Aurora", "Jamie", "Siri"
  string name = 3;

  // The type of the assistant.
  AssistantType type = 4;

  // The avatar url for the assistant. Rendered in UIs and shown to users.
  // If not provided, a default one will be chosen. Can be modified later.
  string avatar_url = 5;

  // The configuration for the assistant.
  Config config = 6;

  // The goals that the assistant is associated with and their configuration
  repeated ConfigurableGoal configurable_goals = 7;

  // The IAM user ID of the service account that is associated with the assistant. Used to authenticate platform requests made by the assistant.
  string user_id = 8;
}
