syntax = "proto3";

package ai_assistants.v1alpha1;
option go_package = "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1;ai_assistants";
option java_outer_classname = "AnswerProto";
option java_package = "com.vendasta.ai_assistants.v1alpha1.generated";
import "ai_assistants/v1alpha1/common.proto";

enum ChatMessageRole {
  CHAT_MESSAGE_ROLE_INVALID = 0;
  CHAT_MESSAGE_ROLE_USER = 1;
  CHAT_MESSAGE_ROLE_ASSISTANT = 2;
}

message ChatContent {
  oneof content_type {
    string text = 1;
    ImageContent image = 2;
  }
}

message ImageContent {
  // URL of the image
  string url = 1;
  // Optional: MIME type of the image (e.g., "image/jpeg", "image/png", "image/gif", "image/webp")
  // If not provided, the server will attempt to determine it from the URL or by fetching headers
  string media_type = 2;
  // Optional: Alternative text description for accessibility
  string alt_text = 3;
}

message ChatMessage {
  // The role of the chat message sender
  <PERSON><PERSON><PERSON><PERSON>ageR<PERSON> role = 1;
  // Deprecated: Use content_parts instead for structured content support
  string content = 2 [deprecated=true];
  // Structured content parts supporting text and images
  repeated ChatContent content_parts = 3;
}

message ChatUserInfo {
  // The name of the user
  string name = 1;
  // The email of the user
  string email = 2;
  // The phone number of the user
  string phone = 3;
  // The address of the user
  string address1 = 4;
  // The additional address line of the user (i.e. Suite or Apt number)
  string address2 = 5;
  // The city of the user
  string city = 6;
  // The state of the user
  string state = 7;
  // The country of the user
  string country = 8;
  // The zip/postal code of the user
  string zip_code = 9;
  // The timezone of the user
  string timezone = 10;
}

// ContextInfo holds additional context information for the AI assistant
message ContextInfo {
  // Variables can include any additional context information that will be used in building the Context Goal
  repeated KeyValuePair variables = 1;
}

enum ChatChannel {
  CHAT_CHANNEL_INVALID = 0;
  // CHAT_CHANNEL_UNSPECIFIED indicates that no special considerations need to be taken for the method in which this chat has been conducted
  CHAT_CHANNEL_UNSPECIFIED = 1;
  // CHAT_CHANNEL_WEB indicates that the chat is being conducted in a browser or mobile app. This allows for certain formatting, such as markdown.
  CHAT_CHANNEL_WEB = 2;
  // CHAT_CHANNEL_SMS indicates that the chat is being conducted over SMS text messages. This will enforce shorter messages and not allow markdown.
  CHAT_CHANNEL_SMS = 3;
  // CHAT_CHANNEL_WHATSAPP indicates that the chat is being conducted over WhatsApp. This allows for certain formatting, such as a subset of markdown.
  CHAT_CHANNEL_WHATSAPP = 4;
  // CHAT_CHANNEL_FACEBOOK indicates that the chat is being conducted over Facebook Messenger. This allows for certain formatting, such as a subset of markdown.
  CHAT_CHANNEL_FACEBOOK = 5;
  // CHAT_CHANNEL_INSTAGRAM indicates that the chat is being conducted over Instagram. This allows for certain formatting, such as a subset of markdown.
  CHAT_CHANNEL_INSTAGRAM = 7;
  // CHAT_CHANNEL_AUTOMATION indicates that the chat request is being made by an Automation. This does not allow for a back and forth chat.
  CHAT_CHANNEL_AUTOMATION = 8;
  // CHAT_CHANNEL_PLATFORM_CHAT indicates that the chat is being conducted over the platform chat.
  CHAT_CHANNEL_PLATFORM = 9;
  // CHAT_CHANNEL_SNAPSHOT indicates that the chat is being conducted for a snapshot report.
  CHAT_CHANNEL_SNAPSHOT = 10;
}

enum ChatAnswerFunctionExecutionJobStatus {
  FUNCTION_EXECUTION_JOB_STATUS_INVALID = 0;
  // FUNCTION_EXECUTION_JOB_STATUS_QUEUED indicates that the job is queued to run
  FUNCTION_EXECUTION_JOB_STATUS_QUEUED = 1;
  // FUNCTION_EXECUTION_JOB_STATUS_QUEUED indicates a currently running job
  FUNCTION_EXECUTION_JOB_STATUS_RUNNING = 2;
  // FUNCTION_EXECUTION_JOB_STATUS_COMPLETE indicates that the job is complete
  FUNCTION_EXECUTION_JOB_STATUS_COMPLETE = 3;
}

message ChatAnswerFunctionExecutionJobResult {
  // The content is a progress update message or final answer from a chat answer function execution job
  string content = 1;
  // The metadata associated with the progress update message or final answer
  repeated KeyValuePair metadata = 2;
}

message ChatAnswerFunctionExecutionJob {
  // The ID of the function execution job
  string id = 1;
  // The status of the function execution job
  ChatAnswerFunctionExecutionJobStatus status = 2;
  // The result of the function execution job
  ChatAnswerFunctionExecutionJobResult result = 3;
  // The ID of the next job in the sequence
  string next_job_id = 4;
}
