syntax = "proto3";

package ai_assistants.v1alpha1;

option go_package = "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1;ai_assistants";
option java_outer_classname = "ConnectionProto";
option java_package = "com.vendasta.ai_assistants.v1alpha1.generated";

import "ai_assistants/v1alpha1/namespace.proto";
import "ai_assistants/v1alpha1/assistant.proto";

message ConnectionKey {
  // ID of the connection
  string id = 1;
  // Namespace that the connection belongs to
  Namespace namespace = 2;
  // The type of connection
  string connection_type = 3;
}

message Connection {
  // ID of the connection.
  // Must be unique within the namespace and connection_type.
  // Clients choose their own IDs, but are encouraged to use UUIDs,
  // particularly if its possible to have more than one Connection for a namespace+connection_type.
  string id = 1;
  // Namespace that the connection belongs to. Can be set only on create.
  Namespace namespace = 2;
  // Name is the human-readable name of the connection. Shown to users.
  // This is different from connection type name in that you may have multiple connections of the same type and need to differentiate them.
  // For example: "Twilio **********" vs "Twilio **********" which are both of the "Inbox Voice" connection type.
  string name = 3;
  // ConfigurationURL is the url for where this connection can be configured
  // Continuing with the Inbox Voice example, this would be the configuration screen for that Twilio number in Business App.
  // For example: "https://colin-gooding-inc-dot-vbc-demo.appspot.com/account/location/AG-ZVTV5THTQK/settings/inbox"
  // Read-only. Set based on the connection type. If you add a connection type, also add a corresponding configuration_url in the `ai-assistants` microservice.
  string configuration_url = 4;
  // the assistants that the connection is associated with
  repeated AssistantKey assistant_keys = 5;
  // Connection Type is the type of connection. This is not shown to user but can be used for filtering or display purposes.
  // This is often a product identifier like "sm" for social marketing or "webchat" for inbox webchat.
  string connection_type = 6;
  // ConnectionTypeName is the human-readable name of the connection type - eg "Inbox" or "Twilio" or "Social Marketing". Shown to users.
  // Read-only. Set based on the connection type. If you add a connection type, also add a corresponding connection type name in the `ai-assistants` microservice.
  string connection_type_name = 7;
  // The url to an icon for the connection. Rendered in UIs and shown to users.
  // If not provided, the product will choose a default icon.
  // Read-only. Set based on the connection type. If you add a connection type, also add a corresponding icon url in the `ai-assistants` microservice.
  string icon_url = 8;
  // IsConnectionLocked is a boolean that indicates if the connection with assistants is locked from being modified by the user.
  // If true, the connection with the participant is locked and cannot be modified by the user.
  // Read-only. Set based on the connection type. If you add a connection type, and you need to lock the connection
  // add the code to lock the connection in the `ai-assistants` microservice.
  bool is_connection_locked = 9;
  // SupportedAssistantTypes is a list of assistant types that this connection supports.
  repeated AssistantType supported_assistant_types = 10;
}
