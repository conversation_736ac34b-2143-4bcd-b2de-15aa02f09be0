## 0.73.0
- Add `ChatContent` message with structured content support for text and images
- Add `ImageContent` message with `url`, `media_type`, and `alt_text` fields
- Add `content_parts` field to `ChatMessage` for structured content approach
- Deprecate `content` field in `ChatMessage` in favor of structured `content_parts`
- Add graceful migration path from simple string content to structured content

## 0.72.0
- Add `GetMultiAssistant` rpc to `AssistantService`

## 0.71.1
- Add `mcp_id` field to function message

## 0.71.0
- Add `ListMCPTools`, `UpsertMCPRequest`, and `ListMCPs` endpoints

## 0.70.0
- Add `ElevenLabsConfig` to `ModelConfig`
- Add `VENDOR_MODEL_ELEVEN_LABS` to `VendorModel`

## 0.69.1
- Add read-only descriptor to `configuration_url` on `Connection` message
- Add read-only descriptor to `is_connection_locked` on `Connection` message

## 0.69.0
- Add `namespaces` field to `ListFunctionRequest`
- Deprecate `namespace` field

## 0.68.0
- Add function parameters and function content to `TestResult` citation

## 0.67.0
- Add `override_of` to `Goal` message

## 0.66.0
- Include citations and reasoning in `TestResult`

## 0.65.0
- Remove `model` field from `GenerateChatAnswerRequest` options
- Include the model on the `Assistant` on the `config` field
- Add the `ListAvailableModels` rpc

## 0.64.0
- Add `options` to `GetAssistantRequest`

## 0.63.0
- Add search and sort options to `ListTestCasesByAssistant` endpoint
- Add `TerminateTestRun` endpoint

## 0.62.0
- Add `model` field to `GenerateChatAnswerRequest` options

## 0.61.0
- Add `TestRun` Get and List endpoints

## 0.60.0
- Add `CHAT_CHANNEL_SNAPSHOT` to `ChatChannel` enum

## 0.59.0
- Add `GOAL_CHANNEL_SNAPSHOT` to `GoalChannel` enum

## 0.58.0
- Add `namespaces` field to `ListGoalsRequest`
- Deprecate `namespace` field

## 0.57.0
- Switch `TestCase` endpoints to use Assistant keys

## 0.56.0
- Add `TestCase` endpoints for connections

## 0.55.0
- Remove `AccountGroupsForPartnerNamespace` and `AccountGroupsForGroupNamespace` from `Namespace` message

## 0.54.0
- Add `ContextInfo` message with flexible `variables` field using KeyValuePair for extensibility
- Add `context_info` field to `GenerateChatAnswerRequest` message

## 0.53.0
- Add `ReputationManagementReviewAgent` to `AssistantType` enum

## 0.52.0
- Add `field_mask` to `ListAllAssistantsAssociatedToConnectionRequest` message

## 0.51.1
- Add `SalesCoach` to `AssistantType` enum

## 0.50.0
- Add `CreateAssistant` and `UpdateAssistant` to `AssistantService`

## 0.49.0
- Add `timezone` to `ChatUserInfo` message

## 0.48.0
- Add `user_id` to `Assistant` message

## 0.47.0
- Add `auth_strategy` to `Function` message

## 0.46.0
- Add `assistant_key` to `GenerateChatAnswerRequest` message
- Add `CHAT_CHANNEL_PLATFORM` to `ChatChannel` enum

## 0.45.0
- Add `ExecuteFunction` rpc to `AssistantService`

## 0.44.0
- Add `ASSISTANT_TYPE_CUSTOM` to `AssistantType` enum

## 0.43.0
- Add `ai-assistant.action` scope to `GenerateChatAnswer` rpc

## 0.42.0
- Add `GOAL_CHANNEL_AUTOMATION` to `GoalChannel` enum

## 0.41.0
- Add `CHAT_CHANNEL_AUTOMATION` to `ChatChannel` enum

## 0.40.0
- Add `location` field to `FunctionParameter` to distinguish between
  parameters set on request bodies vs URL query parameters

## 0.39.0
- Include the id in the response from `CreatePromptModule`
- Add `CreateGoal` rpc to `GoalService`
- Add `UpdateGoal` rpc to `GoalService`
- Deprecate `UpsertGoal` rpc from `GoalService`

## 0.38.0
- Add `CHAT_CHANNEL_FACEBOOK` to `ChatChannel` enum
- Add `CHAT_CHANNEL_INSTAGRAM` to `ChatChannel` enum

## 0.37.0
- Add `configuration` to `ConfigurableGoal`

## 0.36.0
- Add  `BookingConfig` message
- Add `booking_config` to `InboxConfig` message

## 0.35.0
- Add `max_tokens` option to `GenerateChatAnswerRequest`

## 0.34.0
- Remove `GoalsDisabledForGroup` RPC from `GoalService`
- Add `GoalsDisabledForAccountGroup` RPC to `GoalService`

## 0.33.0
- Rename `assistant_type` to `type` on `BuildDefaultAssistantRequest`

## 0.32.0
- Add `GoalsDisabledForGroup` RPC to `GoalService`

## 0.31.0
- Add `BuildDefaultAssistant` rpc

## 0.30.0
- Add `managed` to `Goal`, `Function`, `PromptModule` and `PromptModuleVersion` messages
- Add `managed` to `CreatePromptModuleRequest` message

## 0.29.0
- Add `FunctionHeaders` to the function definition

## 0.28.0
- Add `VENDOR_MODEL_DEEPGRAM` to `VendorModel` enum
- Add `DeepgramConfig` message
- Add `deepgram_config` to `ModelConfig` message

## 0.27.0
- Add `CHAT_CHANNEL_WHATSAPP` to `ChatChannel` enum

## 0.26.0
- Add `Options` to `UpsertAssistantRequest`

## 0.25.0
- Replace `GoalKey` with `Goal` in `ConfigurableGoal` message
- Replace `FunctionKey` with `Function` in `Goal` message
- Replace `PromptModuleKey` with `PromptModule` in `Goal` message

## 0.24.0
- Add `VoiceConfig` to `Assistant` message

## 0.23.0
- Add `Options` to `CreatePromptModuleVersionRequest`

## 0.22.0
- Add `GoalChannel` enum
- Add `supported_channels` to `Goal` message
- Add `supported_channels` to `ListGoalsRequest` filter
- Add `type` to `ListGoalsRequest` filter
- Remove `context_id` from `SystemNamespace`
- Remove `context_id` from `GlobalNamespace`

## 0.21.0
- Rename `group_id` to `group_path` in `AccountGroupsForGroupNamespace` message.

## 0.20.0
- Add `description` and `deployed` to `PromptModule` message.
- Add `GetDeployedVersion` rpc to `PromptModuleService`

## 0.19.0
- Add `supported_assistant_types` to `Connection`
- Add `assistant_type` to filter for `ListConnections` rpc.

## 0.18.0
- Add `GOAL_TYPE_OBJECTIVE` to `GoalType`

## 0.17.0
- Add `FunctionService` rpcs and messages.
- Add new namespace types: `GlobalNamespace`, `AccountGroupsForPartnerNamespace` and `AccountGroupsForGroupNamespace`.

## 0.16.0
- Add `ASSISTANT_TYPE_VOICE_RECEPTIONIST` to `AssistantType`

## 0.15.0
- Add `deployed` to `Prompt` message.

## 0.14.0
- Add `description` to `Prompt` message.
- Add `description` to `CreatePromptRequest` and `UpdatePromptRequest` messages.

## 0.13.0
- Add `PromptModule` and `PromptModuleVersion` messages
- Add `Goal` message
- Add `PromptModuleKey`, `GoalKey` and `FunctionKey` messages
- Add `GoalService` and `PromptModuleService` services and rpcs
- Add new rpcs request and response messages

## 0.12.0
- Add `SetAssistantConnections` rpc

## 0.11.0
- Add `is_connection_locked` to `Connection` message

## 0.10.0
- Add `ListAllAssistantsForConnection` rpc

## 0.9.0
- Add `AssistantKey` to `Connection` message

## 0.8.0
- Add `GetMultiDeployedVersion` rpc to `PromptService`

## 0.7.0
- Add `GetDeployedVersion` rpc to `PromptService`

## 0.6.0
- Change `List` rpc to also gather the deployed versions

## 0.5.0
- Add `PromptService` service and all related rpcs and messages

## 0.4.0
- Add `GenerateChatAnswer` rpc

## 0.3.0
- Remove `CreateAssistant` rpc
- Add `UpsertAssistant` rpc, `ListAssistants` rpc, `DeleteAssistant` rpc
- Add `Assistant` proto definition

## 0.2.2
- Add `connection_type` parameter to GetConnection and DeleteConnection
- Clarify read only properties of Connections

## 0.2.1
- Remove `partner_id` from `AccountGroupNamespace`

## 0.2.0
- Adding Connection-related endpoints

## 0.1.0
- initial commit of ai-assistants protos
