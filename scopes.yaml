# Please add any new scopes in alphabetical order
#
# name - is human-readable name of the access scope. It will be shown to the end-user on Consent Grant screens as a title.
# description - is should tell users what they are letting the app do on their behalf.
# See scopes.md for more details

# This scope is part of the OAuth2 Open ID Connect standard
address:
  name: "View Address"
  description: "Allows read-only access on this account"

admin:
  name: "Administration"
  description: "View and manage partner resources in the Vendasta Platform"

# Used by the Advertising Intelligence REST API
advertising:
  name: "Advertising"
  description: "Allow the application to view and manage advertising reports for your business(es)"

ai-assistant.action:
  name: "AI Assistant Action"
  description: "Allows the application to perform actions via an AI Assistant"

ai-assistant:read:
  name: "AI Assistant Action - Read Only"
  description: "Allows the application read only access to your AI Assistants"

automation:
  name: "Automation"
  description: "Allow the application to manage running automations on your account"

automation:read:
  name: "Automation - Read Only"
  description: "Allows read-only access of automations"

business:
  name: "Business"
  description: "Allows user edit access of business profiles"

business:read:
  name: "Business - Read Only"
  description: "Allows read-only access to business profiles"

# This is a deprecated version of the business:read scope. Don't use it.
"business.read":
  name: "Business - Read Only"
  description: "Read-only access to your business profiles"

campaign.recipient:
  name: "Campaign Recipients"
  description: "Allows user to add and remove recipients from campaigns"

conversation:
  name: "Conversation"
  description: "Allows access to conversations"

conversation:read:
  name: "Conversation - Read Only"
  description: "Allows read-only access to conversations"

conversation.widget:
  name: "Conversation Widget"
  description: "Allows access to conversation widgets"

customers:
  name: "Customers"
  description: "Allows the application to manage the customers for the business location(s) that you manage"

# This scope is part of the OAuth2 Open ID Connect standard
email:
  name: "Email"
  description: "Allows to view your email address"
  reserved: true

eventtriggerdefinition:
  name: "Automation Event Trigger Definition"
  description: "Allows access to event triggers for automations"

eventtriggerdefinition:read:
  name: "Automation Event Trigger Definition - Read Only"
  description: "Allows read-only access to event triggers for automations"

feature-use-tracking:
  name: "Feature Use Tracking"
  description: "Allows automations to administrate feature tracking and submit feature usage events."

financial:
  name: "Financial"
  description: "Allows the application to manage financial details for your business and clients"

listing:
  name: "Manage Business Listings"
  description: "Allows the application to manage all public listings for your client’s business location(s)"

notification-subscription:
  name: "Notification Subscription"
  description: "Allow the application to manage notification subscriptions for your platform users"

notify:
  name: "Send Notifications"
  description: "Allows the application to send notifications to your platform users"

whitelabel-email.send-to-user:
  name: "White-Label Emails: Send to Users"
  description: "Allows emails to be sent to platform users with your white label email address as the sender"

# This scope is part of the OAuth2 Open ID Connect standard
offline_access:
  name: "Offline Access"
  description: "Allows the application to use the requested capabilities, even while the application is not in use"
  reserved: true

# This scope is part of the OAuth2 Open ID Connect standard
openid:
  name: "OpenID Connect"
  description: "Allows the application to identify you using OpenID Connect"
  reserved: true

order:
  name: "Order"
  description: "Allows read-write access to orders"

order:read:
  name: "Order - Read Only"
  description: "Alows read-only access to orders"

proposal:
  name: "Proposal"
  description: "Allows read-write access to proposals"

proposal:read:
  name: "Proposal - Read Only"
  description: "Allows read access to proposals"

package:read:
  name: "Package - Read Only"
  description: "Allows the application read-only access to your packages and their contents"

partner.admin:
  name: "Partner Admin"
  description: "Allows the application to verify your partner admin role"

partner-prospect:read:
  name: "Partner prospects - Read Only"
  description: "Allows the application read-only access to your partner prospects"

partner:read:
  name: "Partner info - Read Only"
  description: "Allows the application read-only access to your platform setup information"

market:
  name: "Market"
  description: "Allows the application to manage markets"

market:read:
  name: "Market - Read Only"
  description: "Allows the application to read markets"

payment-status:
  name: "Payment status"
  description: "Allows the application to read the status of your client payments"

integration-connection:read:
  name: "Integration Connection Data - Read Only"
  description: "Allows the application read-only access to your integration connection data"

# This scope is part of the OAuth2 Open ID Connect standard
phone:
  name: "View your phone number"
  description: "Allows the application to view the phone number on your user profile"

product:
  name: "Product"
  description: "Allows the application edit access to products"

product:read:
  name: "Product - Read Only"
  description: "Allows users read-only access to products"

# This scope is part of the OAuth2 Open ID Connect standard
profile:
  name: "Profile"
  description: "Allows access to your user profile, including your name, locale, and language preferences"
  reserved: true

purchase:
  name: "Purchase"
  description: "Allows the application to perform purchases within the platform"

billing:read:
  name: "Billing - Read Only"
  description: "Allows the application read-only access to billing information"

question:
  name: "Vox Populi Question"
  description: "Allows the application to view and manage Vox Populi questions on your behalf"

# Used by the Reputation Management REST API
reputation:
  name: "Reputation"
  description: "Allows the application to view and manage reputation reports for your clients' business(es)"

reviews:
  name: "Manage reviews"
  description: "Allows the application to manage reviews of the client business location(s) you have access to"

reputation.review-request:
  name: "Review Request"
  description: "Allows the application to send review requests on your behalf"

sales.account:
  name: "Sales Account"
  description: "Allows the application to manage your sales accounts"

sales.business:
  name: "Sales Business"
  description: "Allow the application to perform sales functions (e.g., campaigns) to a business"

sales.contact:
  name: "Sales Contact"
  description: "Allows edit access to sales contact details"

sales.domain:read:
  name: "Sales App Domain - Read Only"
  description: "Allows read-only access on partner's sales app domain"

sales.opportunity:
  name: "Sales Opportunity"
  description: "Allows edit access to partner's sales opportunities"

sales.opportunity:read:
  name: "Sales Opportunity - Read Only"
  description: "Allows read only access to sales opportunities"

salesperson:
  name: "Salesperson"
  description: "Allows read and manage access to a salesperson's profile"

salesperson:read:
  name: "Salesperson - Read Only"
  description: "Allows read-only access to a salesperson's profile"

salesperson-in-same-market:read:
  name: "Salespeople in same market - Read Only"
  description: "Allow the application to lookup salespeople that belong in the same market as you"

self.user.contact:read:
  name: "self.user.contact:read"
  description: "Allows read-only access to the contact info (email, phone, address)"

self.user.admin:
  name: "Administer your user profile"
  description: "Allows the application to edit your profile, contact info and profile image."

shopping-cart:read:
  name: "Shopping cart - Read Only"
  description: "Allows the application read-only access to your client's shopping cart and their contents"

# Currently only used for SSO V3 related endpoints
single-sign-on.identity-provider:
  name: "SSO Identity Provider"
  description: "Allows the application to act as an identity provider for Single Sign On."

snapshot:
  name: "Snapshot"
  description: "Allows the application to view and manage snapshots"

snapshot:read:
    name: "Snapshot - Read Only"
    description: "Allows the application to view snapshots"

# Used by the Social (and other?) REST API
social:
  name: "Social"
  description: "Allows the application to view and manage social media reports & posts for your clients' business(es)"

task:
  name: "Task"
  description: "Allows edit access to tasks and projects"

task:read:
  name: "Tasks - Read Only"
  description: "Allows read-only access to tasks generated by the platform"

taskdefinition:read:
  name: "Automation Task Definition - Read Only"
  description: "Allows read-only access to the task definitions for automations"

taskdefinition:
  name: "Automation Task Definition"
  description: "Allows access to the task definitions for automations"

taskmanager.account:
  name: "Task Manager Account"
  description: "Allows the application to manage your task manager accounts"

template:read:
  name: "Template - Read Only"
  description: "Allows the application to read and render your templates"

user.admin:
  name: "Administer Users"
  description: "Allows the application to manage both client business users and your team"

user.business:
  name: "Business Users"
  description: "Allows the application to manage client business users, including creating them"

user.business:read:
  name: "Business Users - read only"
  description: "Allows the application to read the users associated to the business"

user.contact:read:
  name: "View User Contact Details"
  description: "Allows read access to the contact info (email, phone, address) of all Partner users"

user.permission:
  name: "Manage User Permission"
  description: "Allows read-write access to the permission info (accessible locations, features and roles) of all categories of users"

user.permission:read:
  name: "View User Permissions"
  description: "Allows read access to the permission info (accessible locations, features and roles) of Partner users"

user.profile:read:
  name: "View User Profiles"
  description: "Allows application to view profiles, including name, locale and language preferences for all users"

user.list:
  name: "List Users"
  description: "Allows searching for business client users based on a set of filters. (ex: email, name, category, organization)"

user:read:
  name: "User - Read Only"
  description: "Allows the application read-only access to all of your users in the platform"

user:
  name: "User"
  description: "Allows the application to manage users in the platform"

user.password:
  name: "Update Password"
  description: "Allows the application to update the password of a user"

crm:
  name: "CRM"
  description: "Allows the application to manage all data in the CRM"

crm.activity:
  name: "CRM Activity"
  description: "Allow the application write access to your crm activity"

crm.activity:read:
  name: "CRM Activity - Read Only"
  description: "Allows read-only access of crm activities"

crm.contact:
  name: "CRM Contact"
  description: "Allow the application write access to your crm contact"

crm.contact:read:
  name: "CRM Contact - Read Only"
  description: "Allows read-only access of crm contacts"

crm.company:
  name: "CRM Company"
  description: "Allow the application write access to your crm company"

crm.company:read:
  name: "CRM Company - Read Only"
  description: "Allows read-only access of crm companys"

crm.custom-object:
  name: "CRM Custom Object"
  description: "Allow the application write access to your crm custom objects"

crm.custom-object:read:
  name: "CRM Custom Object - Read Only"
  description: "Allows read-only access of crm custom objects"

crm.opportunity:
  name: "CRM Opportunity"
  description: "Allow the application write access to your crm opportunities"

crm.opportunity:read:
  name: "CRM Opportunity - Read Only"
  description: "Allows read-only access of crm opportunities"

crm.association:
  name: "CRM Association"
  description: "Allow the application write access to associations in your crm"

crm.association:read:
  name: "CRM Association - Read Only"
  description: "Allows the application read-only access to associations in your crm"

crm.schema:read:
  name: "CRM Object Schema - Read Only"
  description: "Allows read-only access of crm object schemas"

integration:
  name: "Integration Admin"
  description: "Allow the application to manage integrations built by your company"

integration.event:
  name: "Integration Event"
  description: "Allow the application to manage your integration events"

business-app:
  name: "Business App"
  description: "Allow the application to manage Business App"

meeting-conversation:read:
  name: "Meeting Conversation - Read Only"
  description: "Allows the application read-only access to meeting conversations"

meeting-conversation:
  name: "Meeting Conversation"
  description: "Allows the application full access to meeting conversations"

sales-coach-config:read:
  name: "Sales Coach Config - Read Only"
  description: "Allows the application read-only access to sales coach configs"

sales-coach-config:
  name: "Sales Coach Config"
  description: "Allows the application full access to sales coach configs"
