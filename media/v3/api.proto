syntax = "proto3";

package media.v3;

option go_package = "github.com/vendasta/generated-protos-go/media/v3;media_v3";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.media.v3.generated";

import "media/v3/files.proto";
import "google/protobuf/empty.proto";

service Files {
  // Create a record for a file in the database, and return an upload URL
  rpc CreateFile(CreateFileRequest) returns (CreateFileResponse);
  // Get metadata for a file
  rpc GetMultiFileMetadata(GetMultiFileMetadataRequest) returns (GetMultiFileMetadataResponse);
  // Delete a record for a file in the database, and remove the file from storage
  rpc DeleteFile(DeleteFileRequest) returns (google.protobuf.Empty);
}
