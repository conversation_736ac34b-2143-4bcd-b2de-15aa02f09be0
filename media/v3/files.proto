syntax = "proto3";

package media.v3;

option go_package = "github.com/vendasta/generated-protos-go/media/v3;media_v3";
option java_outer_classname = "FilesProto";
option java_package = "com.vendasta.media.v3.generated";

import "google/protobuf/timestamp.proto";

message FileIdentifier {
  oneof identifier {
    // file unique identifier
    string file_id = 1;
    // file url most other services will only know a file by its URL
    string file_url = 2;
  }
}

// Request to fetch an embed code of a custom form
message GetMultiFileMetadataRequest {
  // The namespace to use for the request
  string namespace = 1;
  // The identifiers of the files to fetch metadata for
  repeated FileIdentifier file_ids = 2;
}

enum FileAccessType {
  // The file access type is invalid, this should not be used
  INVALID = 0;
  // The file is private and requires authentication to access
  PRIVATE = 1;
  // The file is publicly accessible
  PUBLIC = 2;
}

message FileMetadata {
  // The file identifier (unique identifier for the file like FileID-e1eb582f-2883-4cf3-ba32-42a27d69594a)
  string file_id = 1;
  // The file name, required for the file to be created
  string file_name = 2;
  // The optional path of the file, relative to the storage bucket
  string path = 3;
  // The file size in bytes, determined from GCS metadata
  int64 file_size = 4;
  // The content type of the file prefer using MIME types (e.g., "image/png", "application/pdf") fallback to extension if not available
  string content_type = 5;
  // The URL to access the file
  string file_url = 6;
  // The access to the file
  FileAccessType access_type = 7;
  // The source of the file, e.g., "user_upload", "system_generated", "meetings-analysis"
  string source = 8;
  // The timestamp when the file was created
  google.protobuf.Timestamp created = 9;
  // The timestamp when the file was last updated
  google.protobuf.Timestamp updated = 10;
  // The timestamp when the file was deleted, if applicable
  google.protobuf.Timestamp deleted = 11;
}

// Response containing metadata for the requested files
message GetMultiFileMetadataResponse {
  // The metadata for the requested files
  repeated FileMetadata file_metadata = 1;
}

// The CreateFileRequest message is used to create a file in the system
// file uploading
message CreateFileRequest {
  // The namespace to use for the request
  string namespace = 1;
  // The file metadata to create the file with
  FileMetadata file_metadata = 2;
}

// The CreateFileResponse message is used to return the file upload url
message CreateFileResponse {
  // The signed_url used for uploading file
  string signed_url = 1;
}

// The DeleteFileRequest message is used to delete a file in the system
message DeleteFileRequest {
  // The namespace to use for the request
  string namespace = 1;
  // The file identifier to delete
  FileIdentifier file_id = 2;
}
