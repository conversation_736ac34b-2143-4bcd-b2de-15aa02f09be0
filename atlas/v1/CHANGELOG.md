## 1.65.0
- Add `entitlment_id` field to `GetDataRequest` message
- Add `should_display_pam` field to `GetDataResponse` message

## 1.64.0
- Add `disable_product_switcher` to `GetNavigationDataResponse`

## 1.63.0
- Add ExitLinkConfiguration data to `Branding` in `GetNavigationDataResponse`

## 1.62.0
- Add `business_app_ui_theme` field to `Branding` and `GetDataResponse`

## 1.61.0
- Add `UI_THEME_USER_PREFERENCE` as an option for `UITheme` in Atlas' `GetNavigationData` branding sub object.

## 1.60.0

- Add `dark_mode_logo_url` to `Branding`

## 1.59.0

- Add `platform_mode` to `GetNavigationDataRequest`

## 1.58.0

- Add `description_translation_id` to `SideNavigationLink`

## 1.57.0

- Add `partner_name` to `GetDataResponse` and `entry_url` to `UserSwitcherData`

## 1.56.0

- Add `UserSwitcherData' to `GetDataResponse`

## 1.55.0

- Add `launch_url` to `SidenavigationLink` message

## 1.54.0

- Add `sublinks` to `SidenavigationLink` message

## 1.53.0

- Add `email_verified` to `GetDataResponse`

## 1.52.0

- Add `open_in_new_tab` to `SideNavigationLink`

## 1.51.0

- Add `navigation_id` to `SideNavigationContainer`

## 1.50.0

- Add `pinnable` to `SideNavigationContainer`

## 1.49.0

- Add `url` to the `SideNavigationContainer`

## 1.48.0

- Remove deprecated fields `GetNavigationData.account_items` and `GetNavigationData.brand_items`

## 1.47.0

- mark `GetNavigationData.account_items` and `GetNavigationData.brand_items` as deprecated
  - add their replacement field: `GetNavigationData.navigation_items`

## 1.46.0

- Add `meeting_booking_url` to SalesContact

## 1.45.0

- Deprecate `theming` and un-deprecate `theme`
  - Theming is a feature not released to our partners, we are no longer supporting it

## 1.44.0

- Added the option to disable the business nav for partners that don't use business center

## 1.43.0

- Remove the (just added) Get and Set DefaultAccount RPCs, and instead add `GetDefaultLocation` to match the existing `SetDefaultLocation`
- Add the ability to pass a group ID to the `SetDefaultLocation` (as a one-of between the existing `account_group_id` and `group_id`)

## 1.42.0

- Add Get and Set DefaultAccount rpc

## 1.41.0

- Add ListLocations

## 1.40.0

- Pull `GetSalesInfo` out of `GetNavigationData` into it's own service

## 1.37.0

- Add `impersonatee_username` to GetDataResponse

## 1.36.0

- Add `market_name` to GetNavigationDataResponse

## 1.35.0

- Add `country`to the SalesContact message.

## 1.34.0

- Add `cobranding_url`to the GetNavigationDataResponse message.

## 1.33.0

- Add user_required to SideNavigationLink

## 1.32.0

- Add `user_id` to the GetNavigationDataResponse

## 1.31.0

- Removes the now unused Set/Get language for user RPCs and associated messages

## 1.30.0

- Add is_trial to SideNavigationLink to indicate if an activation is a trial

## 1.29.0

- Use a location data message to contain data about the location that is loaded by Atlas

## 1.28.0

- If an account group is being used for the GetData request, the business name will be returned.

## 1.27.0

- Blacklisted pins removed

## 1.26.0

- Deprecate pinned_items on the GetNavigationDataResponse message.
- Add pinned_items_blacklist on the GetNavigationDataResponse message.

## 1.25.0

- Deprecate SetPins and GetPins.
- Add SetPinBlacklist and GetPinBlacklist rpcs.

## 1.24.0

- Add TotalLocations to the GetNavigationDataResponse rpc

## 1.23.0

- Add ListElevatedLocations rpc

## 1.22.0

- Add SetLanguageForUser rpc

## 1.21.0

- Add GetLanguageForUser rpc

## 1.20.0

- Add `user_view` to the get navigation data endpoint

## 1.19.0

- Add `user_id` to the get data endpoint

## 1.18.0

- Add `notification_enabled` to the get data endpoint

## 1.17.0

- Add `current_brand_name` to GetNavigationDataResponse. Will use this to populate the brand navigation links

## 1.16.0

- Add `group_path` to GetDataRequest

## 1.15.0

- Add chip content to SideNavigation sections, links, and containers

## 1.14.0

- Add a pinnable flag

## 1.13.0

- Add show icon flag for menu items

## 1.12.0

- Add SetDefaultLocationRequest + rpc

## 1.11.0

- Return product logo for a link

## 1.10.0

- Return product logo

## 1.9.0

- Add Account Group and Brands urls

## 1.8.0

- Add Get Multi Locations

## 1.7.0

- Add partner name and business center whitelabel name to branding

## 1.6.0

- Add `label` to navigation items, and change brand_name to market_name

## 1.5.0

- Change items to `account_items`, add `brand_items` field

## 1.4.0

- Add `external` bool field to the `SideNavigationLink` message

## 1.3.0

- Add the subject's email to the get data endpoint

## 1.2.0

- Add the subject's email to the get data endpoint

## 1.1.0

- Add center_id to the CenterNavigationItem

## 1.0.0

- Initial commit of Atlas protos
