syntax = "proto3";

package atlas.v1;

option go_package = "github.com/vendasta/generated-protos-go/atlas/v1;atlas_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.atlas.v1.generated";

import "google/protobuf/empty.proto";
import "vendasta_types/annotations.proto";

enum UITheme {
    UI_THEME_DARK = 0;
    UI_THEME_LIGHT = 1;
    UI_THEME_USER_PREFERENCE = 2;
}

enum UserViewType {
    USER_VIEW_TYPE_SMB = 0;
    USER_VIEW_TYPE_ADMIN = 1;
}

enum PlatformMode{
   WEB = 0;
   MOBILE = 1;
}

// Represents a User navigation item in the Atlas navbar
message UserNavigationItem {
    // The text of a navigation item displayed in the Atlas navbar
    string text = 1;
    // The url to navigate to upon clicking this navigation item
    string url = 2;
    // Optionally, this will be emitted from Atlas if the Atlas navbar has been initialized as within an Angular app
    string route_id = 3;
}

// Represents a Center navigation item in the Atlas navbar
message CenterNavigationItem {
    // The name of a center navigation item displayed in the Atlas navbar
    string name = 1;
    // Entry URL
    string entry_url = 2;
    // The center's unique ID
    string center_id = 3;
}

// Request for Atlas.GetData
message GetDataRequest {
    // A partner ID
    string partner_id = 1;
    // A market ID
    string market_id = 2;
    // An account group ID
    string account_group_id = 3;
    // The next url the user will be taken to when logout is clicked
    string sign_out_next_url = 4;
    // A brand's group path
    string group_path = 5;
    // The ID of the entitlement required by PAM
    string entitlement_id = 6;
}

message LocationData {
    // The name of the business
    string business_name = 1;
    // The business address
    string address = 2;
}

message UserSwitcherData{
  // The user's identifier
  string user_id = 1;
  // The partner ID
  string partner_id = 2;
  // Full name of the partner
  string partner_name = 3;
  // URL to switch to the specified user
  string entry_url = 4;
}

// Response for Atlas.GetData
message GetDataResponse {
    // Navigation items for the user dropdown
    repeated UserNavigationItem user = 1;
    // Navigation items for the centers dropdown
    repeated CenterNavigationItem centers = 2;
    // The username of the user currently logged in
    string username = 3;
    // The email of the user currently logged in
    string email = 4;
    // Sign out url that the user is taken to when they logout
    string sign_out_url = 5;
    // The UI theme for Atlas
    UITheme theme = 6;
    // The user's currently selected language
    string language = 7;
    // Deprecated (use theme): The theming set for the partner and market
    Theming theming = 8 [deprecated = true];
    // Denotes whether or not the notifications bell is enabled
    bool notifications_enabled = 9;
    // The user's identifier
    string user_id = 10;
    // If a business or brand was requested, the location data will be populated
    LocationData location_data = 11;
    // The username of the impersonatee if the current user is impersonating
    string impersonatee_username = 12;
    // Whether the currently logged in user's email address is verified
    bool email_verified = 13;
    // List of users that the current user can switch to
    repeated UserSwitcherData user_switcher_data  = 14;
    // The name of the current partner
    string partner_name = 15;
    // The theme for business app
    UITheme business_app_ui_theme = 16;
    // Denotes whether or not the PAM dialog should be displayed
    bool should_display_pam = 17;
}

// Request for Atlas.GetNavigationData
message GetNavigationDataRequest {
    // An account group ID
    string account_group_id = 1;
    // A brand's group path
    string group_path = 2;
    // The partner ID
    string partner_id = 3;
    // The market ID
    string market_id = 4;
    // Platform mode whether is mobile or web version
    PlatformMode platform_mode = 5;
}

// Request for Atlas.GetSalesInfoRequest
message GetSalesInfoRequest {
  // An account group ID
  string account_group_id = 1;
  // A brand's group path
  string group_path = 2;
}

// Response for Atlas.GetSalesInfoResponse
message GetSalesInfoResponse {
  // The sales information
  SalesInfo sales_info = 3;
}


// The Container Item for the Side Navigation Bar
message SideNavigationSection {
    // The id of the translation to use
    string translation_id = 1;
    // A repeated item for the side nav
    repeated SideNavigationItem side_navigation_items = 2;
    // the default label in the case that a translation id does not exist
    string label = 3;
    // Any text content to be shown in a chip on the item
    string chip_content = 4;
}

// The Container Item for the Side Navigation Bar
message SideNavigationContainer {
    // The id of the translation to use
    string translation_id = 1;
    // A repeated item for the side nav
    repeated SideNavigationItem side_navigation_items = 2;
    // The icon of the container
    string icon = 3;
    // The logo url for the products icon
    string logo_url = 4;
    // the default label in the case that a translation id does not exist
    string label = 5;
    // Whether or not the icon be shown
    bool show_icon = 6;
    // Any text content to be shown in a chip on the item
    string chip_content = 7;
    // A URL that can be accessed by clicking on the right side of the container
    string url = 8;
    // Whether or not a link is pinnable
    bool pinnable = 9;
    // The navigation ID
    string navigation_id = 10;
}

// The navigation link
message SideNavigationLink {
    // The navigation ID
    string navigation_id = 1;
    // The place the link takes you
    string url = 2;
    // The path that angular would navigate you to, assuming this link takes you to a route in the angular app
    string path = 3;
    // The service provider ID that owns this link
    string service_provider_id = 4;
    // The logo url for the products icon
    string logo_url = 5;
    // The name of the material icon to use
    string icon = 6;
    // The ID for the translation of this Link
    string translation_id = 7;
    // Whether or not this link is for an external page
    bool external = 8;
    // the default label in the case that a translation id does not exist
    string label = 9;
    // Whether or not the icon be shown
    bool show_icon = 10;
    // Whether or not a link is pinnable
    bool pinnable = 11;
    // Any text content to be shown in a chip on the item
    string chip_content = 12;
    // A flag to indicate whether an activation is a trial
    bool is_trial = 13;
    // A flag to show an auth wall when navigation is attempted for items that require a user on the account group
    bool user_required = 14;
    // A flag to indicate if we should open a new tab when clicked
    bool open_in_new_tab = 15;
    // Subrouted links
    repeated SideNavigationLink sub_links = 16;
    // This url is used as the navigation target for a launch button on the links respective page.
    string launch_url = 17;
    // The ID for the translation of this Link's description
    string description_translation_id = 18;
}

// The navigation link for atlas' dropdown
message DropdownItem {
    // The place the link takes you
    string url = 1;
    // The path that angular would navigate you to, assuming this link takes you to a route in the angular app
    string path = 2;
    // The ID for the translation of this Link
    string translation_id = 3;
    // the default label in the case that a translation id does not exist
    string label = 4;
}

message SideNavigationItem {
    // The actual item
    oneof item {
        // It could be a section
        SideNavigationSection side_navigation_section = 1;
        // It could also be a container
        SideNavigationContainer side_navigation_container = 2;
        // It could also just be a normal navigation link
        SideNavigationLink side_navigation_link = 3;
    }
}

message SalesInfo {
    // The market name
    string market_name = 1;
    // The sales contact information
    SalesContact sales_contact = 2;

}

message SalesContact {
    // The salesperson id
    string sales_person_id = 1;
    // The salesperson's email
    string email = 2;
    // The salesperson's first name
    string first_name = 3;
    // The salesperson's last name
    string last_name = 4;
    // The salesperson's phone number
    string phone_number = 5;
    // The salesperson's secure photo URL
    string photo_url_secure = 6;
    // The salesperson's job title
    string job_title = 7;
    // The salesperson's country
    string country = 8;
    // The user's meeting booking url
    string meeting_booking_url = 9;

}

// A representation of a brand
message Brand {
    // The name of the brand
    string name = 1;
    // The path nodes of the brand
    repeated string path_nodes = 2;
    // Whether or not access is granted
    bool has_access = 3;
    // The url to the brand
    string url = 4;
}

// An item which would be pinned in the sidenav
message PinnedItem {
    // The ID of the pinned navigation item
    string navigation_id = 1;
}

message ExitLinkConfiguration {
  // the text to display for the exit link (back to dashboard)
  string exit_link_text = 1;
  // the url to redirect to when the exit link is clicked (https://sso.your-identity-provider.com)
  string exit_link_url = 2;
}

// Branding contains the major visual data for a branded partner/market context
message Branding {
    // Major theme of the UI -- Dark/Light
    UITheme theme = 1;
    // The URL of the partner's logo
    string logo_url = 2;
    // The partner's name
    string partner_name = 3;
    // The whitelabeled business center name
    string center_name = 4;
    // The theming set for the partner and market
    Theming theming = 5 [deprecated = true];
    // The cobranding-logo url to show for the tiers with cobranding
    string cobranding_logo_url = 6;
    // market name
    string market_name = 7;
    // Optional dark mode logo, used when theme is set to dark mode
    string dark_mode_logo_url = 8;
    // The business app ui theme
    UITheme business_app_ui_theme = 9;
    // The exit link configuration
    ExitLinkConfiguration exit_link_configuration = 10;
}

// A representation of the information on an account group that we're interested in
message AccountGroup {
    // The ID of the account group
    string account_group_id = 1;
    // The name of the account group
    string name = 2;
    // The address of the account group
    string address = 3;
    // The list of activated aproducts on the account group
    repeated string activated_product_ids = 4;
    // The url to the account group
    string url = 5;
}

// A location is either an account group or a brand
message Location {
    oneof location {
        AccountGroup account_group = 1;
        Brand brand = 2;
    }
}

// The list of associated ids
message AssociatedLocationIDs {
    repeated string account_group_ids = 1;
    repeated string group_paths = 2;
}

// Theming values set for a partner and market
message Theming {
    // primary color and variations
    string primary_color = 1;
    string primary_hover_color = 2;
    string primary_active_color = 3;
    // secondary color and variations
    string secondary_color = 4;
    string secondary_hover_color = 5;
    string secondary_active_color = 6;
    // font color
    string font_color = 7;
    string font_disabled_color = 8;
    // accents color and variation, accents are things like the pin icons in the navigation
    string accents_color = 9;
    string accents_active_color = 10;
    // focus color is the target of something that has focus in the current context, i.e. the link to the page you're
    // currently on has this color as its background in the side navigation
    string focus_color = 11;
    // the color of borders
    string border_color = 12;
}

message RetentionConfig {
    // The email address to email if the business would like to cancel
    string cancellation_notification_email = 1;
}

message TotalLocations {
    // the total number of accounts available to the user
    int64 accounts = 1;
    // the total number of brands available to the user
    int64 brands = 2;
}

// Response for Atlas.GetNavigationData
message GetNavigationDataResponse {
    reserved 1,7;
    // The partner's whitelabel branding data
    Branding branding = 2;
    // The sales information
    SalesInfo sales_info = 3;
    // The list of pinned products
    repeated PinnedItem pinned_items = 4;
    // The list of associated account groups
    AssociatedLocationIDs associated_location_ids = 5;
    // The default location (account_group_id)
    string default_location = 6;
    // The user's currently selected language
    string language = 8;
    // Dropdown buttons to place in atlas' dropdown menu
    repeated DropdownItem dropdown_items = 9;
    // The currently requested brand's name. Used for navigation purposes
    string current_brand_name = 10;
    // The type of view the user has
    UserViewType user_view = 11;
    // The configuration for how the business can contact the partner's retention team
    RetentionConfig retention_config = 12;
    // The total number of Accounts and Brands the user has
    TotalLocations total_locations = 13;
    // The user's identifier
    string user_id = 14;
    // Sets partner branding to the style of the "Business App" initiative
    bool business_app_branding = 15;
    // Determines whether or not the business side navigation should be visible.
    // Partners who don't use Business Nav don't want their users going to business nav from
    // the products they do serve.
    bool disable_business_nav = 16;
    // the nav items to display to the user
    repeated SideNavigationItem navigation_items = 17;
    // Determines whether or not the product switcher should be visible.
    bool disable_product_switcher = 18;
}

// Request for PinsService.SetPins
message SetPinsRequest {
    // Identifies a pin set, this could be a brand id or a business id
    string identifier = 1;
    // An ordered list of the updated state of pinned navigation items
    repeated PinnedItem items = 3;
}

// Request for PinsService.GetPins
message GetPinsRequest {
    // Identifies a pin set, this could be a brand id or a business id
    string identifier = 1;
}

// Response for PinsService.SetPins
message GetPinsResponse {
    // An ordered list of the pinned navigation items
    repeated PinnedItem items = 1;
}

// Request for Atlas.GetLocations
message GetLocationsRequest {
    message AccountGroups {
        repeated string account_group_ids = 1;
    }
    message Groups {
        repeated string group_paths = 1;
    }
    oneof identifier {
        AccountGroups account_groups = 1;
        Groups groups = 2;
    }
}

// Response for Atlas.GetLocations
message GetLocationsResponse {
    repeated Location locations = 1;
}

// Response for Atlas.ListElevatedLocations
message ListElevatedLocationsRequest {
    string partner_id = 1;
    string cursor = 2;
    int64 page_size = 3;
    string search = 4;
    oneof type {
        bool account_groups = 5;
        bool brands = 6;
    }
}

// Response for Atlas.ListElevatedLocations
message ListElevatedLocationsResponse {
    repeated Location locations = 1;
    string cursor = 2;
    bool has_more = 3;
}

// Response for Atlas.ListLocations
message ListLocationsRequest {
  string partner_id = 2;
  string cursor = 3;
  int64 page_size = 4;
  string search = 5;
  bool include_account_groups = 6;
  bool include_brands = 7;
}

// Response for Atlas.ListLocations
message ListLocationsResponse {
  repeated Location locations = 1;
  string cursor = 2;
  bool has_more = 3;
}

// SetLanguageRequest remember the specified language string
message SetLanguageRequest {
    string language = 1;
}

// GetLanguageRequest get the remembered language for the caller
message GetLanguageRequest {
}

// GetLanguageResponse returns the remembered language
message GetLanguageResponse {
    // the remembered language
    string language = 1;
}

// ContactUsRequest
message ContactUsRequest {
    // The ID of the account group
    string account_group_id = 1;
    // Message sent from the contacter
    string message = 2;
}

// SetDefaultLocationRequest
message SetDefaultLocationRequest {
    // The partner ID that the default location is applicable to.
    string partner_id = 2;
    // The default location - one of either a single or multi location.
    oneof location {
        string account_group_id = 1;
        string group_id = 3;
    }
}

// GetDefaultLocationRequest
message GetDefaultLocationRequest {
    // The partner ID that the default location is applicable to.
    string partner_id = 1;
}

// GetDefaultLocationResponse
message GetDefaultLocationResponse {
    // The default location - one of either a single or multi location.
    oneof location {
        string account_group_id = 1;
        string group_id = 2;
    }
}

// The Atlas service
service Atlas {
    // GetData gets the data that populates the Atlas navbar
    rpc GetData (GetDataRequest) returns (GetDataResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // GetNavigationData gets the navigation data that populates the sidenav
    rpc GetNavigationData (GetNavigationDataRequest) returns (GetNavigationDataResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // GetSalesInfo gets the sales info that populates the sidenav
    rpc GetSalesInfo (GetSalesInfoRequest) returns (GetSalesInfoResponse) {
      option (vendastatypes.access) = {
        scope: "admin"
        scope: "business-app"
      };
  };
    // Deprecated (use ListLocations): GetLocations returns associated account groups or groups
    rpc GetLocations (GetLocationsRequest) returns (GetLocationsResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // Deprecated (use ListLocations): ListElevatedLocations returns a paged list of locations available to an elevated user.
    rpc ListElevatedLocations (ListElevatedLocationsRequest) returns (ListElevatedLocationsResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // ListLocations returns a paged list of locations available to a user.
    rpc ListLocations (ListLocationsRequest) returns (ListLocationsResponse) {
      option (vendastatypes.access) = {
        scope: "admin"
        scope: "business-app"
      };
    }
    // ContactUs notifies the platform that the user is interested in contacting the partner
    rpc ContactUs (ContactUsRequest) returns (google.protobuf.Empty) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // SetDefaultLocationRequest sets a user's default location for the given partner.
    rpc SetDefaultLocation (SetDefaultLocationRequest) returns (google.protobuf.Empty) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // GetDefaultLocation` gets a user's default location that they have set for the given partner.`
    rpc GetDefaultLocation(GetDefaultLocationRequest) returns (GetDefaultLocationResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
}

// The service for managing pinned products and navbar items
service Pins {
    // SetPins will set the pins for a user in an business or brand
    rpc SetPins (SetPinsRequest) returns (google.protobuf.Empty) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // GetPins will return the pins for a business or brand
    rpc GetPins (GetPinsRequest) returns (GetPinsResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
}

// The service for managing your preferred language
service Languages {
    // SetLanguage will remember your currently selected language
    rpc SetLanguage (SetLanguageRequest) returns (google.protobuf.Empty) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
    // GetLanguage will return your currently selected language
    rpc GetLanguage (GetLanguageRequest) returns (GetLanguageResponse) {
        option (vendastatypes.access) = {
          scope: "admin"
          scope: "business-app"
        };
    };
}
