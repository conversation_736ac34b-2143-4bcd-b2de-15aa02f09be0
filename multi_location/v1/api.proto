syntax = "proto3";

package multilocation.v1;

option go_package = "github.com/vendasta/generated-protos-go/multi_location/v1;multilocation_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.multilocation.v1.generated";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/annotations.proto";

message ForeignKeys {
  // The id of the partner that owns the group
  string partner_id = 1;
  // The id of the market that owns the group
  string market_id = 2;
}

message Path {
  // A list of nodes in the path
  repeated string nodes = 1;
}

message TabStatuses {
    // reviews_tab_enabled denotes whether or not the tab is enabled
    bool reviews_tab_enabled = 1;
    // listings_tab_enabled denotes whether or not the tab is enabled
    bool listings_tab_enabled = 2;
    // social_tab_enabled denotes whether or not the tab is enabled
    bool social_tab_enabled = 3;
    // insights_tab_enabled is deprecated. Use listings_tab_enabled instead.
    bool insights_tab_enabled = 4 [deprecated = true];
    // map_tab_enabled denotes whether or not the tab is enabled
    bool map_tab_enabled = 5;
    // advertising_tab_enabled denotes whether or not the tab is enabled
    bool advertising_tab_enabled = 6;
    // report_tab_enabled denotes whether or not the tab is enabled
    bool report_tab_enabled = 7;
    // data_export_tab_enabled denotes whether or not the tab is enabled
    bool data_export_tab_enabled = 8;
    // website_tab_enabled denotes whether or not the tab is enabled
    bool website_tab_enabled = 9;
    // google_q_and_a_tab_enabled denotes whether or not the tab is enabled
    bool google_q_and_a_tab_enabled = 10;
    // keyword_tracking_tab_enabled is deprecated. Use listings_tab_enabled instead.
    bool keyword_tracking_tab_enabled = 11 [deprecated = true];

}

message FeatureStatuses {
    // request_review_page_enabled denotes whether or not the feature is enabled
    bool request_review_page_enabled = 1;
    // request_review_card_enabled denotes whether or not the feature is enabled
    bool request_review_card_enabled = 2 [deprecated=true];
    // customer_voice_executive_report_enabled denotes whether or not the feature is enabled
    bool customer_voice_executive_report_enabled = 3;
}

message Group {
    // Group Identifier
    string group_id = 1;
    // The path to this specific group
    Path path = 2;
    // The name of the group
    string name = 3;
    // The members of the group
    repeated string members = 4;
}

message Brand {
    // brand_path is the brand's node path
    string brand_path = 1;
    // market_id is the market that the brand belongs to
    string market_id = 2;
    // The group for the brand
    Group group = 3;
    // the brand tab enablement statuses
    TabStatuses tab_statuses = 4;
    // the brand feature enablement statuses
    FeatureStatuses feature_statuses = 5;
    // activated_visibility_sources are sources enabled for listings/reviews
    repeated string activated_visibility_sources = 6;
}

message BrandMetadata {
    // The brand's group id (node path)
    string group_id = 1;
    // The name of the brand
    string name = 2;
    // The timestamp the brand was created
    google.protobuf.Timestamp created = 3;
    // The timestamp the brand was last updated
    google.protobuf.Timestamp updated = 4;
    // The number of account groups associated with the brand
    int64 locations = 5;
}

// Request message for MultiLocation.SyncBrand
message SyncBrandRequest {
    string brand_path = 1;
}

message ListGroupsRequest {
    // The base group id that will be returned and all their descendants
    string group_id = 1;
    // Foreign keys which relate to this group
    ForeignKeys foreign_keys = 2;
}

message ListGroupsResponse {
    // A list of groups
    repeated Group groups = 1;
}

message GetBrandRequest {
    oneof path {
      string brand_path = 1;
      string group_id = 2;
    }
}

message GetBrandResponse {
    // The brand matching the given brand path
    Brand brand = 1;
}

message ListBrandsMetadataRequest {
    string partner_id = 1;
}

message ListBrandsMetadataResponse {
    repeated BrandMetadata brands = 1;
}

message GetLocationsIdsRequest {
    string group_id = 1; // ex. G-123|G-444
}

message GetLocationsIdsResponse {
    repeated string location_ids = 1;
}

message ListBrandsForBusinessRequest {
    // The unique identifier of the partner
    string partner_id = 1;
    // The unique identifier of the business
    string business_id = 2;
}

message ListBrandsForBusinessResponse {
    // List of brand information
    repeated BrandMetadata brands = 1;
}

// MultiLocation gRPC Service
service MultiLocation {
    // SyncBrand is called when a brand is created/updated/deleted
    rpc SyncBrand (SyncBrandRequest) returns (google.protobuf.Empty);
    // ListGroups all the groups and members of each group
    rpc ListGroups (ListGroupsRequest) returns (ListGroupsResponse);
    // GetBrand returns the brand data, not including it's members
    rpc GetBrand (GetBrandRequest) returns (GetBrandResponse){
      option (vendastatypes.access) = {
        scope: "business-app"
      };
    };
    // GetLocationIds returns the list of distinct location ids in the brand
    rpc GetLocationIds (GetLocationsIdsRequest) returns (GetLocationsIdsResponse);
    // ListBrands returns a list of all the brands on a pid along with the number of associated locations
    rpc ListBrandsMetadata (ListBrandsMetadataRequest) returns (ListBrandsMetadataResponse);
    // Lists brand information for a business
    rpc ListBrandsForBusiness (ListBrandsForBusinessRequest) returns (ListBrandsForBusinessResponse);
}
