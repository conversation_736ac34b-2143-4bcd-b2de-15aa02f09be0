### 1.16.0
- Depreacted the fields `keyword_tracking_tab_enabled` and `insights_tab_enabled` from TabStatuses for Combine all these functionalities into `listings_tab_enabled`

### 1.15.0
- Add `keyword_tracking_tab_enabled` to TabStatuses to determine if we show the Keyword Tracking tab

### 1.14.0
- Add `business-app` scope to `GetBrand`

### 1.13.0
- Allow `GetBrandRequest` to accept `group_id` in place of `brand_path`

### 1.12.0
- Add `activated_visibility_sources` to `Brand` to replace functionality from VBC

### 1.11.0
- Add `google_q_and_a_tab_enabled` to TabStatuses to determine if we show the Google Q&A tab 

### 1.10.0
- Add customer_voice_executive_report_enabled to FeatureStatuses

### 1.9.0 
- Add ListBrandsForBusiness RPC 

### 1.8.0
- Add website_tab_enabled status to TabStatuses
- Depreciate request_review_card_enabled

### 1.7.0
- Add GetLocationIds endpoint

### 1.6.0
- Add request_review_card_enabled status to FeatureStatuses

### 1.5.0
- Add data_export_tab_enabled status to TabStatuses

### 1.4.0
- Add tab settings to GetBrand endpoint

### 1.3.0
- Add Group to GetBrand endpoint

### 1.2.0
- Add GetBrand endpoint

### 1.1.0
- Add ListGroups endpoint

### 1.0.0
- Initial commit of multi_location protos
