syntax = "proto3";

package yext.v1;

option go_package = "github.com/vendasta/generated-protos-go/yext/v1;yext_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.yext.v1.generated";

import "yext/v1/field_mask.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/annotations.proto";


// YextIDs are the IDs used by Yext to store an entity.
message YextIds {
  // The Yext account ID. In the Vendasta instance of Yext, this is seen in the user interface as the "Partner Customer ID".
  string account_id = 1;
  // The Yext entity ID. In the Vendasta instance of Yext, this is seen in the suer interface as the "Partner Location ID".
  string entity_id = 2;
}

// Entity is a configuration object to map a Vendasta account group to a Yext entity.
message Entity {
  // The Vendasta account group ID. In the Vendasta instance of Yext, this will be equal to the entity_id.
  string account_group_id = 1;
  // The IDs used by Yext to store an entity.
  YextIds yext_ids = 2;
  // The category IDs to sync to Yext. This is NOT necessarily the Yext categories, as the Yext instance can have a category mapping defined.
  // For example, In the Vendasta instance of Yext, these category IDs will be the Vendasta taxonomy IDs from the account group.
  repeated string category_ids = 3;
}

message CreateRequest {
  Entity entity = 1;
}

message UpdateRequest {
  Entity entity = 1;
  // Mask for what fields should be updated.
  FieldMask field_mask = 2;
}

message GetRequest {
  // The Vendasta account group ID. In the Vendasta instance of Yext, this will be equal to the entity_id.
  string account_group_id = 1;
}

message GetResponse {
  Entity entity = 1;
}

message LookupRequest {
  // The IDs used by Yext to store an entity.
  YextIds yext_ids = 2;
}

message LookupResponse {
  Entity entity = 1;
}

message DeleteRequest {
  // The Vendasta account group ID. In the Vendasta instance of Yext, this will be equal to the entity_id.
  string account_group_id = 1;
}

// EntityAdmin is the service to manage mappings of Vendasta account groups to Yext entities.
service EntityAdmin {
  rpc Create(CreateRequest) returns (google.protobuf.Empty);
  rpc Update(UpdateRequest) returns (google.protobuf.Empty);
  rpc Get(GetRequest) returns (GetResponse);
  // Lookup attempts to find a single entity by the Yext IDs, rather than the account group ID primary key.
  rpc Lookup(LookupRequest) returns (LookupResponse);
  rpc Delete(DeleteRequest) returns (google.protobuf.Empty);
};

// An enumerated constant specifying Yext API version to use
enum YextAPIVersion {
  YEXT_API_VERSION_UNKNOWN = 0;
  YEXT_API_VERSION_NOTSET = 1;
  YEXT_API_VERSION_V1 = 2;
  YEXT_API_VERSION_V2 = 3;
}

// An enumerated constant specifying what the Yext specific fields on the order form can fall back to if not
// specified.
enum IDFallBackSource {
  ID_FALL_BACK_SOURCE_UNKNOWN = 0;
  ID_FALL_BACK_SOURCE_ACCOUNT_GROUP_CUSTOMER_ID = 1;
  ID_FALL_BACK_SOURCE_ACCOUNT_GROUP_ID = 2;
  ID_FALL_BACK_SOURCE_PARTNER_ID = 3;
  ID_FALL_BACK_SOURCE_BRAND_ID = 4;
}

enum UserConnectionMethod {
  USER_CONNECTION_METHOD_UNKNOWN = 0;
  // Default: Accounts with the same email address will be linked
  USER_CONNECTION_METHOD_EMAIL = 1;
  // Accounts in Yext with the username format {yextAccountID}@{vendastaEmail} will be connected
  USER_CONNECTION_METHOD_USERNAME = 2;
}

// PartnerConfig a configuration for a partner using their own Yext contract.
message PartnerConfig {
  message IdentityFederationConfig {
    // Whether or not we will create Yext users JIT.
    bool create_users = 1;
    // Whether or not we will update Yext users JIT.
    bool update_users = 2;
    // Whether or not we will add roles to a Yext user JIT.
    bool add_roles = 3;
    // Deprecated: What roles to add to a Yext user, if add_roles is true, and the user is missing any of these roles.
    repeated string roles = 4 [deprecated = true];
    // How should the Yext user account be linked to the Vendasta user account?
    UserConnectionMethod account_link_method = 5;
  }

  message LocationFederationConfig {
    // Whether or not we sync location data from Yext to Vendasta
    bool process_yext_to_vendasta = 1;
    // Whether or not we sync location data from Vendasta to Yext
    bool process_vendasta_to_yext = 2;
    // Whether or not location is enabled in Yext when LSP is activated
    bool create_location = 3;
    // Whether or not location is disabled in Yext when LSP addon is deactivated
    bool remove_location = 4;
  }

  // The partner ID, provided by Vendasta.
  string partner_id = 1;
  // The reseller ID, provided by Yext.
  string reseller_id = 2;
  // The API key for an 'application' in Yext's developer console.
  // This API key needs read/write access to the the knowledge API, the listings API, and the administrative API in order to work.
  // The api_key below refers to API V2 which is the default
  string api_key = 3;
  // The Yext whitelabelled web host: the host that users would see in their browser in the Yext dashboard.
  string yext_web_host = 5;
  // The Yext api host: the host used for API calls. Should be either api.yext.com or api-sandbox.yext.com.
  string yext_api_host = 6;
  // The configuration for managing (syncing) partner-level users between Vendasta and Yext.
  IdentityFederationConfig partner_identity_config = 7;
  // The configuration for managing (syncing) smb-level users between Vendasta and Yext.
  IdentityFederationConfig smb_identity_config = 8;
  // The configuration for location federation between Vendasta and Yext
  LocationFederationConfig location_federation_config = 9;
  // The Yext V1 API key.
  string api_key_v1 = 10;
  // Specify which API to use for the location management (V1 or V2). Default is V2
  YextAPIVersion location_management_api_version = 11;
  // Specify the fall back value of the Yext account id. The fall back value is used when the AccountId is not
  // specified on the order form. This is a required field.
  IDFallBackSource account_id_fallback = 12;
  // Specify the fallback value of the Yext entity id. The fall back value is used when the EntityId is not specified
  // on the order form. This is a required field.
  IDFallBackSource entity_id_fallback = 13;
}

message CreatePartnerConfigRequest {
  PartnerConfig partner_config = 1;
}

message UpdatePartnerConfigRequest {
  PartnerConfig partner_config = 1;
}

message GetPartnerConfigRequest {
  string partner_id = 1;
}

message GetPartnerConfigResponse {
  PartnerConfig partner_config = 1;
}

message ActivateYextLSPRequest {
  string account_group_id = 1;
  string addon_id = 2;
  string yext_plan_id = 3;
  string activation_id = 4;
}

message ActivateYextPlansRequest {
  string account_group_id = 1;
  string addon_id = 2;
  repeated string yext_plan_ids = 3;
  string activation_id = 4;
}

message DeactivateYextLSPRequest {
  string account_group_id = 1;
  string partner_id = 2;
  string activation_id = 3;
  // Optional, if not specified, all skus will be cancelled
  repeated string skus = 4;
}

message SyncAccountGroupToYextRequest {
  string account_group_id = 1;
  // Optional, is_bring_your_own_yext specifies if the account group is configured with Bring Your Own Yext (BYOY).
  bool is_bring_your_own_yext = 2;
}

message ListActiveAccountsRequest {
  // The Yext account ID for which to list locations/entities with active service on Yext
  //   Leave this blank if you want to get all active entities for Vendasta.
  string partner_id = 1;
}

message ListActiveAccountsResponse {
  // account_group_ids are the location/entities with an active service on Yext
  repeated string account_group_ids = 1;
}

message ChangePlanRequest {
  string partner_id = 1;
  string account_group_id = 2;
  string addon_id = 3;
  string activation_id = 4;
  string existing_plan_id = 5;
  string target_plan_id = 6;
}

message ListingsOptInRequest {
  string partner_id = 1;
  string account_group_id = 2;
  repeated string yext_source_ids = 3;
}

message ListingsOptOutRequest {
  string partner_id = 1;
  string account_group_id = 2;
  repeated string yext_source_ids = 3;
}

message SyncAccountGroupFromYextRequest {
  string account_group_id = 1;
}

message ImportPartnerRequest {
  // The partner ID for Vendasta and Yext
  string partner_id = 1;
  // Whether or not to use Yext referral add-ons
  bool is_referral = 2;
  // A list specifying a subset of Yext entities to import for the partner
  repeated string specific_yext_entity_ids = 3;
}

message GetPartnerImportWorkflowInfoRequest {
  message Filters {
    // Filter by partner ID
    string partner_id = 1;
    // Filter by import entity workflow started after this time
    google.protobuf.Timestamp started_after = 2;
  }
  // The number of results to return
  int64 page_size = 1;
  // The cursor to get the next page of import workflows
  string cursor = 2;
  // Optional filters
  Filters filters = 3;
}

message GetPartnerImportWorkflowInfoResponse {
  message WorkflowInfo {
    // The partner ID for Vendasta and Yext
    string partner_id = 1;
    // The account group ID for the import workflow
    string account_group_id = 2;
    // The Yext account ID, which starts different than the account group ID
    string yext_account_id = 3;
    // The stage of the import workflow
    string stage = 4;
    // Whether expected inconsistencies were found during the import (e.g. too many keywords)
    bool has_warnings = 5;
    // The error message if the import workflow failed
    string error_message = 6;
  }
  // The stages of the import workflows
  repeated WorkflowInfo workflow_info = 1;
  // The total number of import workflows
  int64 total = 2;
  // Whether there are more import workflows to list
  bool has_more = 3;
  // The cursor to get the next page of import workflows
  string cursor = 4;
}

message IsBusinessReferralRequest {
  string account_group_id = 1;
}

message IsBusinessReferralResponse {
  bool is_referral_account = 1;
}

service Yext {
  // CreatePartnerConfig creates a configuration for a partner using their own Yext contract.
  rpc CreatePartnerConfig(CreatePartnerConfigRequest) returns (google.protobuf.Empty);
  // UpdatePartnerConfig updates a configuration for a partner using their own Yext contract.
  rpc UpdatePartnerConfig(UpdatePartnerConfigRequest) returns (google.protobuf.Empty);
  // GetPartnerConfig gets a configuration for a partner using their own Yext contract.
  rpc GetPartnerConfig(GetPartnerConfigRequest) returns (GetPartnerConfigResponse);
  // GetEffectivePartnerConfig gets the configuration for a partner, who may or may not be using their own Yext contract.
  // This RPC will fall back to our default config if a config specific to the partner does not exist.
  rpc GetEffectivePartnerConfig(GetPartnerConfigRequest) returns (GetPartnerConfigResponse);
  // ActivateYextLSP triggers a workflow to create a location entity and enable a plan on Yext for a business
  rpc ActivateYextLSP(ActivateYextLSPRequest) returns (google.protobuf.Empty);
  // SyncAccountGroupToYext triggers a workflow to syndicate a business's latest information to Yext
  rpc SyncAccountGroupToYext(SyncAccountGroupToYextRequest) returns (google.protobuf.Empty);
  // DeactivateYextLSP triggers a workflow to remove a business's location from all Yext plans
  rpc DeactivateYextLSP(DeactivateYextLSPRequest) returns (google.protobuf.Empty);
  // ListActiveAccounts lists the locations/entities with active service on Yext
  rpc ListActiveAccounts(ListActiveAccountsRequest) returns (ListActiveAccountsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc ChangePlan(ChangePlanRequest) returns (google.protobuf.Empty);

  // ActivateYextPlans triggers a workflow to activate one or more plans on Yext for a business
  rpc ActivateYextPlans(ActivateYextPlansRequest) returns (google.protobuf.Empty);
  // ListingsOptOut opts out of specified listings sources for a business
  rpc ListingsOptOut(ListingsOptOutRequest) returns (google.protobuf.Empty);
  // ListingsOptIn opts in to specified listings sources for a business
  rpc ListingsOptIn(ListingsOptInRequest) returns (google.protobuf.Empty);
  // SyncAccountGroupFromYext fetches data from Yext and updates the business profile
  rpc SyncAccountGroupFromYext(SyncAccountGroupFromYextRequest) returns (google.protobuf.Empty);
  // ImportPartner triggers a workflow to import a partner's SMBs from Yext
  rpc ImportPartner(ImportPartnerRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // GetPartnerImportProgress gets the progress of the import workflow
  rpc GetPartnerImportWorkflowInfo(GetPartnerImportWorkflowInfoRequest) returns (GetPartnerImportWorkflowInfoResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // IsBusinessReferral takes in a business id and checks if it is from the Referral Import Workflow.
  rpc IsBusinessReferral(IsBusinessReferralRequest) returns (IsBusinessReferralResponse);
};
