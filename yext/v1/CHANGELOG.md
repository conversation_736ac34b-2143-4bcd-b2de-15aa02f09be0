## 1.15.0
- Add `is_bring_your_own_yext` to `SyncAccountGroupToYextRequest`

## 1.14.0
- Add `specific_yext_entity_ids` to `ImportPartnerRequest` request

## 1.13.0
- Add `GetPartnerImportWorkflowInfo` endpoint

## 1.12.1
- Add admin scope to `ImportPartner` rpc

## 1.12.0
- Add `ImportPartner` endpoint

## 1.11.0
- Add `IsBusinessReferral` endpoint

## 1.10.0
- Add `SyncAccountGroupFromYext` endpoint

## 1.9.0
- Add `skus` field to `DeactivateYextLSPRequest`

## 1.8.1
- Add `skus` field to `DeactivateYextLSPRequest`

## 1.8.0
- Add `ActivateYextPlans` endpoint for activating multiple Yext plans at a time

## 1.7.0
- Add `ChangePlan` endpoint

## 1.6.1
- Fix `ListActiveAccountsResponse` scope

## 1.6.0
- Added `ListActiveAccountsResponse` endpoint

## 1.5.0
- Added `DeactivateYextLSP` endpoint

## 1.4.0
- Added the `ActivateYextLSP` and `SyncAccountGroupToYext` endpoints

## 1.3.0
- Added new fallback option for the Yext account id and entity id to look it up by the account's Brand

## 1.2.0
- Added fallback options for the Yext account id and the Yext entity id to the partner config

## 1.1.0
- Updated the partner config to support Yext API V1 in addition to existing V2 (default)

## 1.0.1 
- Added location configuration options to the partner config

## 1.0.0
- `Yext` service for managing partner configuration
- `EntityAdmin` service to manage CRUD for the Yext "Entity" objects.
