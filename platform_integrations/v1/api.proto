syntax = "proto3";

package platform_integrations.v1;

option go_package = "github.com/vendasta/generated-protos-go/platform_integrations/v1;platform_integrations_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.platform_integrations.v1.generated";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "vendasta_types/field_mask.proto";
import "google/protobuf/struct.proto";
import "vendasta_types/annotations.proto";
import "vendasta_types/paging.proto";


enum ConnectionMethods {
  UNSPECIFIED = 0;
  OAUTH2 = 1;
  APIKEY = 2;
  VENDOR_MANAGED = 3;
  LEGACY_OAUTH = 4;
}

enum SupportedContexts {
  PI_CONTEXT_UNSET = 0;
  PI_CONTEXT_PARTNER = 1;
  PI_CONTEXT_SMB = 2;
}

message DeleteIntegrationRequest{
  string integration_id = 1;
  string namespace = 2;
}

message IntegrationsFilters{
  string display_name = 1;
  //category_keys are used to filter the integrations based on which category it falls under
  repeated string category_keys = 2;
}

//added fields to save config for the integration
message CreateIntegrationRequest{
  string owner_id = 1;
  string logo = 2;
  string display_name = 3;
  string description = 4;
  string integration_type = 5;
  ConnectionMethods connection_method = 6;
  string client_id = 7;
  string client_secret = 8;
  string api_key = 9;
  string authentication_url = 10;
  string token_url = 11;
  string long_description = 12;
  AdditionalInfo additional_info = 13;
  string base_url = 14;
  int64 max_connections = 15;
  string webhook_verifier_token = 16;
  repeated SupportedContexts supported_contexts = 17;
  // Contains the ID of a CRM field extension that should be automatically enabled for the namespace when a connection to this integration is created
  repeated string field_extension_ids = 18;
  // sync setting details of preconnect form for the integration
  FormConfig preconnect_form_fields = 19;
  // sync setting details of config page for the connection
  repeated FormConfig config_page_fields = 20;
  // instructions displayed on the config page when the connection is in preconnected status
  FormConfig instructions = 21;
  //category_keys are used to filter the integrations based on which category it falls under
  repeated string category_keys = 22;
  // OAuth endpoint used to revoke the access or refresh token for this integration
  string revoke_token_url = 23;
}

// added FormConfig for storing title, description, fieldConfig, actions, urls of preconnect form and config page
message FormConfig {
  // title of the form
  string title = 1;
  // for description of the  preconnect form
  string description = 2;
  // list of the sync settings
  repeated FieldConfig fields = 3;
  // for actions of the preconnect form and config page
  repeated Actions actions = 4;
}

// added FieldConfig for storing settings of the integration
message FieldConfig{
  // id of the setting
  string id = 1;
  // label of the setting
  string label = 2;
  // place holder for the inputbox for API key based integration
  string place_holder = 3;
  // description for the setting, for special case jobber "select the trigger to sync customer data:"
  string hint_text = 4;
  // type of the setting like checkbox, toggle, password, text, radio ...etc
  FieldType field_type= 5;
  // for required field
  bool required = 6;
  // dependents for special case like jobber, dependents: [{id: 'triggerIsClosedJob', required: false, type: 'radio',value: 'false'}]
  repeated FieldConfig dependents = 7;
  // options for the radio button like jobber, options: [{label: 'When a job is closed', value: 'true'}, {label: 'When a visit is completed', value: 'false'}]
  repeated FieldOptions options = 8;
  // button actions of the settings of config page
  repeated Actions actions = 9;
  // url in instruction of the config page
  string url = 10;
  // lock icon for sync setting like automated review request, url icon for instruction, eye icon for password
  string icon = 11;
  // default_value The value to use when there is no connection record or the connection record does not have a custom field with this field's id.
  google.protobuf.Value default_value = 12;
  // flag to determine whether we need to show the field in the UI or not.
  bool hidden = 13;
  // fieldScope is the allowed scopes that can view the field in the connection settings page.
  repeated FieldScope field_scopes = 14;
  // Readonly allows to enable the password field editable or readonly.
  bool readonly = 15;
  // writeScope is the allowed scope that can allow edit access to a field in the connection settings page.
  repeated FieldScope write_scopes = 16;
}

// FieldOptions represents a single item in a dropdown or radio list
message FieldOptions {
  // The primary text to display to the user
  string label = 1;
  // value The value to use when this option is selected
  google.protobuf.Value value = 2;
}

// FieldScope defines scopes for which the field will be visible
// using enum here because in future we might add more scope types and expand the usecase beyond the fields
enum FieldScope {
  // Allows only the support team to have the access
  INTEGRATION_SUPPORT = 0;
  // Allows the namespace admins such as partner admins, sales people & SMB users to have the access
  NAMESPACE_ADMINS = 1;
  // Allows the user who established the connection to have the acess
  CONNECTING_USER = 2;
}

// FieldType defines options for how the field will be rendered on the forms.
enum FieldType {
  FIELD_TYPE_UNSPECIFIED = 0;
  // for checkbox sync setting
  FIELD_TYPE_CHECKBOX = 1;
  // for toggle sync setting
  FIELD_TYPE_TOGGLE = 2;
  // for password sync setting
  FIELD_TYPE_PASSWORD = 3;
  // for input_box sync setting
  FIELD_TYPE_TEXT = 4;
  // for radiobutton sync setting
  FIELD_TYPE_RADIO = 5;
  // for dropdown sync setting
  FIELD_TYPE_SELECT = 6;
  //for the instructions label ,as we need FieldType for instructions which holds only label.
  FIELD_TYPE_LABEL  = 7;
  // for chips sync setting
  FIELD_TYPE_CHIPLIST = 8;
  // for url of the instructions
  FIELD_TYPE_URL= 9;
}


// actions for sync setting
message Actions {
  // label of the button
  string label = 1;
  // type of the action like continue, cancel, send request, copy, save
  ActionType action_type = 2;
}

// added enum for possible action type for sync setting
enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;
  // for AUTH2 based integrations, preconnect form buttons
  ACTION_TYPE_CONTINUE = 1;
  ACTION_TYPE_CANCEL = 2;
  // for gingr integration, preconnect form has add connection button
  ACTION_TYPE_ADD_CONNECTION = 3;
  // for support based integrations, preconnect form has send request button
  ACTION_TYPE_SEND_REQUEST = 4;
  // for gingr integration, config page has copy button
  ACTION_TYPE_COPY = 5;
  // save button needs for legacy based connections
  ACTION_TYPE_SAVE = 6;
  // unlock button for automated review request
  ACTION_TYPE_UNLOCK = 7;
  // for text type sync setting like protractor
  ACTION_TYPE_EDIT = 8;
}

message AdditionalInfo {
  string provider = 1;
  repeated string categories = 2;
  repeated WebsiteInfo websites = 3;
  repeated string requirements = 4;
  repeated ResourceInfo resources = 5;
  repeated string businesses = 6;
  repeated string billing = 7;
  // Product id which are available in the partner center marketplace and ID could change based on the
  // environment the user is logged in (DEMO/PROD). Ex. RM, SM, MP-94072eXXXXXXXXXXXXXXd7ab4fc7a7e8
  repeated string product_ids = 8;
  // The works_with section can be used to display the sections that are associated with integrations.
  // Ex: GoogleMeet : {Business:["My Meetings"]}, QuickBooks : {Customers:["contacts","companies"]} , GoogleSearchConsole : {Reports:"Executive reports"}
  // This field can be used instead of the existing businesses field to support different, multiple sections as key value pairs utilizing the required fields in FieldConfig type
  repeated FieldConfig works_with = 9;
}

message WebsiteInfo {
  string site_name = 1;
  string site_url = 2;
}

message ResourceInfo {
  string resource_name = 1;
  string resource_url = 2;
}

message CreateIntegrationResponse{
  string integration_id = 1;
}

message ListIntegrationRequest{
  vendastatypes.PagedRequestOptions paging_options = 1;
  IntegrationsFilters filters = 2;
}

message ListIntegrationResponse{
  repeated GetIntegrationResponse integrations = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}
message GetIntegrationRequest{
  string integration_id = 1;
}

message GetIntegrationResponse{
  string integration_id = 1;
  string owner_id = 2;
  string logo = 3;
  string display_name = 4;
  string description = 5;
  string integration_type = 6;
  string client_id = 7;
  string client_secret = 8;
  string api_key = 9;
  string authentication_url = 10;
  string token_url = 11;
  ConnectionMethods connection_method  = 12;
  google.protobuf.Timestamp created = 13;
  google.protobuf.Timestamp updated = 14;
  google.protobuf.Timestamp deleted = 15;
  string long_description = 16;
  AdditionalInfo additional_info = 17;
  string base_url = 18;
  int64 max_connections = 19;
  string webhook_verifier_token = 20;
  repeated SupportedContexts supported_contexts = 21;
  // Contains the ID of a CRM field extension that should be automatically enabled for the namespace when a connection to this integration is created
  repeated string field_extension_ids = 22;
  // sync setting details of preconnect form for the integration
  FormConfig preconnect_form_fields = 23;
  // sync setting details of config page for the connection
  repeated FormConfig config_page_fields = 24;
  // instructions displayed on the config page when the connection is in preconnected status
  FormConfig instructions = 25;
  //category_keys are used to filter the integrations based on which category it falls under
  repeated string category_keys = 26;
  // OAuth endpoint used to revoke the access or refresh token for this integration
  string revoke_token_url = 27;
}

message UpdateIntegrationRequest {
  message UpdateFields {
    string logo = 1;
    string display_name = 2;
    string description = 3;
    string integration_type = 4;
    string client_id = 5;
    string client_secret = 6;
    string api_key = 7;
    ConnectionMethods connection_method  = 8;
    string authentication_url = 9;
    string token_url = 10;
    string long_description = 11;
    AdditionalInfo additional_info = 12;
    string base_url = 13;
    int64 max_connections = 14;
    string webhook_verifier_token = 15;
    repeated SupportedContexts supported_contexts = 16;
    string owner_id = 17;
    // Contains the ID of a CRM field extension that should be automatically enabled for the namespace when a connection to this integration is created
    repeated string field_extension_ids = 18;
    // sync setting details of preconnect form for the integration
    FormConfig preconnect_form_fields = 19;
    // sync setting details of config page for the connection
    repeated FormConfig config_page_fields = 20;
    // instructions displayed on the config page when the connection is in preconnected status
    FormConfig instructions = 21;
    //category_keys are used to filter the integrations based on which category it falls under
    repeated string category_keys = 22;
    // OAuth endpoint used to revoke the access or refresh token for this integration
    string revoke_token_url = 23;
  }
  string integration_id = 1;
  UpdateFields update_fields = 2;
  vendastatypes.FieldMask field_mask = 3;
}

message ListIntegrationMarketingRequest{
  vendastatypes.PagedRequestOptions paging_options = 1;
  IntegrationsFilters filters = 2;
  SupportedContexts supported_contexts = 3;
}

message IntegrationMarketingResponse{
  string integration_id = 1;
  string logo = 2;
  string display_name = 3;
  string description = 4;
  string integration_type = 5;
  string status = 6;
  google.protobuf.Timestamp created = 7;
  google.protobuf.Timestamp updated = 8;
  google.protobuf.Timestamp deleted = 9;
  string long_description = 10;
  AdditionalInfo additional_info = 11;
  int64 max_connections = 12;
  ConnectionMethods connection_method = 13;
  repeated SupportedContexts supported_contexts = 14;
  // sync setting details of preconnect form for the integration
  FormConfig preconnect_form_fields = 18;
  // sync setting details of config page for the connection
  repeated FormConfig config_page_fields = 19;
  // instructions displayed on the config page when the connection is in preconnected status
  FormConfig instructions = 20;
  //category_keys are used to filter the integrations based on which category it falls under
  repeated string category_keys = 21;
}

message ListIntegrationMarketingResponse{
  repeated IntegrationMarketingResponse integrations = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}
message GetIntegrationMarketingRequest{
  string integration_id = 1;
  string namespace = 2;
}
message GetMultiIntegrationMarketingRequest{
  repeated string integration_ids = 1;
}
message GetMultiIntegrationMarketingResponse{
  repeated IntegrationMarketingResponse integrations = 1;
}

message EmitEventRequest {
  // This is not the event broker event topic. It is a sub topic that the vendor will use as their "topic" for the event.
  string topic = 1;
  // The namespace that this event is for, the current options are an account group id or partner id. Any string is accepted here
  // we do not validate that it refers to an actual account group or partner.
  string namespace = 2;
  google.protobuf.Struct payload = 3;

  // Optional: An ID to identify this as a unique event. Idempotency is calculated as a combination of the event id and the emittedAt time.
  // if event_id is not provided a UUID will be generated instead
  string event_id = 4;
  // Optional: The time this event was emitted at. Is used along with the eventID to calculate idempotency to stop duplicate events.
  // If provided, it must be a value in the past, after the year 2000.
  google.protobuf.Timestamp emitted_at = 5;
  // The integration owner of the id. Should be the vendor id.
  string owner_id = 6;
}

message EmitEventResponse {
  // The ID of the event that was emitted. It is returned because event_id is optional on the intake so vendors might want to
  // have access to the generated id
  string event_id = 1;
}

// IntegrationMarketingService is used by end consumers who are looking for integrations they can enable for themselves
service IntegrationMarketingService{
  rpc List(ListIntegrationMarketingRequest) returns (ListIntegrationMarketingResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  rpc Get(GetIntegrationMarketingRequest) returns (IntegrationMarketingResponse);
  rpc GetMulti(GetMultiIntegrationMarketingRequest) returns (GetMultiIntegrationMarketingResponse);
}

// IntegrationManagementService is used by the builder of an integration to configure it
service IntegrationManagementService{
  rpc Create(CreateIntegrationRequest) returns (CreateIntegrationResponse) {
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };
  rpc Update(UpdateIntegrationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };
  rpc List(ListIntegrationRequest) returns (ListIntegrationResponse) {
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };
  rpc Delete(DeleteIntegrationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };
  rpc Get(GetIntegrationRequest) returns (GetIntegrationResponse) {
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };
  // EmitEvent allows vendors to emit event broker events to be consumed by automations. There is manual work to be done before a vendor can use this.
  // https://vendasta.jira.com/wiki/spaces/AUTO/pages/2293989386/How+do+Vendor+triggers+work
  rpc EmitEvent(EmitEventRequest) returns (EmitEventResponse) {
    option (vendastatypes.access) = {
      scope: "integration.event"
    };
  }
}

enum ConnectionStatus {
  UNSPECIFIED_STATUS = 0;
  PRECONNECTED = 1;
  CONNECTED = 2;
  DISCONNECTED = 3;
  DELETED = 4;
}

enum Severity {
  UNSPECIFIED_SEVERITY = 0;
  INFO = 1;
  WARNING = 2;
  ERROR = 3;
}

message ConnectionMessage {
  // INFO, WARNING, ERROR
  Severity severity = 1;
  string title = 2;
  string message = 3;
  // Text to display on a call to action button
  string cta_text = 4;
  // Page or site to navigate to when the call to action button is clicked
  string cta_url = 5;
}

message ListConnectionRequest{
  string namespace = 1;
  vendastatypes.PagedRequestOptions paging_options = 2;
  Filters filters = 3;
  message Filters {
     // Optional. connections could be filtered based one of these attribute.
     SupportedContexts context = 1;
     string external_id = 2;
     string integration_id = 3;
     string integration_type = 4;
  }
}
message ConnectionResponse{
  string connection_id = 1;
  string namespace = 2;
  string integration_id = 3;
  string user_id = 4;
  google.protobuf.Timestamp created = 5;
  google.protobuf.Timestamp updated = 6;
  google.protobuf.Timestamp deleted = 7;
  ConnectionStatus status= 8;
  string label = 9;
  string external_id = 10;
  repeated CustomFields custom_fields = 11;
  google.protobuf.Timestamp status_last_updated = 12;
  google.protobuf.Timestamp data_last_received = 13;
  SupportedContexts context = 14;
  repeated ConnectionMessage connection_messages = 15;
}
message ListConnectionResponse{
  repeated ConnectionResponse connections = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message DeleteConnectionRequest{
  string connection_id = 1;
  string namespace = 2;
}

message CreateConnectionRequest{
  string namespace = 1;
  string integration_id = 2;
  repeated CustomFields custom_fields = 3;
  SupportedContexts context = 4;
}

message CreateConnectionResponse{
  string connection_id = 1;
}

message GetConnectionRequest{
  string connection_id = 1;
  string namespace = 2;
  Filters filters = 3;
  message Filters {
     // Optional. connections could be filtered based one of these attribute.
     SupportedContexts context = 1;
     string external_id = 2;
     string integration_id = 3;
     string integration_type = 4;
  };
  int64 refresh_if_older_than_minutes = 4;
}

message GetConnectionResponse{
  string connection_id = 1;
  string namespace = 2;
  string integration_id = 3;
  string user_id = 4;
  google.protobuf.Timestamp created = 5;
  google.protobuf.Timestamp updated = 6;
  google.protobuf.Timestamp deleted = 7;
  ConnectionStatus status = 8;
  string label= 9;
  string external_id= 10;
  repeated CustomFields custom_fields = 11;
  SupportedContexts context = 12;
  google.protobuf.Timestamp status_last_updated = 13;
  google.protobuf.Timestamp data_last_received = 14;
  repeated ConnectionMessage connection_messages = 15;
}

message CustomFields{
  string label = 1;
  string value = 2;
}

message UpdateConnectionRequest{
  message UpdateFields {
    ConnectionStatus status = 1;
    repeated CustomFields custom_fields = 2;
    string label = 3;
    SupportedContexts context = 4;
  }
  string connection_id = 1;
  string namespace = 2;
  UpdateFields update_fields = 3;
  vendastatypes.FieldMask field_mask = 4;
}
message GetConnectionDataRequest{
  string connection_id = 1;
}
message GetConnectionDataResponse{
  string connection_id = 1;
  string namespace = 2;
  string integration_id = 3;
  repeated CustomFields custom_fields = 4;
  ConnectionStatus status = 5;
  google.protobuf.Timestamp status_last_updated = 6;
  google.protobuf.Timestamp data_last_received = 7;
}

service ConnectionsService{
  rpc Create(CreateConnectionRequest) returns (CreateConnectionResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  rpc Update(UpdateConnectionRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  rpc Get(GetConnectionRequest) returns (GetConnectionResponse){
    option (vendastatypes.access) = {
      scope: "integration-connection:read"
    };
  };
  rpc List(ListConnectionRequest) returns (ListConnectionResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  rpc Delete(DeleteConnectionRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };;
  rpc GetConnectionData(GetConnectionDataRequest) returns (GetConnectionDataResponse);
}

service ProxyService{
  //CallIntegrationEndpoint is used to call an endpoint on the integrated system
  // using the credentials stored on the connection.
  //
  // Example usage is an automation action that calls an endpoint on the integrated system
  rpc CallIntegrationEndpoint(CallIntegrationEndpointRequest) returns (CallIntegrationEndpointResponse){
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };

  //CallVendastaEndpoint is used to call an endpoint on the vendasta platform
  // It will be used by vendors to modify data within the connected namespace
  rpc CallVendastaEndpoint(CallVendastaEndpointRequest) returns (CallVendastaEndpointResponse){
    option (vendastatypes.access) = {
      scope: "integration"
    };
  };
}

message CallIntegrationEndpointRequest{
  ConnectionIdentifier connection = 1;
  APIRequest request = 2;
}
message CallIntegrationEndpointResponse{
  APIResponse response = 1;
  // The ID assigned to the request by the platform
  // It can be used to lookup the request in the logs
  string request_id = 2;
}

message CallVendastaEndpointRequest{
  ConnectionIdentifier connection = 1;
  APIRequest request = 2;
}
message CallVendastaEndpointResponse{
  APIResponse response = 1;
  // The ID assigned to the request by the platform
  // It can be used to lookup the request in the logs
  string request_id = 2;
}

message ConnectionIdentifier{
  // integration is used to filter the list of connections
  oneof integration {
    // The UUID of an integration configuration record
    string integration_id = 1;
    // The type of the integration configuration record. Ex: "ShopMonkey"
    // Some integrations have multiple records to represent different configurations
    // Using the code allows all configurations to be checked however may result in multiple connections
    string integration_type = 2;
  }
  oneof connection {
    // The UUID of a connection record
    string connection_id = 3;
    // The vendor supplied id for the connection to the integration.
    // They are unique per integration.
    string external_id = 4;
    // The namespace that this connection is for, the current options are an account group id or partner id.
    string namespace = 5;
  }
}

// APIRequest is the request to be proxied to the specified system
// The request parts will be processed as go templates allowing for values from the connection to be inserted
// For example you may use
// {.connection.custom_field.some_field}
// {.connection.namespace}
message APIRequest{
  HTTPMethod method = 1;
  string path = 2;
  repeated HTTPParam query = 3;
  repeated HTTPParam headers = 4;
  bytes body = 5;
}

message APIResponse{
  repeated HTTPParam headers = 1;
  int32 status_code = 2;
  bytes body = 3;
}

enum HTTPMethod {
  UNDEFINED = 0;
  POST = 1;
  GET = 2;
  PUT = 3;
  DELETE = 4;
  PATCH = 5;
}

message HTTPParam{
  string key = 1;
  repeated string values = 2;
}
