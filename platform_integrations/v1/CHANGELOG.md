## 0.46.0
- Added `revoke_token_url` to integration record

## 0.45.0
- Add list of `category_keys` field in CreateIntegrationRequest, GetIntegrationResponse, UpdateIntegrationRequest and IntegrationMarketingResponse

## 0.44.0
- Add `category_keys` filter for IntegrationsFilters
- 
## 0.43.0
- Add Categories CRUD API

## 0.42.0
- Add Filter `integration_type` in the ListConnectionRequest and GetConnectionRequest

## 0.41.0
- Add `write_scopes` field to `FieldConfig`

## 0.40.0
- Add `refresh_if_older_than_minutes` to `GetConnectionRequest`

## 0.39.0
- Used PagedRequestOptions and PagedResponseMetadata from vendastatypes instead of defining them.

## 0.38.0
- Added 'readonly' field in the FieldConfig

## 0.37.0
- Added Filters with `context` `external_id` `integration_id` the GetConnectionRequest

## 0.36.0
- Added Filters with `context` `external_id` `integration_id` the ListConnectionRequest

## 0.35.0
- Added 'namespace' field in the GetIntegrationMarketingRequest

## 0.34.0
- Added 'works_with' field in the AdditionalInfo section

## 0.33.0
- Added 'fieldScope' field in the FieldConfig

## 0.32.0
- Add 'ConnectionMessage' for ConnectionResponse

## 0.31.0
- Add `business-app` scope to `OAuthService.GetAuthorizationCodeRedirectURL` `OauthService.RevokeAccessTokenAndDelete` `IntegrationMarketingService/List` `ConnectionsService/List` `ConnectionsService/Create` `ConnectionsService/Delete` `ConnectionsService/Update`

## 0.30.0
- RPC endpoints for proxy service

## 0.29.0
- Added productIds fields which can show the products, the integrations can work with.

## 0.28.0
- Added hidden field in feildConfig

## 0.27.0
- Added field_extensio_id to `CreateIntegrationRequest, GetIntegrationResponse, UpdateIntegrationRequest`

## 0.26.4
- Add `formConfig` to create,get,update rpc endpoint

## 0.26.3
- Add Legacy to `ConnectionMethods`

## 0.26.2
- add context in `ListConnectionResponse`

## 0.26.1
- add options to`GetVendorConnection` rpc endpoint

## 0.26.0
- Added `data_last_received` field in connection response

## 0.25.0
- Create `GetConnectionBusinessData` rpc endpoint

## 0.24.0
- Return `status_last_updated` in Connection response

## 0.23.0
- Connection `label` update attribute added in `UpdateConnection` rpc

## 0.22.3
- add context in update connection request

## 0.22.2
- add UpdateConnection rpc endpoint

## 0.22.1
- Adding scope for vendor endpoints

## 0.22.0
- Adding vendor connection endpoints

## 0.21.5
- Added OwnerId to the update integration request

## 0.21.4
- Added new scope : integration-connection

## 0.21.3
- add GetConnectionData rpc endpoint

## 0.21.2
- add context(smb,partner) to integration updatable fields and list marketing request

## 0.21.1
- Add context(smb,partner) to integration and connection APIs

## 0.20.1
- Updated CreateConnection and GetAuthorizationCodeRedirect API

## 0.19.0
- Added support for vendor managed integrations

## 0.18.2
- Update the List,Get and Create connection API.

## 0.18.1
- Update the update connection API.

## 0.18.0 
- Add `owner_id` to `EmitEvent` rpc

## 0.17.2
- Add scope to `EmitEvent` rpc

## 0.17.1
- Return the event id from the `EmitEvent` rpc

## 0.17.0
- Add a new service for managing events and an `EmitEvent` rpc

## 0.16.0
- Make requirements field as array in AdditionalInfo proto

## 0.15.0
- Added ConnectionMethod to MarketingIntegrationResponse

## 0.14.0
- Added max_connections in the integrations APIs, external_id in the connections APIs

## 0.13.0
- Added base_url in integration APIs

## 0.12.0
- Added LongDescription and AdditionalInfo in Integrations APIs

## 0.11.0
- Updated status and name in connections APIs

## 0.10.0
- Adding RevokeAccessTokenAndDelete endpoint

## 0.9.0
- Remove protos for HandleCallbackRequest and HandleCallbackResponse and remove them from the authorization service

## 0.8.0
- Adding integrationID to GetAuthorizationCodeRedirectURLRequest

## 0.7.0
- RPC endpoints for authorization service

## 0.6.0
- RPC (get && getmulti) endpoints for Integration Marketing service

## 0.5.0
- RPC endpoints for connections table

## 0.4.0
- Refine the protos field names

## 0.3.0
- Refined IntegrationManagementService

## 0.2.0
- Added IntegrationManagementService && IntegrationMarketingService

## 0.1.0
- Initial commit
