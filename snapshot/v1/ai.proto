syntax = "proto3";

package snapshot.v1;

option go_package = "github.com/vendasta/generated-protos-go/snapshot/v1;snapshot_v1";
option java_outer_classname = "ArtificialIntelligenceProto";
option java_package = "com.vendasta.snapshot.v1.generated";

import "snapshot/v1/snapshot.proto";

enum Role {
  ROLE_UNSET = 0;
  ROLE_SYSTEM = 1;
  ROLE_USER = 2;
  ROLE_ASSISTANT = 3;
}

message AIPrompt {
  string prompt = 1;
  Role role = 2;
}

message ContextualizeRequest {
  string snapshot_id = 1;
  // The sections to include in the response
  // If not provided, all sections will be included
  repeated Section sections = 2;
}

message ContextualizeResponse {
  string context = 1;
}

message GetWidgetRequest {
  string snapshot_id = 1;
}

message GetWidgetResponse {
  string widget_id = 1;
  string widget_url = 2;
  bool enabled = 3;
}

message CreateWidgetRequest {
  string namespace = 1;
}
