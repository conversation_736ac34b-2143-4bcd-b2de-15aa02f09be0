syntax = "proto3";

package snapshot.v1;

option go_package = "github.com/vendasta/generated-protos-go/snapshot/v1;snapshot_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.snapshot.v1.generated";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "snapshot/v1/advertising.proto";
import "snapshot/v1/configuration.proto";
import "snapshot/v1/current.proto";
import "snapshot/v1/field_mask.proto";
import "snapshot/v1/listings.proto";
import "snapshot/v1/reviews.proto";
import "snapshot/v1/salesperson.proto";
import "snapshot/v1/seo.proto";
import "snapshot/v1/snapshot.proto";
import "snapshot/v1/snapshot_lite.proto";
import "snapshot/v1/social.proto";
import "snapshot/v1/website.proto";
import "snapshot/v1/whitelabel.proto";
import "snapshot/v1/ecommerce.proto";
import "snapshot/v1/direct_competitor.proto";
import "snapshot/v1/record.proto";
import "snapshot/v1/shared.proto";
import "snapshot/v1/technology.proto";
import "vendasta_types/annotations.proto";
import "snapshot/v1/ai.proto";

message GetSectionRequest {
  // The id of the snapshot to get a section for
  string snapshot_id = 1;
}

message CreateSnapshotRequest {
  // The id of the business to create the snapshot for
  string business_id = 1;
  // The id to assign to the snapshot report
  string snapshot_id = 2;
  // The id of the user that created the snapshot report
  string created_by = 3;
}

message CreateSnapshotResponse {
  // The id of the snapshot that is being created
  string snapshot_id = 1;
}

message GetSnapshotRequest {
  // The id of the snapshot to get
  string snapshot_id = 1;
}

message GetSnapshotResponse {
  Snapshot snapshot = 1;
}

message UpdateSnapshotConfigRequest {
  // The id of the snapshot to update the config for
  string snapshot_id = 1;
  GlobalConfig config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message UpdateSnapshotLanguageConfigRequest {
  // The id of the snapshot to update the config for
  string snapshot_id = 1;
  // The language config to update
  LanguageConfig config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 3;
}

message GetSnapshotIDRequest {
  // The id of the partner (required)
  string partner_id = 1;
  // The id of the market (optional)
  string market_id = 2;
}

message GetSnapshotIDResponse {
  // The opaque snapshot id to get the snapshot with to edit the default configs
  // There are no guarantees on the structure of this id, and it may change at any time
  // you should treat is as an opaque random string and not try to build it manually
  string snapshot_id = 1;
}

// GetWhiteLabelRequest is deprecated and will be removed in a future release.
message GetWhitelabelRequest {
  string snapshot_id = 1;
}

message GetWhitelabelResponse {
  Branding branding = 1;
}

message GetSalesPersonRequest {
  string snapshot_id = 1;
}

message GetSalesPersonResponse {
  SalesPerson sales_person = 1;
}

message GetConfigurationRequest {
  string snapshot_id = 1;
}

message GetConfigurationResponse {
  Configuration configuration = 1;
}

message ForceRefreshSectionRequest {
  // The id of the snapshot to refresh
  string snapshot_id = 1;
  // The sections of the report to refresh
  repeated Section sections = 2;
}

message GetLatestSnapshotIDRequest {
  // The id of the business to get the latest snapshot ID for.
  string business_id = 1;
}

message GetLatestSnapshotIDResponse {
  // The id to assign to the latest snapshot report.
  string snapshot_id = 2;
}

message GetBusinessIDFromSalesforceIDRequest {
  // The salesforce id
  string salesforce_id = 1;
  // The id of the partner
  string partner_id = 2;
}

message GetBusinessIDFromSalesforceIDResponse {
  // The id of the business
  string business_id = 1;
}

message GetSnapshotCountCreatedBySalespersonIDRequest {
  // The id of the partner
  string partner_id = 1;
  // The id of the market
  string market_id = 2;
  // The id of the snapshot created by salesperson ID
  string salesperson_id = 3;
}

message GetSnapshotCountCreatedBySalespersonIDResponse {
  // Count for how many snapshot created by specific salesperson ID
  int64 count = 1;
}

message PreviewRefreshSnapshotForBusinessesRequest {
  // The ids of the businesses
  repeated string business_ids = 1;
}

message PreviewRefreshSnapshotForBusinessesResponse {
  // Total count of businesses
  int64 total = 1;
  // Count of existing snapshots that would be refreshed for a business
  int64 to_refresh = 2;
  // Count of snapshots that would be created for a business
  int64 to_create = 3;
  // Count of businesses that will not have snapshots refreshed or created.
  int64 unaffected = 4;
}

message GetSectionAvailabilityRequest {
  // The id of the snapshot to get the section availability
  string snapshot_id = 1;
}

message GetSectionAvailabilityResponse {
  // Returns true if the section is available in the snapshot
  bool is_available = 1;
}

message ShareSnapshotRequest {
  repeated Contact contacts = 1;
  Contact sender = 2;
  string business_id = 3;
  string snapshot_id = 4;
  Email email = 5;
  string domain = 6;
}

// Provides the domain name and additional metadata.
message Domain {
  string domain = 1;
  bool secure = 2;
}

// AppDomain is the app to get the domain for
// VBC = Business app/ MS = Listing builder/ SM = Social marketing/ ST = Salestool/ RM = Reputation Intelligence.
enum AppDomain {
  APP_DOMAIN_INVALID = 0;
  APP_DOMAIN_VBC = 1;
  APP_DOMAIN_MS = 2;
  APP_DOMAIN_SM = 3;
  APP_DOMAIN_ST = 4;
  APP_DOMAIN_RM = 5;
}

// Request for GetDomain
message GetDomainRequest {
  string partner_id = 1;
  AppDomain application = 2;
}

// Response for GetDomain
message GetDomainResponse {
  Domain primary = 1;
}

message PreviewEmailRequest {
  Email email = 1;
}

message PreviewEmailResponse {
  bytes rendered_template = 1;
}

message GetDirectCompetitorsRequest {
  string snapshot_id = 1;
}

message GetDirectCompetitorsResponse {
  repeated DirectCompetitor direct_competitors = 1;
}

message SaveDirectCompetitorsRequest {
  string snapshot_id = 1;
  repeated DirectCompetitor direct_competitors = 2;
}

message CanProvisionSnapshotRequest {
  string namespace = 1;
  string business_id = 2;
  string user_id = 3;
}

enum CannotProvisionReason {
  UNKNOWN = 0;
  AUTHENTICATION_FAILED = 1;
  ACTIVE_SNAPSHOT_ALREADY_EXISTS = 2;
  PARTNER_CANNOT_CREATE_SNAPSHOT = 3;
  SALESPERSON_SNAPSHOT_LIMIT_REACHED = 4;
}

message CanProvisionSnapshotResponse {
  // If the snapshot can be provisioned
  bool can_provision = 1;
  // If can_provision is false, this can contain multiple reasons why
  repeated CannotProvisionReason reason = 2;
}

message ProvisionSnapshotOptions {
  // infer missing data for a business
  bool infer_missing_data = 1;
}

message ProvisionSnapshotRequest {
  // The business to provision the snapshot for
  string business_id = 1;
  // An indicator of where this snapshot was created from
  string origin = 2;
  // Who initiated the snapshot provisioning. If not provided, will be inferred
  string user_id = 3;
  // Options for provisioning a snapshot
  ProvisionSnapshotOptions options = 4;
}

message ProvisionSnapshotResponse {
  // The ID for the snapshot
  string snapshot_id = 1;
}

message GetSummaryRequest {
  // The snapshot to get a summary for
  string snapshot_id = 1;
}

message GetCurrentRequest {
  // The ID for the business
  string business_id = 1;
}

message GetCurrentResponse {
  // The current snapshot
  Current snapshot = 1;
}

message SectionSummary {
  // A human readable string with the section name
  string section_name = 1;
  // The grade assigned to the section
  Grade grade = 2;
  // An enum indicating the section
  Section section_id = 3;
}

message Location {
  // The Location's street address
  string address = 1;
  // The Location's City
  string city = 2;
  // The Location's State
  string state = 3;
  // The location's Country
  string country = 4;
}

message GetSummaryResponse {
  // The snapshot to get a summary for
  string snapshot_id = 1;
  // The business ID that the snapshot belongs to
  string business_id = 2;
  // The company name for the snapshot's Business
  string company_name = 3;
  // The created date of the snapshot
  google.protobuf.Timestamp created = 4;
  // The expiration date of the snapshot
  google.protobuf.Timestamp expired = 5;
  // The snapshot's overall score
  string score = 6;
  // A summary of each individual section of the report
  repeated SectionSummary sections = 7;
  // Location information for the business
  Location location = 8;
}

message GetRequest {
  // The business id to get the record for
  string business_id = 1;
  // the snapshot id of the record
  string snapshot_id = 2;
}

message GetResponse {
  // A snapshot record
  Record record = 1;
}

message LookupRequest {
  // Options for how to page the response for this request
  PagedRequestOptions paging_options = 1;
  // the business id for looking up existing snapshots
  string business_id = 2;
}

message LookupResponse {
  // A snapshot record
  repeated Record record = 1;
  // Metadata about the paging
  PagedResponseMetadata paging_metadata = 2;
}

message UpdateCurrentRequest {
  // The ID for the business
  string business_id = 1;
  // The ID identifying the snapshot
  string snapshot_id = 2;
  // The created timestamp of the snapshot
  google.protobuf.Timestamp created = 3;
  // The expired timestamp of the snapshot
  google.protobuf.Timestamp expired = 4;
}

message GeneratePDFRequest {
  string snapshot_id = 1;
}

message GeneratePDFResponse {
  bytes pdf = 1;
}

message GetFreeSnapshotsRemainingRequest {
  // PartnerID
  string namespace = 1;
}

message GetFreeSnapshotsRemainingResponse {
  int64 free_snapshots_remaining = 1;
}

// The service responsible for the CurrentSnapshot.
service CurrentSnapshot {
  // Update the current snapshot. Used for migration purposes
  rpc UpdateCurrent(UpdateCurrentRequest) returns (google.protobuf.Empty);
  // Get Current gets the current snapshot for a business
  rpc GetCurrent(GetCurrentRequest) returns (GetCurrentResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

// The service responsible for interacting with snapshot records
service SnapshotRecord {
  // Get gets a snapshot record
  rpc Get(GetRequest) returns (GetResponse);
  // Lookup will return all snapshots for a business ordered by creation date
  // using page_size of 1 returns the most recent snapshot
  rpc Lookup(LookupRequest) returns (LookupResponse);
}

// The service to interact with snapshots
service SnapshotService {
  // Create will create an individual snapshot entity only
  // note: use Provision to kick off the entire snapshot creation process
  rpc Create(CreateSnapshotRequest) returns (CreateSnapshotResponse);
  rpc Get(GetSnapshotRequest) returns (GetSnapshotResponse);
  rpc UpdateConfig(UpdateSnapshotConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  // UpdateLanguageConfig sets the locale and video style for a snapshot
  rpc UpdateLanguageConfig(UpdateSnapshotLanguageConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  // GetSnapshotID converts a partner/market combo to the snapshotID to be used to edit the default config
  rpc GetSnapshotID(GetSnapshotIDRequest) returns (GetSnapshotIDResponse);
  // DEPRECATED: GetWhitelabel retrieves the whitelabel data for the business the snapshot was created for
  rpc GetWhitelabel(GetWhitelabelRequest) returns (GetWhitelabelResponse);
  // GetConfiguration retrieves the configuration associated with the business for a snapshot
  rpc GetConfiguration(GetConfigurationRequest) returns (GetConfigurationResponse);
  // GetSalesPerson retrieves the salesperson associated with the business for a snapshot
  rpc GetSalesPerson(GetSalesPersonRequest) returns (GetSalesPersonResponse);
  // ForceRefreshSection refreshes specific sections of a snapshot
  rpc ForceRefreshSection(ForceRefreshSectionRequest) returns (google.protobuf.Empty);
  // GetLatestSnapshotID gets the latest snapshot ID for a business.
  rpc GetLatestSnapshotID(GetLatestSnapshotIDRequest) returns (GetLatestSnapshotIDResponse) {
    option (vendastatypes.access) = {
      scope: "snapshot:read"
      scope: "snapshot"
    };
  };
  // GetBusinessIDFromSalesforceID gets the business id for a business associated with a given salesforce id
  rpc GetBusinessIDFromSalesforceID(GetBusinessIDFromSalesforceIDRequest) returns (GetBusinessIDFromSalesforceIDResponse);
  // GetSnapshotCountCreatedBySalespersonResponse gets the count of a salesperson create snapshot
  rpc GetSnapshotCountCreatedBySalespersonID(GetSnapshotCountCreatedBySalespersonIDRequest) returns (GetSnapshotCountCreatedBySalespersonIDResponse);
  // PreviewRefreshSnapshotForBusinesses returns count of snapshots to refresh, to create, and unaffected
  rpc PreviewRefreshSnapshotForBusinesses(PreviewRefreshSnapshotForBusinessesRequest) returns (PreviewRefreshSnapshotForBusinessesResponse);
  // ShareSnapshot shares the snapshot with contacts
  rpc ShareSnapshot(ShareSnapshotRequest) returns (google.protobuf.Empty);
  // Returns a domain by the Identifier if it exists.
  rpc GetDomain(GetDomainRequest) returns (GetDomainResponse);
  // PreviewEmail previews email content for sharing a snapshot
  rpc PreviewEmail(PreviewEmailRequest) returns (PreviewEmailResponse);
  // Returns Direct Competitors
  rpc GetDirectCompetitors(GetDirectCompetitorsRequest) returns (GetDirectCompetitorsResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  // Saves Direct Competitors
  rpc SaveDirectCompetitors(SaveDirectCompetitorsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  // Can Provision Snapshot checks if a snapshot can be provisioned with the provided options
  // if unable to provision it will provide the reason why
  rpc CanProvision(CanProvisionSnapshotRequest) returns (CanProvisionSnapshotResponse);
  // Provision kicks off the entire snapshot creation process
  // it handles all the prerequisite work required for a snapshot as well as creating the snapshot
  rpc Provision(ProvisionSnapshotRequest) returns (ProvisionSnapshotResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  // GetSummary gets a summary of the snapshot and its sections
  rpc GetSummary(GetSummaryRequest) returns (GetSummaryResponse) {
    option (vendastatypes.access) = {
      scope: "snapshot:read"
      scope: "snapshot"
    };
  };
  // Generate PDF
  rpc GeneratePDF(GeneratePDFRequest) returns (GeneratePDFResponse);
  // Get the number of free Snapshots remaining for the month
  rpc GetFreeSnapshotsRemaining(GetFreeSnapshotsRemainingRequest) returns (GetFreeSnapshotsRemainingResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message UpdateReviewConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  ReviewConfig review_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetReviewSectionResponse {
  ReviewSection section = 1;
}

// The service to interact with the reviews section
service ReviewSectionService {
  rpc Get(GetSectionRequest) returns (GetReviewSectionResponse);
  rpc UpdateConfig(UpdateReviewConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message UpdateSocialConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  SocialConfig social_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetSocialSectionResponse {
  SocialSection section = 1;
}

// The service to interact with the social section
service SocialSectionService {
  rpc Get(GetSectionRequest) returns (GetSocialSectionResponse);
  rpc UpdateConfig(UpdateSocialConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message UpdateListingConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  ListingConfig listing_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetListingSectionResponse {
  ListingSection section = 1;
}

message GetListingScanRequest {
  string business_id = 1;
}

message GetListingScanResponse {
  ListingScan listing_scan = 1;
}

message GetAvailableListingSourcesRequest {
  string snapshot_id = 1;
}

message GetAvailableListingSourcesResponse {
  repeated AvailableListingSource sources = 1;
}

message IgnoreListingSourceRequest {
  string snapshot_id = 1;
  string source_id = 2;
}

message UnignoreListingSourceRequest {
  string snapshot_id = 1;
  string source_id = 2;
}

// The service to interact with the listings section
service ListingSectionService {
  rpc Get(GetSectionRequest) returns (GetListingSectionResponse);
  rpc UpdateConfig(UpdateListingConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  rpc GetListingScan(GetListingScanRequest) returns (GetListingScanResponse);
  rpc GetAvailableListingSources(GetAvailableListingSourcesRequest) returns (GetAvailableListingSourcesResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  rpc IgnoreListingSource(IgnoreListingSourceRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  rpc UnignoreListingSource(UnignoreListingSourceRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message UpdateWebsiteConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  WebsiteConfig website_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetWebsiteSectionResponse {
  WebsiteSection section = 1;
}

// The service to interact with the website section
service WebsiteSectionService {
  rpc Get(GetSectionRequest) returns (GetWebsiteSectionResponse);
  rpc UpdateConfig(UpdateWebsiteConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message UpdateAdvertisingConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  AdvertisingConfig advertising_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetAdvertisingSectionResponse {
  AdvertisingSection section = 1;
}

// The service to interact with the listings section
service AdvertisingSectionService {
  rpc Get(GetSectionRequest) returns (GetAdvertisingSectionResponse);
  rpc UpdateConfig(UpdateAdvertisingConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message UpdateSEOConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  SEOConfig seo_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetSEOSectionResponse {
  SEOSection section = 1;
}

// The service to interact with the listings section
service SEOSectionService {
  rpc Get(GetSectionRequest) returns (GetSEOSectionResponse);
  rpc UpdateConfig(UpdateSEOConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message GetSnapshotLiteRequest {
  string business_id = 1;
}

message GetSnapshotLiteResponse {
  SnapshotLite snapshot = 1;
}

message UpdateSnapshotLiteBusinessDataRequest {
  // The id of the business to update the scan business data for
  string business_id = 1;
  // The business data to change
  Business business = 2;
  // The fields to change on the business
  FieldMask field_mask = 3;
}

// Service to interact with the lite snapshot
service SnapshotLiteService {
  rpc Get(GetSnapshotLiteRequest) returns (GetSnapshotLiteResponse);
  rpc UpdateSnapshotLiteBusinessData(UpdateSnapshotLiteBusinessDataRequest) returns (google.protobuf.Empty);
}

message UpdateEcommerceConfigRequest {
  // The id of the snapshot to update a section for
  string snapshot_id = 1;
  EcommerceConfig ecommerce_config = 2;
  // Mask for what fields should be updated
  FieldMask field_mask = 20;
}

message GetEcommerceSectionResponse {
  EcommerceSection section = 1;
}

// The service to interact with the ecommerce section
service EcommerceSectionService {
  rpc Get(GetSectionRequest) returns (GetEcommerceSectionResponse);
  rpc UpdateConfig(UpdateEcommerceConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  rpc GetSectionAvailability(GetSectionAvailabilityRequest) returns (GetSectionAvailabilityResponse);
}

// TechnologyService requests/responses
message CreateTechnologyRequest {
  string name = 1;
  repeated string category_ids = 2;
  string website = 3;
  string icon_name = 4;
}

message CreateTechnologyResponse {
  Technology technology = 1;
}

message GetTechnologyRequest {
  string technology_id = 1;
}

message GetTechnologyResponse {
  Technology technology = 1;
}

message GetMultiTechnologyRequest {
  repeated string technology_ids = 1;
}

message GetMultiTechnologyResponse {
  repeated Technology technologies = 1;
}

message UpdateTechnologyRequest {
  string technology_id = 1;
  Technology technology = 2;
  FieldMask field_mask = 3;
}

message DeleteTechnologyRequest {
  string technology_id = 1;
}

message GetMultiTechnologyCategoryRequest {
  repeated string category_ids = 1;
}

message GetMultiTechnologyCategoryResponse {
  repeated TechnologyCategory technology_categories = 1;
}

// The service to handle the technology data
service TechnologyService {
  rpc Create(CreateTechnologyRequest) returns (CreateTechnologyResponse);
  rpc Get(GetTechnologyRequest) returns (GetTechnologyResponse);
  rpc GetMulti(GetMultiTechnologyRequest) returns (GetMultiTechnologyResponse);
  rpc Update(UpdateTechnologyRequest) returns (google.protobuf.Empty);
  rpc Delete(DeleteTechnologyRequest) returns (google.protobuf.Empty);

  rpc GetMultiCategory(GetMultiTechnologyCategoryRequest) returns (GetMultiTechnologyCategoryResponse);
}

message UpdateTechnologyConfigRequest {
  string snapshot_id = 1;
  TechnologyConfig technology_config = 2;
  FieldMask field_mask = 3;
}

message GetTechnologySectionResponse {
  TechnologySection section = 1;
}

// The service to interact with the technology section
service TechnologySectionService {
  rpc Get(GetSectionRequest) returns (GetTechnologySectionResponse);
  rpc UpdateConfig(UpdateTechnologyConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
}

message GenerateMessageRequest {
  string snapshot_id = 1;
  repeated AIPrompt prompts = 2;
}

message GenerateMessageResponse {
  Role role = 1;
  string content = 2;
}

service ArtificialIntelligence {
  rpc GenerateMessage(GenerateMessageRequest) returns (GenerateMessageResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  rpc Contextualize(ContextualizeRequest) returns (ContextualizeResponse) {
    option (vendastatypes.access) = {
      scope: "partner.admin"
    };
  }
  rpc GetWidget(GetWidgetRequest) returns (GetWidgetResponse) {}
  rpc CreateWidget(CreateWidgetRequest) returns (google.protobuf.Empty);
}
