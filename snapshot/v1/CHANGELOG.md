# 4.76.0
- Add `CreateWidget` RPC in `ArtificialIntelligence` service

# 4.75.1
- Adding `enabled` to widget rpc

# 4.75.0
- Add rpc for `GetWidget` 

# 4.74.0
- Add `sections` to `ContextualizeRequest`

# 4.73.0
- Add `STATUS_REEL_DETECTED` to `InstagramDataStatus`

# 4.72.0
- Add `snapshot` scope to `GetLatestSnapshotID`

# 4.71.0
- Add `snapshot` scope to `GetSummary`

# 4.70.0
- Add `snapshot:read` scope to `GetSummary`

# 4.69.0
- Add `CanProvision` rpc to `SnapshotService`

# 4.68.0
- Add `customized_footer` properties to `GlobalConfig`

# 4.67.0
- Add `Contextualize` rpc to `ArtificialIntelligence` service

# 4.66.0
- Add `GetFreeSnapshotsRemaining` rpc to `SnapshotService`

# 4.65.0
- Add `GeneratePDF` rpc to `SnapshotService`

# 4.64.0
- Add `ignored` flag to `AvailableListingSource` message

# 4.63.0
- Add `IgnoreListingSource` and `UnignoreListingSource` rpcs to `ListingSectionService`

# 4.62.0
- Add `partner.admin` scope to `GetDirectCompetitors` rpc

# 4.61.0
- Add `GetAvailableListingSources` rpc to `ListingSectionService`

# 4.60.0
- Add `ArtificialIntelligence` service
- Add `GenerateMessage` rpc to `ArtificialIntelligence` service

# 4.59.0
- Add `TechnologySectionService` service
- Add `Get` and `UpdateConfig` rpcs to `TechnologySectionService`

# 4.58.0
- Add `partner.admin` scope to `Provision` and `GetCurrent` rpcs

# 4.57.0
- Add `icon_name` property to `Create` rpc in `TechnologyService`

# 4.56.0
- Add `icon_name` property to `Technology`

# 4.55.0
- Add `TechnologyCategory` getMulti rpc to `TechnologyService`

# 4.54.0
- Add `TechnologyService` service, rpcs and initial messages

# 4.53.0
- Add partner admin scope to `Update...` rpcs required for editing a snapshot

# 4.52.0
- Add `vcategory_id` to `SnapshotData` message

# 4.51.0
- Add options to the ProvisionSnapshot request
  - Options include flag to infer missing business data prior to provisioning the snapshot

# 4.50.1
- Add `appointment_scheduling` to ecommerce `Competitors` message

# 4.50.0
- Add `applications_availability` and `appointment_scheduling` to `EcommerceData` message

# 4.49.0
- Update the ShareSnapshotRequest message
  - Contact:
    - add full_name
    - deprecate first_name, last_name
  - Email:
    - deprecate greeting, closing, button_text

# 4.48.0
- Add `website` to `EcommerceData` message

# 4.47.0
- Add `inferred_fields` to `SnapshotData` message

# 4.46.0
- Add `user_id` to `ProvisionSnapshotRequest` message

# 4.45.0
- Add search_keyword to LocalSEOData message

# 4.44.0
- Add phone_number, reviews, and claim_status to LocalSEOData Results message
- Add Reviews message that contains review rating and review count
- Add GMBClaimStatus enum
    - GMB_CLAIM_STATUS_UNKNOWN // A default status for existing snapshots that doesn't have any Local SEO Data
    - GMB_CLAIM_STATUS_CLAIMED // A status for a claimed google my business location
    - GMB_CLAIM_STATUS_UNCLAIMED // A status for an unclaimed google my business location

# 4.43.0
- Add more LocalSEO Vicinities
  - NEARME_NORTH, NEARME_NORTHEAST, NEARME_EAST, NEARME_SOUTHEAST, NEARME_SOUTH, NEARME_SOUTHWEST, NEARME_WEST, NEARME_NORTHWEST

# 4.42.0
- Add Status enum to LocalSEOData
  - STATUS_DOES_NOT_EXIST means the LocalSEOData does not exist
  - STATUS_IN_PROGRESS means we are in the process of fetching the data
  - STATUS_FINISHED means data has been fetched
  
# 4.41.0
- Add custom_keyword property to SEO config
  - if a custom keyword has been configured, it will be used in local seo search

# 4.40.0
- Add grade to the LocalSEOData message

#4.39.0 
- Add hide_local_seo_data to SEO Config

#4.38.0
- Add value_per_click property to SEODomainMetrics

#4.37.0
- Update SERPMetrics LatLng message to include latitude and longitude fields as doubles and deprecate the lat/lng string fields

#4.36.0
- Add LocalSEOData to the SEOData of the SEOSection

#4.35.0
- Add GetCurrent to the CurrentSnapshot service

#4.34.0
- Add UpdateCurrentSnapshot RPC and CurrentSnapshot Service

#4.33.0
- Add a sectionID to the snapshot summary

#4.32.0
- Add `ssl` to WebsiteData message
  - `SSLStatus` specifies the status of a website's ssl certificate

#4.31.0
- Add id to pagespeed insights v5 Audit rule

#4.30.0
- Add section_version to WebsiteConfig. 

#4.29.0
- Add instagram as a listing source in the listing presence response. Add ability to hide it on config

#4.28.0
- Add StatusV2 enum to DataProviderAccuracy
  - StatusV2 contains a proper default value of UNSPECIFIED_V2
- Deprecate neustar_accurate, factual_accurate, acxiom_accurate, infogroup_accurate
- Add neustar_status, factual_status, infogroup_status, foursquare_status

#4.27.0
- Deprecate ListingScan message
- Deprecate listing_scan property on ListingData
- Add ListingV2 message
- Add listings property to ListingData

#4.26.0
- Add hide_listing_details to the ListingConfig
  - This will hide the Listing Details subsection

#4.25.0
- Add a GetSummary RPC to the SnapshotServer

#4.24.0
- Add SnapshotRecord service with Get, Lookup endpoints
- Add Provision rpc to SnapshotService
  - Provision will kick off the workflow for creating a snapshot
- Add shared protos for Paging data

#4.23.0
- Add Get and Save endpoints for Direct Competitors

#4.22.0
- Add CompetitionAnalysis to GlobalConfig

#4.21.0
- Deprecate GetWhitelabel endpoint

# 4.20.0
- Add Competitors to WebsiteData

# 4.19.0
- Add Competitors to EcommerceData
  - returns competitor data if found
  - the existence of 'website' in the message means that data fetching has occurred
  - if there is no 'website', we have not yet successfully fetched data

# 4.18.0
- Add GetDomain to SnapshotService

# 4.17.0
- Add PreviewEmail to SnapshotService

# 4.16.0
- Add greeting, button_text, closing, subject to Email for ShareSnapshot

# 4.15.0
- Add domain to ShareSnapshot

# 4.14.0
- Add Email to ShareSnapshot
- Add snapshot_id to ShareSnapshot

# 4.13.0
- Add ShareSnapshot function to SnapshotService
- Add Contact message

# 4.12.0
- Add Competitor field to SocialData
  - this is a nested message that contains data for the competitor

# 4.11.0
- Deprecating fields `use_custom_snapshot_footer`, `custom_snapshot_footer`, `use_custom_snapshot_popup_message`, `custom_snapshot_popup_message` from `Configuration` 
  - The features related to them are not supported in snapshot report anymore.
  
# 4.10.0
- Add customized_footer_cta_target_product to all sections config

# 4.9.0
- Add customized_footer_cta_label and customized_footer_cta_target_url to all sections config

# 4.8.0
- Add customized_footer_message to all sections config

# 4.7.0
- Add hide_schedule_meeting_option to GlobalConfig
  - this is a configuration option to hide the ability for a prospect to directly schedule a meeting with a salesperson

# 4.6.0
- Add is_partner_config to Snapshot message

# 4.5.0
- Add audit details to audits in website section

# 4.4.0
- Add GetSectionAvailability rpc
  - this is an rpc that returns true if the section is available in Snapshot or false if it isn't

# 4.3.0
- Add fields to EcommerceData
  - The following fields are included in the Data
    - ecommerce_solution, online_payments, digital_advertising, ad_retargeting
- Add fields to EcommerceConfig
  - The following fields are included in the Config
    - hide_content, customized_message, hide_footer, hide_ecommerce_solution, hide_online_payments,
    hide_digital_advertising, hide_ad_retargeting

# 4.2.0
- Add Ecommerce section proto
  - Create EcommerceSectionService, add Get and UpdateConfig functions

# 4.1.0
- Add desktop_insights, mobile_insights fields to WebsiteData
  - these fields contain information from pagespeedinsights v5

# 4.0.0
- Add UpdateSnapshotLanguageConfig rpc
  - this is an rpc for updating the locale and video style
- Pull VideoStyle enum out of GlobalConfig message

# 3.13.0
- Add Competitor field to ReviewData
  - this is a nested message that contains data for the competitor

# 3.12.0
- Add Competitor field to ListingAccuracy, ListingPresence
  - this is a nested message that contains data for the competitor

# 3.11.0
- Add snapshot_id to Competitor message
  - the snapshot_id corresponds to a competitor's existing snapshot that will be used to compare the prospect against

# 3.10.0
- Add PreviewRefreshSnapshotForBusinesses to SnapshotService
 - these are counts of number of businesses unaffected or that will have snapshots refreshed or created

# 3.9.0
- Add hide_subsection_grades to GlobalConfig
  - this enabled/disables the sub-grades in sections

# 3.8.1
- Add partnerID and marketID into GetSnapshotCountCreatedBySalespersonID for auth checking

# 3.8.0
- Add GetSnapshotCountCreatedBySalespersonID in snapshot endpoint

# 3.7.0
- Add business_center_host to GetWhitelabel endpoint 

# 3.6.0
- Add GetBusinessIDFromSalesforceID to SnapshotService

# 3.5.1
- Declare Keyword message before use

# 3.5.0
- Add created_by to CreateSnapshotRequest and Snapshot

# 3.4.0
- Add website and zip to Advertising Data
- Add still_working to Advertising Adwords Data

# 3.3.0
- Add FRENCH to VideoStyle options on GlobalConfig

# 3.2.0
- Add GetLatestSnapshotID to get the latest snapshot report generated for a business.

# 3.1.0
- Add service_area_business to SnapshotData

# 3.0.0
- BREAKING: Remove industry_percentiles since it wasn't used, and add in
industry_leader to reviews and social

# 2.0.0
- BREAKING: Rename Status to InstagramDataStatus on InstagramData social proto to avoid naming conflicts with DataProviderAccuracy listing proto Status

# 1.25.0
- Add Status to InstagramData on social proto

# 1.24.0
- Introduce LiteshotService
- Add GetListingScan RPC to ListingSectionService

# 1.23.0
- add local search volume to keywords in seo

# 1.22.0
- deprecate mobile friendly of the homepage data, add it to general website data.
- Add instagram present to the homepage data.

# 1.21.0
- Add instagram_url to snapshot

# 1.20.0
- Add instagram to social

# 1.19.0
- Add mobile friendliness

# 1.18.0
- Add twitter_url and facebook_url to SnapshotData

# 1.17.0
- Add ability to turn off the seo subsections

# 1.16.0
- Add keyword data to the seo section 

# 1.15.0
- Deprecate sem_content from AdvertisingSection

# 1.14.0
- Add facebook and twitter URLs to social responses

# 1.13.0
- Add ability to turn of homepage video in website section

# 1.12.0
- Add Listing Scan to Data in Listings proto

# 1.11.0
- Add footer_message_id to SectionContent

# 1.10.0
- Add hide_footer to section config protos

# 1.9.0
- Add Grades to SEO OrganicDomainData and Advertising PayPerClickData

# 1.8.0
- Add hide_homepage_content to WebsiteConfig

# 1.7.0
- Add mobile grade and scores to Website Section protos

# 1.6.0
- Add average_posts_per_month to FacebookData message

# 1.5.0
- Set SEO related fields in Advertising as Reserved, they are now only in SEO

# 1.4.1
- Add SEO to Available and Enabled Sections

# 1.4.0
 - Add SEO section protos

# 1.3.0
 - Add ForceRefreshSection RPC

# 1.2.0
 - Accept an optional `snapshot_id` on `SnapshotService.Create`

# 1.1.0
 - Add update snapshot config rpc

# 1.0.0
 - Initial release
