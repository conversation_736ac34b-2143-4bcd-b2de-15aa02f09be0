# IAM v2 Changelog

## 2.59.0 - 2025-07-09
- Add `Entitlement` message
- Add `GetEntitlementRequest` message
- Add `GetEntitlementResponse` message
- Add `GetEntitlement` rpc

## 2.58.0 - 2025-06-11
- Add CreatePamGrant rpc
- Add ListPamGrants rpc

## 2.57.0 - 2025-05-12
- Remove `VerifyShortOTPCode` and `RequestShortOTPCode`

## 2.56.0 - 2025-05-09
- Add `VerifyShortOTPCode`

## 2.55.0 - 2025-05-07
- Add `RequestShortOTPCode`

## 2.54.0 - 2025-04-09
- Add `valid_at` and `expires_at` to `GetTokenForUserRequest`

## 2.53.0 - 2024-09-23
- Add `consenting_user_id` to `GetTokenForUserRequest`

## 2.52.0 - 2024-09-12
- Add new rpc `DeleteResourceAndPolicies`

## 2.51.0 - 2024-09-09
- Add `user_and_impersonatee_id` to `AccessResourceRequest`

## 2.50.0 - 2024-08-12
- Add `search_term` to `ListResources` request

## 2.49.9 - 2024-07-30
- Add `SearchUsers` to IAMAdmin service

## 2.48.0 - 2024-07-09

- Mark version field on `Resource`, `RegisterResourceRequest`, and `ReplaceResourceRequest` as deprecated.


## 2.47.0 - 2024-06-04

- Add `resource_owner_service_url` and `resource_owner_audience` to `CallbackResourceData`
- Add `CallbackResourceData` to `ReplaceResourceRequest`

## 2.46.0 - 2024-05-28

- Add `user` scope to `UpdateUser`
- Add `user.password` scope to `ChangePassword`

## 2.45.0 - 2024-05-17

- Add `ai_explanation` to `AccessResourceWithExplanation`'s response

## 2.44.0 - 2024-05-14

- Add `AccessResourceWithExplanation` rpc

## 2.43.0 - 2024-05-07

- Add `version` filter to `Resource` to be returned in `ListResourcesResponse`

## 2.42.0 - 2024-05-07

- Add `version` filter to `RegisterResourceRequest` and `ReplaceResourceRequest`

## 2.41.0 - 2024-05-01

- Add `actions` to `Resource`

## 2.40.0 - 2024-05-01

- Add `resource_attribtues` filter to `ListResourceRequest`
- Add `subject_attributes` filter to `ListResourceRequest`
- Add `actions` filter to `ListResourceRequest`
- Add `app_ids` filter to `ListResourceRequest`

## 2.39.0 - 2024-04-30

- Add `resource_ids` filter to `ListResourceRequest`

## 2.38.0 - 2024-04-24

- Add `filters` to `ListResourceRequest` with a filter on `role_types`

## 2.37.0 - 2024-04-22

- Add `resource_attributes` to `Policy` message

## 2.36.0 - 2024-04-17

- Add `supported_role_types` to `ListResourcesResponse`

## 2.35.0 - 2024-04-08

- Add `ListResources` rpc & messages to IAMAdmin server

## 2.34.0 - 2023-12-06

- Add `user:read` scope to `ListUsers`

## 2.34.0 - 2023-12-06

- Add `all_partners` to `ListUsersRequest`
- Add `email_verified` to `ListUsersRequest`'s `UserFilters`

## 2.33.0 - 2023-11-28

- Add `subject_types` to `CreateSessionRequest`

## 2.32.0 - 2023-07-12

- Add optional `invalidate_active_sessions` at `ResetPasswordRequest`

## 2.31.0 - 2023-06-27

- deprecate `include_roles` field on `UserPropertyMask` as roles are always included by default

## 2.30.0 - 2023-06-12

- delete `EmailBranding` field and reserve number 2

## 2.29.0 - 2023-05-02

- Add the ability to set the external id when creating or updating a user

## 2.28.0 - 2023-04-25

- Add `SendOneTimePasswordEmail` rpc to `IAM` service

## 2.27.0 - 2023-03-30

- Add `user:read` scope to `GetMultiUser`

## 2.26.0 - 2023-02-27

- Add option of `one_time_password_credentials` to `credentials` at `CreateSession` rpc
- Add response to `VerifyEmail` rpc that contains a one-time code if requested

## 2.25.0 - 2023-02-23

- Add `UpsertExternalID` rpc to `IAM` service

## 2.24.2 - 2023-02-16

- Updating version to revert 2.24.1

## 2.24.0 - 2022-11-15

- Add optional `recaptcha_response` to verify email rpcs
- Change `SendEmailVerification` rpc scope to public

## 2.23.1 - 2022-10-31

- Temporarily bring back branding enum, for backwards-compat while we fix breaking change in users
  of this RPC.

## 2.23.0 - 2022-10-27

- Replace branding options with optional branding override on send verification email endpoint

## 2.22.2 - 2022-08-19

- Updated the possible return codes for `CreateSessionFailure` to be snake case.

## 2.22.0 - 2022-08-17

- Add `CreateSession` to the `IAMAdmin` service

## 2.21.0 - 2022-05-04

- Add `BookingURL` to `User` message.

## 2.20.0 - 2022-04-29

- Add `GetMultiUserAuxiliaryDataFieldSchema` rpc

## 2.19.0 - 2022-04-26

- Add `user.admin` scope to `UpsertUserAuxiliaryData`

## 2.18.1 - 2022-04-04

- Add `user:read` scope to `ListUserAuxiliaryData` for real

## 2.18.0 - 2022-04-04

- Add `user:read` scope to `ListUserAuxiliaryData`

## 2.17.1 - 2022-03-18

- Fix name from `ArchiveAuxiliaryDataFieldSchema` to `ArchiveUserAuxiliaryDataFieldSchema`
- Fix name from `UnarchiveAuxiliaryDataFieldSchema` to `UnarchiveUserAuxiliaryDataFieldSchema`

## 2.17.0 - 2022-03-16

- Add `ArchiveAuxiliaryDataFieldSchema` and `UnarchiveAuxiliaryDataFieldSchema` rpcs
- Add `only_archived` as part of `filters` for `ListAuxiliaryDataFieldSchemaRequest`
- Add `archived` to `AuxiliaryDataFieldSchema`

## 2.16.0 - 2022-03-09

- Add `user_id` to `AccessResourceRequest` inside new oneof field `user_identifier`

## 2.15.0

- Add `UpsertPartnerLimitsRequest` to `IAMAdmin` service

## 2.14.0

- Add user `AuxiliaryFieldData` and `AuxiliaryFieldSchema` rpcs

## 2.13.0

- switch to use `vendastatypes.PagedResponseMetadata`.

## 2.12.0

- Add `LinkedIn` message to `User` message.

## 2.11.0 - 2021/06/21

- Add `ReplaceResource` api for safer, more-consistent resource updates.

## 2.10.0

- Add `next_url` and `next_url_button_text` to `SendEmailVerificationRequest`

## 2.9.0

- Add `EmailBranding` to the `SendEmailVerificationRequest` message

## 2.8.0

- Add `legacy_user_id` to `GetTokenForUserRequest` message.

## 2.7.0

- Add `user_specified_password` to `User` message.

## 2.6.0

- Add `service_provider_id` to `GetTokenForUserRequest`.

## 2.5.0

- Add `additional_address` to the `address` message

## 2.4.1

- Version bump to fix generated-protos-go issues with v2 submodules

## 2.4.0

- Add `ChangePassword` for changing a users password by using their current password

## 2.3.0

- Add `phone_numbers`, `address`, `profile_image_url`, `time_zone` to the `User` message
- Add `phone_numbers`, `address`, `profile_image_url`, `time_zone` to the `UpdateUserRequest` message

## 2.3.0-rc1

- Remove unimplemented descoped `CreateTemporaryUser` RPC

## 2.2.0-rc1

- Add `oidc_identity_token` to `GetTokenForUserResponse`.

## 2.1.0-rc1

- Add `DeletePolicy`

## 2.0.0-rc1

- Initial release
