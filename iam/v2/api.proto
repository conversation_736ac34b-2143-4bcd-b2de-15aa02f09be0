syntax = "proto3";

package iam.v2;

option go_package = "github.com/vendasta/generated-protos-go/iam/v2;iam_v2";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.iam.v2.generated";

import "vendasta_types/annotations.proto";
import "vendasta_types/paging.proto";
import "iam/policies/policies.proto";
import "iam/attributes/attributes.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";
import "auxiliary_data/v1/fields.proto";

// RPCs only available to IAM Admins (Superadmins/VSAs)
service IAMAdmin {
  // User Management
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc GetResetPasswordToken(GetResetPasswordTokenRequest) returns (GetResetPasswordTokenResponse);

  // Role Management
  // RegisterUserRoleMetadata creates a new role "type"
  rpc RegisterUserRoleMetadata(RegisterUserRoleMetadataRequest) returns (google.protobuf.Empty);
  // List all role types
  rpc ListUserRolesMetadata(ListUserRolesMetadataRequest) returns (ListUserRolesMetadataResponse);

  // Add a user role with attributes, failing if the role already exists on the user.
  rpc AddUserRole(AddUserRoleRequest) returns (google.protobuf.Empty);
  // Update the attributes of an existing user role, failing if the role doesn't exist on the user.
  rpc UpdateUserRole(UpdateUserRoleRequest) returns (google.protobuf.Empty);
  // Remove a user role.
  rpc RemoveUserRole(RemoveUserRoleRequest) returns (google.protobuf.Empty);

  // Resource Admin
  rpc RegisterResourceOwner(RegisterResourceOwnerRequest) returns (google.protobuf.Empty);
  rpc RegisterResource(RegisterResourceRequest) returns (google.protobuf.Empty);
  rpc ReplaceResource(ReplaceResourceRequest) returns (google.protobuf.Empty);

  // Policy Admin
  rpc RegisterPolicy(RegisterPolicyRequest) returns (google.protobuf.Empty);
  rpc DeletePolicy(DeletePolicyRequest) returns (google.protobuf.Empty);

  // Partner Limits
  rpc UpsertPartnerLimits(UpsertPartnerLimitsRequest) returns (google.protobuf.Empty);

  // Backend Session Creation
  rpc CreateSession(CreateSessionRequest) returns (CreateSessionResponse);

  rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse);

  // Determines if a resource can be accessed & returns explanation in event of access being denied.
  rpc AccessResourceWithExplanation (AccessResourceRequest) returns (AccessResourceResponse);


  rpc SearchUsers (SearchUsersRequest) returns (SearchUsersResponse);

  // Delete resource and all the policies associated with the given app ID and resource ID
  rpc DeleteResourceAndPolicies(DeleteResourceAndPoliciesRequest) returns (google.protobuf.Empty);
}

// RPCs available to the frontend or backend applications
service IAM {
  // User RPCs
  rpc GetMultiUsers(GetMultiUsersRequest) returns (GetMultiUsersResponse) {
    option (vendastatypes.access) = {
      scope: "user:read"
    };
  };
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse) {
    option (vendastatypes.access) = {
      scope: "user:read"
    };
  };
  rpc UpdateUser(UpdateUserRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "user"
    };
  };
  rpc DeleteUser(DeleteUserRequest) returns (google.protobuf.Empty);

  rpc AddClientKey(AddClientKeyRequest) returns (AddClientKeyResponse);
  rpc RemoveClientKey(RemoveClientKeyRequest) returns (google.protobuf.Empty);
  rpc ResetPassword(ResetPasswordRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      public: true
    };
  };

  rpc ListSecurityLogs(ListSecurityLogsRequest) returns (ListSecurityLogsResponse);

  // Access RPCs
  rpc AccessResource(AccessResourceRequest) returns (google.protobuf.Empty);

  // Credential exchange RPCs
  // Exchanges a claim signed using a client private key for an IAM user token.
  rpc ExchangeClientKeyAssertion(ExchangeClientKeyAssertionRequest) returns (ExchangeClientKeyAssertionResponse) {
    option (vendastatypes.access) = {
      public: true
    };
  };


  // Given a token, marks a user's email as verified.
  rpc VerifyEmail(VerifyEmailRequest) returns (VerifyEmailResponse);
  // Send an email verification Email to the user
  rpc SendEmailVerification(SendEmailVerificationRequest) returns (google.protobuf.Empty);
  // Send an email for the given template and template data
  rpc SendOneTimePasswordEmail(SendOneTimePasswordEmailRequest) returns (google.protobuf.Empty);

  // Get External ID mappings for users
  rpc GetMultiExternalID(GetMultiExternalIDRequest) returns (GetMultiExternalIDResponse);
  // Create External ID Mappings for users
  rpc CreateExternalID(CreateExternalIDRequest) returns (google.protobuf.Empty);
  //Update or insert user's external identifier
  rpc UpsertExternalID(UpsertExternalIDRequest) returns (google.protobuf.Empty);

  // Restrict users
  rpc AddMultiUserRestriction(AddMultiUserRestrictionRequest) returns (google.protobuf.Empty);
  // Unrestrict users
  rpc RemoveMultiUserRestriction(RemoveMultiUserRestrictionRequest) returns (google.protobuf.Empty);

  // Use your current password to change your password
  rpc ChangePassword(ChangePasswordRequest) returns  (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "user.password"
    };
  };

  // ListUserAuxiliaryData auxiliary_data_object_id in the AuxiliaryDataObjectID refers to the user_id
  rpc ListUserAuxiliaryData(auxiliarydata.v1.ListAuxiliaryDataRequest) returns (auxiliarydata.v1.ListAuxiliaryDataResponse) {
    option (vendastatypes.access) = {
      scope: "user:read"
    };
  };
  // UpsertUserAuxiliaryData auxiliary_data_object_id in the AuxiliaryDataObjectID refers to the user_id
  rpc UpsertUserAuxiliaryData(auxiliarydata.v1.UpsertAuxiliaryDataRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "user.admin"
    };
  };

  // UserAuxiliaryFieldSchema service rpcs
  rpc CreateUserAuxiliaryDataFieldSchema(auxiliarydata.v1.CreateAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc GetUserAuxiliaryDataFieldSchema(auxiliarydata.v1.GetAuxiliaryDataFieldSchemaRequest) returns (auxiliarydata.v1.GetAuxiliaryDataFieldSchemaResponse);
  rpc GetMultiUserAuxiliaryDataFieldSchema(auxiliarydata.v1.GetMultiAuxiliaryDataFieldSchemaRequest) returns (auxiliarydata.v1.GetMultiAuxiliaryDataFieldSchemaResponse);
  rpc ListUserAuxiliaryDataFieldSchema(auxiliarydata.v1.ListAuxiliaryDataFieldSchemaRequest) returns (auxiliarydata.v1.ListAuxiliaryDataFieldSchemaResponse);
  rpc UpdateUserAuxiliaryDataFieldSchema(auxiliarydata.v1.UpdateAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc ArchiveUserAuxiliaryDataFieldSchema(auxiliarydata.v1.ArchiveAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc UnarchiveUserAuxiliaryDataFieldSchema(auxiliarydata.v1.UnarchiveAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);

  // PAM service rpcs
  // CreatePamGrant creates a new PAM grant for the given user.
  rpc CreatePamGrant(CreatePamGrantRequest) returns (google.protobuf.Empty);
  // ListPamGrants lists PAM grants matching the given filters. Results are not ordered.
  rpc ListPamGrants(ListPamGrantsRequest) returns (ListPamGrantsResponse);
  // GetEntitlement retrieves the Entitlement with the given id.
  rpc GetEntitlement(GetEntitlementRequest) returns (GetEntitlementResponse);
}

// IAMSSO contains methods for only the SSO service
service IAMSSO {
  // Get a token for the specified user.
  rpc GetTokenForUser(GetTokenForUserRequest) returns (GetTokenForUserResponse);
}

message Policy {
  string app_id = 1;
  string resource_id = 2;
  string policy_id = 3;
  string policy_name = 4;
  repeated string actions = 5;
  iam.policies.PolicyNode policy = 6;
  // The human readable version of the policy
  string read_only_policy = 7;
  // The inferred set of resource attributes used within the policy
  repeated string resource_attributes_used = 8;
}

// Messages ===================================================================

// A user's external id. Identifies a user in a system outside of IAM.
message TypedExternalIdentifier {
  // The type of external id.
  string external_id_type = 1;
  // The external id.
  string external_id = 2;
}

// DEPRECATED: A user can be uniquely identified with an email and a partner ID namespace
message NamespacedEmail {
  // The namespace (i.e. partner id) to which a user belongs
  string namespace = 1;
  // The email ID of the user
  string email = 2;
}

// A UserIdentifier uniquely identifies a user.
message UserIdentifier {
  oneof identifier {
    // Identifies a user by their user_id
    string user_id = 1;
    // An IAM token
    // Both identity tokens and access tokens are permitted.
    string token = 2;

    // Deprecated: Prefer to use the `user_id` or `token` fields to avoid handling email, or for `user_id` being a more stable reference to a user who may have changed their email
    NamespacedEmail namespaced_email = 3 [deprecated = true];
    // Identify a user by an external identifier.
    TypedExternalIdentifier typed_external_identifier = 4;

    // Looking up a User via a Subject ID will be slower than other methods
    string subject_id = 5;
  }
}

// Supported Algorithm Types for Persona Public/Private Keys
enum AlgorithmType {
  ALGORITHM_TYPE_ES256 = 0;
  ALGORITHM_TYPE_RS256 = 1;
}

// A Client Public Key
message PublicKey {
  string key = 1;
  string id = 2;
  AlgorithmType algorithm_type = 3;
}

// A user has many roles
// Roles contain attributes which are used in access checks.
message UserRole {
  // The type of role, e.g. "smb", "partner", etc.
  string role_id = 1;
  // Additional attributes associated with the role.
  // These are used in conjunction with policies for Access Checks
  iam.attributes.StructAttribute attributes = 2;
  // When this role was first created on this user
  google.protobuf.Timestamp created = 3;
  // When this role was last updated on this user
  google.protobuf.Timestamp updated = 4;
}

enum PhoneNumberType {
  PHONE_NUMBER_TYPE_INVALID = 0;
  PHONE_NUMBER_TYPE_WORK = 1;
  PHONE_NUMBER_TYPE_HOME = 2;
  PHONE_NUMBER_TYPE_MOBILE = 3;
  PHONE_NUMBER_TYPE_FAX = 4;
  PHONE_NUMBER_TYPE_PAGER = 5;
}

message PhoneNumber {
  // The phone number stored in the tel RFC3966 format https://tools.ietf.org/html/rfc3966
  string phone_number = 1;
  // Type of the phone number
  PhoneNumberType phone_number_type = 2;
}

message Address {
  // Street Address
  string address = 1;
  // Name of the city
  string city = 2;
  // Postal code/zip
  string postal_code = 3;
  // Country code: ISO code 3166 format (CA, US)
  string country = 4;
  // State code: ISO code 3166-2 format (CA-SK, US-FL)
  string state = 5;
  // Additional address line
  string additional_address = 6;
}

message LinkedIn {
  // Public profile identifier used to build Linkedin public profile URL, e.g.: 'https://www.linkedin.com/in/${public_profile_id}'
  string public_profile_id = 1;
}

message User {
  // The User ID e.g. U-e7751c53-8a49-5e89-90fd-7a8140889c02
  string user_id = 1;
  // The "owner" partner for the user.
  string partner_id = 2;
  // The user's email
  string email = 3;
  // A map of all roles the user has, keyed by role_id
  map<string, UserRole> roles = 4;
  // When the user was first created
  google.protobuf.Timestamp created = 5;
  // When the user was last updated
  google.protobuf.Timestamp updated = 6;
  // The user's first name
  string first_name = 7;
  // This is a special name used to greet individuals
  // which is used in certain languages.
  // It's unused in Western cultures.
  string greeting_name = 8;
  // The user's surname
  string last_name = 9;
  // The preferred language of the user as a locale
  string language_code = 10;
  // The user's public client keys
  repeated PublicKey public_keys = 11;
  // The last time the user went through the "sign-in" flow.
  google.protobuf.Timestamp last_login = 12;
  // Whether or not a user has verified their email
  bool email_verified = 13;
  // List of phone numbers associated to the user
  repeated PhoneNumber phone_numbers = 14;
  // Address of the user
  Address address = 15;
  // The user's profile image
  string profile_image_url = 16;
  // Time zone database representing the End-User's time zone. For example, Europe/Paris or America/Los_Angeles.
  string time_zone = 17;
  // The password entered specifically by the end user.
  // This field should only be used when someone is creating their OWN user, never
  // when an administrator is creating a user for someone else.
  // This must never be a "default" or system-generated password.
  // Access to this field is limited to only the service accounts that require it.
  // All other consumers of CreateUser should instead initiate the Password Reset
  // process to allow the end-user themselves to set their own password.
  string user_specified_password = 18;
  // LinkedIn user profile information
  LinkedIn linked_in = 19;
  // Meeting booking url is where the user can provide a link for booking meetings
  string meeting_booking_url = 20;
}

// Allow including additional user data
message UserPropertyMask {
  // Whether to include the user roles in the response
  // Deprecated, roles are always included
  bool include_roles = 1 [deprecated = true];
}

message GetMultiUsersRequest {
  // The users to get
  repeated UserIdentifier user_identifiers = 1;
  // Which properties to include on the returned user
  UserPropertyMask property_mask = 2;
}

message GetMultiUsersResponse {
  // The users
  repeated User users = 1;
}

// Filter which users are returned
message UserFilter {
  // Users with ANY of the provided types will be returned
  // Empty list will return ALL users.
  repeated string role_ids = 1;

  // Search terms filter (not scored), matches each search term if it exists within one of first name, last name, or email of the User
  repeated string search_terms = 2;

  // Search by email
  string email = 3;

  // Search by email verified status, if requested
  google.protobuf.BoolValue email_verified = 4;
}

enum SortDirection {
  SORT_DIRECTION_INVALID = 0;
  SORT_DIRECTION_ASCENDING = 1;
  SORT_DIRECTION_DESCENDING = 2;
}

// The field to sort a result set on.
enum UserSortField {
  USER_SORT_FIELD_INVALID = 0;
  USER_SORT_FIELD_CREATED = 1;
  USER_SORT_FIELD_FIRST_NAME = 2;
  USER_SORT_FIELD_LAST_NAME = 3;
  USER_SORT_FIELD_EMAIL = 4;
  USER_SORT_FIELD_LAST_LOGIN = 5;
}

// Options for controlling the order of query results
message UserSortOptions {
  // A direction to sort results in
  SortDirection direction = 1;
  // Field to sort on
  UserSortField field = 2;
}

message ListUsersRequest {
  // The partner to list users for.
  // This field is required.
  oneof partner_filter {
    // The partner ID
    string partner_id = 1;
    // All Partners
    bool all_partners = 7;
  }
  // Limit which users are included in the results
  UserFilter user_filter = 2;
  // Which extra properties to include on the listed users
  UserPropertyMask property_mask = 3;
  // Cursor allows paging of results, the next_cursor returned by the last response is expected.
  string cursor = 4;
  // The number of results to return
  int64 page_size = 5;
  // ListUserSortField, defaults to created ascending if no sort options provided
  repeated UserSortOptions sort_options = 6;
}

message ListUsersResponse {
  repeated User users = 1;

  // Paging info
  vendastatypes.PagedResponseMetadata paging_metadata = 4;
}

message ResetPasswordRequest {
  // The password reset token which was sent in a password reset email
  string reset_password_token = 1;
  // The new password to set on the user
  string new_password = 2;
  // Invalidates user's active sessions if reset was successful
  bool invalidate_active_sessions = 4;
}

message AddClientKeyRequest {
  // The user to add a new client key for.
  UserIdentifier user_identifier = 1;
  // The type of key to add
  AlgorithmType algorithm_type = 2;
}

message AddClientKeyResponse {
  // The matching private key for the public key generated by IAM. This key is not stored by IAM and can not be retrieved again.
  string private_key = 1;
  // The identifier of the matching public key.
  string client_key_id = 2;
}

message RemoveClientKeyRequest {
  // The user to remove the key from
  UserIdentifier user_identifier = 1;
  // The identifier for the key to remove
  string client_key_id = 2;
}

message UpdateUserRequest {
  message PhoneNumbers {
    repeated PhoneNumber phone_numbers = 1;
  }
  message Operation {
    oneof operation {
      string first_name = 1;
      string greeting_name = 2;
      string last_name = 3;
      string language_code = 4;
      string email = 5;
      PhoneNumbers phone_numbers = 6;
      Address address = 7;
      string profile_image_url = 8;
      string time_zone = 9;
      LinkedIn linked_in = 10;
      string meeting_booking_url = 11;
      // Set the value for the specified external id type
      // To set the value of multiple types add the operation multiple times
      TypedExternalIdentifier external_id = 12;
    }
  }
  // The user to update
  UserIdentifier user_identifier = 1;
  // The updates to perform
  repeated Operation operations = 2;
}

message AddUserRoleRequest {
  // The user to update
  UserIdentifier user_identifier = 1;
  // The type of role, e.g. "smb", "partner", etc.
  // Your service must own a given role in order to add it to a user.
  string role_id = 2;
  // Attributes associated with the role.
  // These are used in conjunction with policies for Access Checks
  iam.attributes.StructAttribute attributes = 3;
}

message UpdateUserRoleRequest {
  message Operation {
    oneof operation {
      string drop_attribute_key = 1;
      iam.attributes.StructAttribute replace_attributes = 2;
      iam.attributes.StructAttribute add_attributes = 3;
      iam.attributes.StructAttribute remove_attributes = 4;
    }
  }
  // The user to update
  UserIdentifier user_identifier = 1;
  // The type of role, e.g. "smb", "partner", etc.
  // Your service must own a given role in order to add it to a user.
  string role_id = 2;
  // The role updates to perform
  repeated Operation operations = 3;
}

message DeleteUserRequest {
  // The user to delete
  UserIdentifier user_identifier = 1;
}

message RemoveUserRoleRequest {
  // The user to update
  UserIdentifier user_identifier = 1;
  // The type of role, e.g. "smb", "partner", etc.
  // Your service must own a given role in order to remove it from a user.
  string role_id = 2;
}

message UserAndImpersonateeId {
  string user_id = 1;
  string impersonatee_id = 2;
}

message AccessResourceRequest {
  oneof user_identifier {
    string token = 1; // An IAM token. Both identity tokens and access tokens are permitted.
    string user_id = 6; // ID of a user to check access against.
    UserAndImpersonateeId user_and_impersonatee_id = 7; // ID of a user and the impersonatee to check access against.
  }

  // The owner of the resource
  string owner_id = 2;
  string resource_id = 3;
  repeated string actions = 4;
  // A map of resources, keys are caller specified.
  // If ANY resources fail the access check, the call will result in
  // a Permission Denied error with details in the error metadata about which
  // resources failed, identified by the caller-specified keys
  map<string, iam.attributes.StructAttribute> resource_attributes = 5;
}

message AccessResourceResponse {
  string explanation = 1;
  bool success = 2;
  string ai_explanation = 3;
}

// An AccessCheckFailures message contains a list of ResourceIdentifiers that failed the access check
// Intended for use in the grpc error details response for AccessResource
message AccessCheckFailures {
  // A list of the user-provided resource keys that have failed their access checks.
  repeated string failed_keys = 1;
}

message CreateUserRequest {
  // User properties to include on the created user.
  // UserID should be left blank as it will be generated by the server
  // UserRoles should be left blank, add them with "AddUserRole"
  User user = 1;
  // Optionally used to store the ids of the user in the system that triggered the create user request.
  repeated TypedExternalIdentifier typed_external_identifiers = 2;
}

message CreateUserResponse {
  string user_id = 1;
}

message UserRoleMetadata {
  // The role's identifier, e.g. digital_agent, smb, partner
  string role_id = 1;
  // The user who owns this role (typically a service account)
  // Only the owner of a role are allowed to edit or view that role's attributes for a user.
  string owner_user_id = 2;
  // Human readable name for the role
  // E.g. "Digital Agent"
  string name = 3;
  // What is this role used for?
  string description = 4;
}

message RegisterUserRoleMetadataRequest {
  // A description of the role being registered
  UserRoleMetadata user_role_metadata = 1;
}

message ListUserRolesMetadataRequest {
  // Limit the number of results
  int64 page_size = 1;
  // Cursor to begin the list from
  string cursor = 2;
}

message ListUserRolesMetadataResponse {
  repeated UserRoleMetadata user_role_metadatas = 1;

  // Paging info
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message RegisterResourceOwnerRequest {
  // A kebab-case unique identifier for your app
  // e.g. "vendasta-business-center"
  string app_id = 1;
  // A human readable name for your application
  // e.g. "Business Center"
  string app_name = 2;
}

message RegisterResourceRequest {
  // A kebab-case unique identifier for your app
  // e.g. "vendasta-business-center"
  string app_id = 1;
  // A kebab-case identifier for your resource which is unique within your app
  // e.g. "godaddy-domain"
  string resource_id = 2;
  // A human readable name for your resource
  string resource_name = 3;
  // DEPRECATED: Semantic version (vX.X.X) used to track updates to a resource
  // If the specified version is the same as the current resource version updates will not be applied
  // Decreasing the version will update the resource but will not rollback to a previous state.
  string version = 4 [deprecated=true];
}

message ReplaceResourceRequest {
  // Which service owns this resource
  // Should be "lower-kebab-case".
  string owner_id = 1;
  // An identifier for the resource; previously "resource_id".
  // Should be "lower-kebab-case"
  string resource_kind = 2;
  // A human-readable description of when this resource is used for access checks.
  string resource_description = 3;
  // map of action to policy for this resource
  map<string, iam.policies.PolicyNode> policies = 4;
  // DEPRECATED: Semantic version (vX.X.X) used to track updates to a resource
  // If the specified version is the same as the current resource version updates will not be applied
  // Decreasing the version will update the resource but will not rollback to a previous state.
  string version = 5 [deprecated=true];
  // Contains data required for registering callback resources.
  CallbackResourceData callback_resource_data = 6;
}

message RegisterPolicyRequest {
  // The policy to register
  Policy policy = 1;
}

message DeletePolicyRequest {
  string app_id = 1;
  string resource_id = 2;
  string policy_id = 3;
}

message UpsertPartnerLimitsRequest{
  // Partner ID
  string partner_id = 1;
  // ID that maps to the limits of the Partner
  string limits_id = 2;
}

message ExchangeClientKeyAssertionRequest {
  // 'assertion' is a JWT credential signed by a Client Private Key.
  // Vax will typically handle all of this for you.
  //
  // Example claims:
  // {
  //      iss: my-app-id-or-pid
  //      aud: vendasta.com
  //      sub: <EMAIL>
  //      kid: my-public-key-id
  //      /* other claims like exp, iat, as normal */
  // }
  string assertion = 1;
}

message ExchangeClientKeyAssertionResponse {
  // token is an IAM token
  string token = 1;
}

message GetResetPasswordTokenRequest {
  // The user to fetch a password reset token for
  UserIdentifier user_identifier = 1;
}

message GetResetPasswordTokenResponse {
  // A one-time-use, time-sensitive token that can only be used to reset the password.
  string reset_password_token = 1;
}

message GetTokenForUserRequest {
  oneof id {
    // subject_id of the user
    string subject_id = 1;
    // email of the user
    string email = 2;
    // user id of the user
    string user_id = 3;
    // refresh token given by a previous call of get session
    string refresh_token = 4;
    // Assertion signed by a private key provided by a client using 2-legged oauth flow
    string client_assertion = 5;
  }
  // The name of the Identity Provider used to federate identity, e.g. google
  string federated_identity_provider = 6;
  // Scopes to be added to the access token
  repeated string scopes = 7;
  // The client id that the session is being created for.
  // If an client_id is not passed, the client_id will fall back to IAM as the client_id.
  // For OAuth2 a client_id should always be given, unless providing a client_assertion as your ID.
  string client_id = 8;
  // When an ID token is generated, a nonce field is used for to associate the client to the given id token and
  // mitigate replay attacks.
  // https://openid.net/specs/openid-connect-core-1_0.html#IDToken
  string nonce = 9;
  // The user ID of the user being impersonated (optional)
  string impersonatee_user_id = 10;
  // Service provider id that the tokens are to be made for and returned to
  string service_provider_id = 11;
  // legacy_user_id is the subject ID that acted as the user ID in ssov3 marketplace integrations
  // Some vendors use this to maintain backwards compatibility with marketplace APIs.
  string legacy_user_id = 12;

  // The user ID of the person who provided consent for the scopes of a service account user.
  // This field is primarily used when tokens are issued to automations where we want
  // to track the user who activated the automation on behalf of the service account.
  string consenting_user_id = 13;

  // The token's validity start time.
  google.protobuf.Timestamp valid_at = 14;

  // The token's validity expiration time.
  google.protobuf.Timestamp expires_at = 15;
}

// Response iam.v2.IAMSSO/GetTokenForUser
message GetTokenForUserResponse {
  // An IAM identity token, which is different than oidc_identity_token.
  string identity_token = 1;
  // An IAM access token
  string access_token = 2;
  // A refresh token for the iam access token
  string refresh_token = 3;
  // An OpenID Connect ID Token.
  // https://openid.net/specs/openid-connect-core-1_0.html#IDToken
  string oidc_identity_token = 4;
}

message VerifyEmailRequest {
  // Email verifier token
  string token = 1;
  // Request OTP Code
  bool request_otp_code = 2;
}

message VerifyEmailResponse {
  // OTP Code, if requested
  string otp_code = 1;
}

// EmailBranding how the email will be displayed, i.e. who it will look like sent the email
// Deprecated: use force_email_branding_partner_id instead.
enum EmailBranding {
  // If no value, will default to Partner branding
  EMAIL_BRANDING_INVALID = 0;
  // Email will be branded as that of the Partner
  EMAIL_BRANDING_PARTNER = 1;
  // Email will be branded as the parent partner, i.e. the reseller of the platform (currently always Vendasta)
  EMAIL_BRANDING_PARENT_PARTNER = 2;
}

message SendEmailVerificationRequest {
  // ID of user to send email verification to
  UserIdentifier user_identifier = 1;
  //reserved EmailBranding email_branding = 2
  reserved 2;
  reserved "email_branding";
  // Provide a url for the user to navigate to after their email has been verified
  string next_url = 3;
  // Provide the text for the button the user will click to navigate to the provided next_url
  string next_url_button_text = 4;
  // By default, the verification email will be sent with whitelabel branding inferred from the given UserIdentifier.
  // You can optionally set this field to a specific partner identifier to use a specific partner's whitelabel branding.
  string force_email_branding_partner_id = 5;
  // Optional ReCaptcha token for public email verification calls: https://developers.google.com/recaptcha/docs/verify
  string recaptcha_token = 6;
}

message SendOneTimePasswordEmailRequest {
  // User ID to send the email to
  string user_id = 1;
  // the template id to use for the email
  string template_id = 2;
  // Substitution parameters
  // Supports dot pathing in the template content; for example, given `productName['.Workflow.Step1.ProductId']` will go Workflow > Step1 > ProductId into
  // parameters and get the associated value
  google.protobuf.Struct parameters = 3;
  // the subject of the email
  string subject = 4;
  // Provide a url for the user to navigate to after exchanging the OTP for a session
  string next_url = 5;
  // Provide the text for the button the user will click to navigate to the provided next_url
  string next_url_button_text = 6;
}

message GetMultiExternalIDRequest {
  // The list of external ids to fetch
  repeated UserIdentifier user_identifiers = 1;

  // Which type of external ids to fetch.
  string external_id_type = 2;
}

message GetMultiExternalIDResponse {
  // The external ids.
  repeated string external_ids = 1;
}

message CreateExternalIDRequest {
  // The identifier for the user to create an external id for.
  UserIdentifier user_identifier = 1;

  // The type and value of external ID to create.
  TypedExternalIdentifier typed_external_identifier = 2;
}

message ListSecurityLogsRequest {
  // Filters which can be applied to this list request
  message Filters {
    // Filter the list by an actionID
    string action_id = 1;
  }
  // User Identifier to list for
  UserIdentifier user_identifier = 1;
  // Optionally apply filters
  Filters filters = 2;
  // Limit the number of results
  int64 page_size = 3;
  // Cursor to begin the list from
  string cursor = 4;
}

message ListSecurityLogsResponse {
  // The list of logs
  repeated SecurityLog logs = 1;
  // Paging info
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

// The list of restriction types which can be applied to Users
enum RestrictionType {
  // Unset
  RESTRICTION_TYPE_UNSET = 0;
  // This restriction prevents user from logging in. Automatically cleared on password reset.
  RESTRICTION_TYPE_RESET_PASSWORD_BEFORE_LOGIN = 1;
  // This restriction prevents user from logging in or passing any access checks.
  RESTRICTION_TYPE_FROZEN = 2;
}

message AddMultiUserRestrictionRequest {
  // The list of users to add the restriction to
  repeated UserIdentifier user_identifiers = 1;

  // The restriction type to add
  RestrictionType restriction_type = 2;
}

message RemoveMultiUserRestrictionRequest {
  // The list of users to remove the restriction from
  repeated UserIdentifier user_identifiers = 1;

  // The restriction type to remove
  RestrictionType restriction_type = 2;
}

message ChangePasswordRequest {
  // The user to attempt to change their password for
  UserIdentifier user_identifier = 1;
  // The current password the user has
  string current_password = 2;
  // The desired new password
  string new_password = 3;
}

// SecurityLog represents a security log
message SecurityLog {
  // User ID that this log is for
  string user_id = 1;
  // The action which was taken
  string action_id = 2;
  // A unique ID to identify this specific log
  string log_id = 3;
  // A description of what occurred
  string description = 4;
  // The time this log was created
  google.protobuf.Timestamp created = 5;
}

// Credentials to be passed for an OAuth workflow
message OAuthCredentials {
  // provider name, ie: "google"
  string provider = 1;
  oneof token {
    string id_token = 2;
    string access_token = 3;
  }
}

// A users email password combination
message PasswordCredentials {
  // user's email
  string email = 1;
  // user's password
  string password = 2;
}

// A user and a one time password
message OneTimePasswordCredentials {
  // user's id
  string user_id = 1;
  // user's one time password
  string one_time_password = 2;
}

message CreateSessionRequest {
  // Namespace to search for the user in
  string namespace = 1;
  oneof credentials {
    OAuthCredentials oauth_credentials = 2;
    PasswordCredentials password_credentials = 3;
    OneTimePasswordCredentials one_time_password_credentials = 4;
  }
  repeated string subject_types = 5;
}

// A CreateSessionFailure message contains an identifying code as to what caused the request
// to fail.
message CreateSessionFailure {
  // Code representing the failure reason
  // federated_identity_required: User must sign in using a federated identity provider
  // password_mismatch: Provided password did not match the user's password
  // locked: User has failed to log in too many times and is now locked for a period of time
  // frozen: User's account has been frozen by an administrator and can no longer log in
  // password_reset_required: User must reset their password before they can log in using with a PasswordCredentials request
  // email_validation_required: User must verify their email before they are able to log in
  string error_code = 1;
}

message CreateSessionResponse {
  // A session token (aka an IAM session)
  string session = 1;
}

message UpsertExternalIDRequest {
  // The identifier for the user to update/insert an external id for.
  UserIdentifier user_identifier = 1;

  // The type and value of external ID to insert/update.
  TypedExternalIdentifier typed_external_identifier = 2;
}

message ListResourcesRequest {
  // Cursor allows paging of results, the next_cursor returned by the last response is expected.
  string cursor = 1;
  // The number of results to return.
  int64 page_size = 2;

  message Filters {
    // Filter to resources that support at least one of the specified role types
    repeated string role_types = 1;
    // Filter to resources that contain at least one of the specified resource ids
    repeated string resource_ids = 2;
    // Filter to resources that contain at least one of the specified resource attributes
    repeated string resource_attributes = 3;
    // Filter to resources that contain at least one of the specified subject attributes on its policies
    repeated string subject_attributes = 4;
    // Filter to resources that contain at least one of the specified actions on its policies
    repeated string actions = 5;
    // Filter to resources that contain at least one of the specified app ids
    repeated string app_ids = 6;

  }
  Filters filters = 3;

  string search_term = 4;
}

message CallbackResourceData {
  repeated string required_resource_parameters = 1;
  string resource_owner_service_url = 2;
  string resource_owner_audience= 3;
}

message Resource {
  string app_id = 1;
  string resource_id = 2;
  string resource_name = 3;
  string vstore_link = 4;
  CallbackResourceData callback_resource_data = 5;
  google.protobuf.Timestamp created = 6;
  google.protobuf.Timestamp updated = 7;
  repeated Policy policies = 8;
  repeated string supported_role_types = 9;
  // The inferred set of resource attributes
  repeated string resource_attributes = 10;
  // Inferred list of actions across all policies on the resource
  repeated string actions = 11;
  // DEPRECATED: Semantic version (vX.X.X) used to track updates to a resource
  string version = 12 [deprecated=true];
}

message ListResourcesResponse {
  repeated Resource resources = 1;
  string next_cursor = 2;
  bool has_more = 3;
}

message SearchUsersRequest {
  string search_term = 1;
  // Cursor allows paging of results, the next_cursor returned by the last response is expected.
  string cursor = 2;
  // The number of results to return.
  int64 page_size = 3;
}

message SearchUsersResponse {
  repeated User users = 1;

  // Paging info
  vendastatypes.PagedResponseMetadata paging_metadata = 4;
}

message DeleteResourceAndPoliciesRequest{
  string app_id = 1;
  string resource_id = 2;
}

message CreatePamGrantRequest {
  string user_id = 1;
  string entitlement_id = 2;
  string reason = 3;
  string partner_id = 4;
  string account_group_id = 5;
}

message ListPamGrantsRequest {
  message GrantFilters {
    string user_id = 1;
    string partner_id = 2;
    string account_group_id = 3;
    string entitlement_id = 4;
  }
  GrantFilters filters = 1;
  bool include_expired = 2;
  vendastatypes.PagedRequestOptions paging_options = 3;
}

message PamGrant {
  string grant_id = 1;
  string user_id = 2;
  string partner_id = 3;
  string account_group_id = 4;
  string entitlement_id = 5;
  string reason = 6;
  google.protobuf.Timestamp last_used_for_access_at = 7;
  google.protobuf.Timestamp expires_at = 8;
  google.protobuf.Timestamp max_expires_at = 9;
}

message ListPamGrantsResponse {
  repeated PamGrant grants = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message Entitlement {
  string id = 1;
  string name = 2;
  string description = 3;
}

message GetEntitlementRequest {
  string entitlement_id = 1;
}

message GetEntitlementResponse {
  Entitlement entitlement = 1;
}
