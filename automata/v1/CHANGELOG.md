## 0.162.0
- Add `EvaluateStepDataForAutomation` and `UpdateStepDataForAutomation` to `Automata` service

## 0.161.0
- Add `ENTITY_TYPE_CUSTOM_OBJECT` to EntityType

## 0.160.0
- Add `ActivateOrUpdateAutomation` rpc to `Automata` service
- Remove `run_with_user_id` and `action` from `StartAutomationRunningRequest`

## 0.159.0
- Add `run_with_user_id` and `action` to `StartAutomationRunningRequest`

## 0.158.0
- Add `automation` scope to `CreateAutomation` and `ListAutomationsV2`

## 0.157.0
- Add `DATA_TYPE_CRM_CUSTOM_OBJECT_ID` to DataType

## 0.156.0
- Add `tags` to `TaskDefinition`

## 0.155.0
- Add `is_default_automation` and `is_ephemeral_automation` to `Activity`

## 0.154.0
- Add `is_ephemeral_automation` to `Automation`
- Add `AUTOMATION_TYPE_EPHEMERAL` to `AutomationType`

## 0.153.0
- Add `next_workflow_step_branch_taken_index`

## 0.152.0
- Add `goal` to `Activity`

## 0.151.0
- Add `STAGE_WORKFLOW_COMPLETED_FROM_GOAL` as a stage for `Activity`

## 0.150.0
- Add `started_processing_min` and `started_processing_max` to `ListAutomationRunsRequest` filters

## 0.149.0
- Add `invoice` data type

## 0.148.0
- Add `goal` to automation

## 0.147.0
- Add `starting_user_id` to List Automation Runs filter

## 0.146.0 
- Add `consenting_user_id` to `token`

## 0.145.0
- remove `access_attributes` from `TaskDefinition`

## 0.144.0
- Add `DATA_TYPE_CRM_OPPORTUNITY_ID` to DataType

## 0.143.0
- Add `notes` to `Automation` `Group`

## 0.142.0
- Add `name` to `ListSnippetsRequest.Filters`

## 0.141.0
- Add `ListSnippets` endpoint

## 0.140.0
- Remove `GetAutomationUsage` endpoint

## 0.139.1
- Add `context` to `StopAllAutomationRunsRequest`
- 
## 0.138.0
- Add `snippet` as automation type. Also added fields to tell if an automation is a snippet to the automation itself

## 0.138.0
- Add `namespace` to `StartAutomationRequest` and `StartAccountGroupAutomationRequest`

## 0.137.0
- Add context to the manual automation search so that we can search system manual automations

## 0.136.0
- Add `group_id` to `Automation` `WorkflowStep`

## 0.135.0
- Add `groups` to `Automation` message

## 0.134.0
- Add `repeated` to `Parameter` message

## 0.133.0
- Add `parent_namespace` and `exclude_platform_automations` to the `ListAutomationsRequestV2`

## 0.132.0
- Add `check_access_only` to `StartAutomationRequest`

## 0.131.0
- Add static validation rules to task definition message 

## 0.130.0
- Add more primitive data types to match CRM

## 0.129.0
- Add Connection integration data type 

## 0.128.0
- Add `required_apps` to the TaskDefinition

## 0.127.0
- Deprecate `trigger_definition_id` on ListTaskDefinition api

## 0.126.0
- Allow multiple entity types for search manual automations

## 0.125.1
- Change `ListAutomationsV2` to use a repeated trigger definition ID

## 0.125.0
- Add `ListAutomationsV2`

## 0.124.0
- Add the rest of the CRM data types

## 0.123.0
- Remove `CallLogging` service
  - Was never used

## 0.122.0
- Update `SleepWaitConditions` to include day-of-month

## 0.121.0
- Add `ExternalID` to `Automation` 

## 0.120.0
- Add CRM Email, Meeting and Call data types

## 0.119.0
- Add Note data type

## 0.118.0
- Update `SleepWaitConditions` to include time ranges

## 0.117.0
- Add `SleepWaitConditions` to `UpdateAutomation`

## 0.116.0
- Add `trigger_task_definition_id` to `ListTaskDefinitionRequest.ValidationData`

## 0.115.0
- Add `path` to `OutputParameter`
  - Used to map an output to a different key on the stream data

## 0.114.0
- Add `choices` to the TaskDefinition

## 0.113.0
- Add `trigger_definition_id` to `ListTaskDefinitionsRequest.ValidationData`

## 0.112.0
- Add SummarizeAutomation to Automata 

## 0.111.0
- Deprecate the rest of the partner_id fields in favor of namespace

## 0.110.0
- Add `GetAutomationUsage` to `Automata` service

## 0.109.0
- deprecate usages of partner id and replace them with namespace

## 0.108.0
- Add `Namespace` to `Automation` to eventually replace PartnerID

## 0.107.0
- Add `Company` and `Contact` as entity types

## 0.106.0
- Add `GetAutomationConfigurationV2` to `AutomataDefaultAutomations` service

## 0.105.0
- Add `ListDefaultAutomations` to `AutomataDefaultAutomations`

## 0.104.0
- add `supported_contexts` to `TaskDefinition`
- add `supported_contexts` to the `ListTaskDefinitionsRequest` filters

## 0.103.0
- Add "Task" DataType

## 0.102.0
- Deprecate `market_reliant`

## 0.101.0
- Remove `AutomationConfigurationV2` usages
- Add enable/disable `AutomationConfigurationV2`

## 0.100.0
- Refactor around the automation `Context`
- Update `AutomationConfigurationV2` to not have FieldData and to have Context

## 0.99.0
- Reformat `StartAutomationRequest` to be able to send a payload dictionary.

## 0.98.0
- Add `DATA_TYPE_PRODUCT` as data types

## 0.97.0
- Add `flattened_config` to `EventTriggerDefinition`

## 0.96.0
- Add `notes` to automations and steps

## 0.95.0
- Add `event_sub_type_id` to TaskDefinition

- ## 0.94.0
- Add `trigger_sub_definition_id` to Automation

## 0.93.0
- Remove `context` from `AutomationConfigurationV2`

## 0.92.0
 - Add `DATA_TYPE_JSON` `DATA_TYPE_COMPANY_ID` `DATA_TYPE_CONTACT_ID` as data types

## 0.91.2
- Add `context` to ListAutomations filters

## 0.91.1
- Add `repeated` to `GetMultiAutomationConfigurationResponse`

## 0.91.0
- Add `DefaultConfigurationV2` and associated CRU to `Automata` service
- Add `context` and `default_fields` to Automation

## 0.90.0
- Add `is_entity` to `OutputParameter`

## 0.89.0
- Add `workflow_step_parent_ids` to `ListTaskDefinitionsRequest.ValidationData`

## 0.88.0
- Add `DATA_TYPE_OPPORTUNITY_ID` to DataType

## 0.87.0
- Add `ENTITY_TYPE_NO_ENTITY` to EntityType

## 0.86.0
- Add `DATA_TYPE_EMAIL_EVENT` to `DataType` enum

## 0.85.0
- Add `DATA_TYPE_EMAIL_ID` to `DataType` enum

## 0.84.0
- Remove `SearchAccountGroupManualAutomations` use `SearchManualAutomations` instead

## 0.83.0
- Add `RUN_TYPE_ONE_AT_A_TIME` to RunType

## 0.82.0
- Add `cleared_data_flag` to `WorkflowStep`

## 0.81.0
- Add `GetMultiTaskDefinition` to `AutomataDevelopment`

## 0.80.0
- Add `market_reliant` to `TaskDefinition` and `ListTaskDefinitionsRequest`'s `Filters`

## 0.79.0
- Add `SearchManualAutomations` to replace `SearchAccountGroupManualAutomations` so that it's usable for both account groups and orders
- Deprecated `SearchAccountGroupManualAutomations`

## 0.78.0
- Add `pre_processors` to `TaskDefinition`

## 0.77.0
- Rejigger `GetAutomationAccessRequirementsRequest`

## 0.76.0
- Add `GetAutomationAccessRequirements` rpc to `Automata` service with `GetAutomationAccessRequirementsRequest` and `GetAutomationAccessRequirementsResponse`
- Define `GetAutomationAccessRequirementsRequest` and `GetAutomationAccessRequirementsResponse` messages

## 0.75.0
- Add `StopAllAutomationRuns` rpc to `Automata` service with `StopAllAutomationRunsRequest`
- Define `StopAllAutomationRunsRequest` message

## 0.74.0
- Add `brokenTime` to `Token` message

## 0.73.0
- add `token` field to `Automation` message
- define `Token` message
- Remove `Owner` message definition
- Reserve `owner` and `17` on `Automation` message definition

## 0.72.0
- add `additional_fields` to `SendTestWebhookRequest`

## 0.71.0
- Add `token_user_id` to `ListAutomationsRequest` filters

## 0.70.0
- Add scope to `RateLimitCheck`

## 0.69.0
- Add `EmitAutomationEvent` RPC to the `AutomataOperations` service

## 0.68.0
- Add `workflow_step_task_definition_id` and `event_trigger_definition_id` to `ListAutomationsRequest`

## 0.67.0
- Add `CountRunningWorkflows` to `Automata` service

## 0.66.0
- Remove `action_list_id` from `StartAccountGroupAutomation`

## 0.65.0
- Add `action_list_name` to `StartAccountGroupAutomation` and deprecate `action_list_id`

## 0.64.0
- Add new Stage enums for Activity: `STAGE_WORKFLOW_CONTINUED` and `STAGE_WORKFLOW_CONTINUED_FROM_ERROR`

## 0.63.0
- Add `ListAutomationRuns` rpc to `Automata` service

## 0.62.0
- Deprecate `event_trigger_definition_id` on `GetWorkflowStepPredecessorOutputParamsRequest`

## 0.61.0
- Add `SetAutomationToDraining` to `Automata` service

## 0.60.0
- Add `ContinueAutomationFromStep` RPC to continue running an automation from a specified step using the activity data of the automation that ran previously

## 0.59.1
- Add `main_run_id` and `step_execution_uuid` to `Activity` and deprecate the usage of `run_id` in the apis

## 0.58.0
- Add `wait_for_data_type_scope` to `ListTaskDefinitionsRequest` filters

## 0.57.0
- Add `StartMigration` rpc
- 
## 0.56.0
- Add `event_type_id` to `TaskDefinition`

## 0.55.0
- Add `DATA_TYPE_ACCOUNT_GROUP_ID`, `DATA_TYPE_ORDER_ID`, `DATA_TYPE_SMB_USER_ID`, and `DATA_TYPE_FULFILLMENT_PROJECT_ID` to `DataType` enum

## 0.54.0
- Add `event_trigger_definition_id` to `GetWorkflowStepPredecessorOutputParamsRequest`

## 0.53.0
- change `market_id` filter on `SearchAccountGroupManualAutomationsRequest` to `market_ids` and deprecate `market_id`

## 0.52.0
- move `call logging` protos to new service

## 0.51.0
- Add `only_errors` to the `ListActivitiesRequest` to only return the activities with errors

## 0.50.0
- add `call_ogging` protos

## 0.49.0
- Add `parameter_filter_keys` property to the `EventTriggerDefinition`

## 0.48.0
- Add `wait_for_trigger_filter` to `WorkflowStep`

## 0.47.0
- Change `action_list_name` property on `StartAccountGroupAutomationRequest` to `action_list_id` as it needs to be an id, not the name.

## 0.46.0
- Add `last_edited_by` to `EventTriggerDefintion` and `TaskDefinition`

## 0.45.0
- Add `DATA_TYPE_ORDER_DECLINED_REASON_IDS` to `DataType` enum
- 
## 0.44.0
- Add name filters to `ListEventTriggerDefinitions` and `ListTaskDefinitions` apis

## 0.43.0
- Add `entity_id` and `workflow_step_id` to `EndAutomationRun` 
- Add `end_run_requested` to `Activity`

## 0.42.0
- Add `EndAutomationRun` to `AutomataOperations`

## 0.41.0
- Add `DATA_TYPE_ORDER_ACTION_TYPE` to `DataType` enum

## 0.40.0
- Add `GetWorkflowStepPredecessorOutputParams` api

## 0.39.0
- Add `custom_output_parameters` to `WorkflowStep`
 
## 0.38.1
- Add `DataType` to `Parameters`

## 0.37.0
- Add `name` to `WorkflowStep`

## 0.36.0
- Add `SendTestWebhook` to `AutomataOperations`

## 0.35.0
- Add `StartAutomation` to `Operations` service

## 0.34.0
- Add `required_subscription_features` to `TaskDefinition`

## 0.33.0
- Add `ENTITY_TYPE_USER` to `EntityType`

## 0.32.0
- Add `entity_type` to `Automation`

## 0.31.1
- Remove running version from `ListActivitiesCurrentlyWaitingRequest`

## 0.31.0
- Add `ListActivitiesCurrentlyWaitingRequest` to `AutomationSerice`

## 0.30.0
- Add `subscribe_to_notifications` to `StartAutomationRunningRequest`

## 0.29.0
- Add `GetSubscription` to `AutomationSerice`

## 0.28.1
- Change `Subscribe` and `Unsubscribe` to not take a subscriber

## 0.28.0
- convert `subscriber` on an `Automation` to it's own `AutomationSubscriber` entity

## 0.27.0
- add `subscriber` to `StartAutomationRunningRequest`

## 0.26.0
- Remove deprecated APIs

## 0.25.0
- Add `subsribers` to `Automation`
- Add `Subscribe` and `Unsubscribe` rpcs

## 0.24.0
- Add `hidden` to `Automation`

## 0.23.0
- Add `wait_for_trigger_task_definition_id` to `WorkflowStep`

## 0.22.0
- Add scopes to event trigger definition rpcs

## 0.21.1
- Change `run_id` to `runnning_version` in `RateLimitCheck`

## 0.21.0
- Add `Limit` to the `RateLimitCheck`

## 0.20.0
- Add `RateLimitCheck` to the `AutomataOperations` service

## 0.19.0
- intentionally left blank

## 0.18.0
- Add a timzeone to the automation settings

## 0.17.0
- Add scopes to all `TaskDefinition` apis

## 0.16.0
- Add `ListEventTriggerDefinitions` to the `AutomataDevelopment` service

## 0.15.0
- Add scope to `ListTaskDefinitions`

## 0.14.0
- Add `automation_type` to `ListActivitiesRequest`

## 0.13.0
- Add `GetMultiAutomations` RPC to the Automata service
- Add filter `latest_per_automation` to `ListActivitiesRequest` for returning only the latest activity per automation

## 0.12.0
- Add `tags` to `ListDefaultAutomationsForPartnerRequest`
- Add to `automation_type` `GetAutomationTagsRequest`

## 0.11.0
- Add deprecations to: AutomataTemplates service, and various RPCs in AutomataDefaultAutomations service

## 0.10.0
- Add `Unpublish` RPC to `Automata` service

## 0.9.0
- Add Publish to Automations service

## 0.8.0
- Add source_id to `CreateAutomationRequest`

## 0.7.0
- Add new enum `AutomationStatus` to the API protos, add it as a new property on the `ListAutomations` filter (for default automations)

## 0.6.0
- Add source_id to `CreateAutomationRequest`

## 0.5.0
- Consolidate the List APIs (for standard, default, and template automations) into a single API
  - Add new enum `AutomationType` to the API protos for the purpose of specifying which type of automation; add a property `automation_type` that uses this enum within the List API filters
  - Add generic enum `AutomationPublishedState` and add a property to the filters on the List API that will apply to the type specified

## 0.4.0
- Added stage filter to `ListActivitiesForDefaultAutomationRequest`

## 0.3.0
- Added `STAGE_WORKFLOW_DID_NOT_RUN` to the `Activity` stages
- Added stage filter to `ListActivitiesRequest`

## 0.2.0
- Added `SearchAutomationTags` rpc to `Automata` service

## 0.1.0
- Start tracking explicit version
- Added `tags` to `Automation`
