syntax = "proto3";

package automata.v1;

option go_package = "github.com/vendasta/generated-protos-go/automata/v1;automata_v1";
option java_outer_classname = "AutomationProto";
option java_package = "com.vendasta.automata.v1.generated";

import "google/protobuf/timestamp.proto";
import "automata/v1/choice.proto";
import "automata/v1/parameter.proto";
import "automata/v1/context.proto";

enum RunningState {
    RUNNING_STATE_NOT_SPECIFIED = 0;
    RUNNING_STATE_RUNNING = 1;
    RUNNING_STATE_STOPPED = 2;
    RUNNING_STATE_DRAINING = 3;
}

enum RunType {
    RUN_TYPE_NOT_SPECIFIED = 0;
    RUN_TYPE_ONLY_ONCE = 1;
    RUN_TYPE_MULTIPLE = 2;
    RUN_TYPE_ONE_AT_A_TIME = 3;
}

enum ErrorHandlingType {
    ERROR_HANDLING_TYPE_UNDEFINED = 0;
    ERROR_HANDLING_TYPE_STOP_ON_ERROR = 1;
    ERROR_HANDLING_TYPE_CONTINUE_ON_ERROR = 2;
}

enum EntityType {
  ENTITY_TYPE_NOT_SPECIFIED = 0;
  ENTITY_TYPE_ACCOUNT_GROUP = 1;
  ENTITY_TYPE_ORDER = 2;
  ENTITY_TYPE_USER = 3;
  ENTITY_TYPE_NO_ENTITY = 4;
  ENTITY_TYPE_CONTACT = 5;
  ENTITY_TYPE_COMPANY = 6;
  ENTITY_TYPE_CUSTOM_OBJECT = 7;
}

message WaitForTriggerFilter {
    string key = 1;
    string value = 2;
}

// SleepWaitConditions define the conditions that must be met before the
// automation can run.
//
// Note 1: Unless otherwise configured here, the time_range values are relative
// to the timezone specified in the Automation's settings.
//
// Note 2: Setting all of the "time_range_" values to 0 means that the automation
// can run at any time of day - assuming the other wait conditions are also met.
//
message SleepWaitConditions {
  repeated string weekday_selection = 1;
  // The hour component for the earliest time where it is acceptable to run the
  // automation (see notes above)
  int32 time_range_start_hour = 2;
  // The minute component for the earliest time where it is acceptable to run
  // the automation (see notes above)
  int32 time_range_start_minute = 3;
  // The hour component for the latest time where it is acceptable to run the
  // automation (see notes above)
  int32 time_range_end_hour = 4;
  // The minute component for the latest time where it is acceptable to run the
  // automation (see notes above)
  int32 time_range_end_minute = 5;
  // The day of the month that the automation can run on. Valid values are 1-32,
  // where 32 is used to select the last day of the month (28, 29, 30, or 31).
  repeated int32 day_of_month_selection = 6;
}

message WorkflowStep {
    reserved 9;
    string id = 1;
    string task_definition_id = 2;
    string data = 3;
    Choices choices = 4;
    // sleep duration in nanoseconds
    int64 sleep = 5;
    // WaitForTriggerDefinitionID is the TriggerDefinitionID of the event that the step should wait for
    string wait_for_trigger_definition_id = 6;
    // WaitForTriggerTaskDefinitionID is the id of the TaskDefinition that should run when the trigger happens
    string wait_for_trigger_task_definition_id = 7;
    string name = 8;
    // Custom output parameters for the workflow step that will be used in addition to the step's task definition output parameters
    repeated OutputParameter custom_output_parameters = 10;
    // wait_for_trigger_filter a key-value pair for which filtering on the incoming trigger event, e.g., "task_id" & "{.WorkflowStep.123.task_id}"
    WaitForTriggerFilter wait_for_trigger_filter = 11;
    bool cleared_data_flag = 12;
    string notes = 13;
    SleepWaitConditions sleep_wait_conditions = 14;
    string group_id = 15;
}

message AutomationSettings {
    RunType run_type = 1;
    ErrorHandlingType error_handling_type = 2;
    string time_zone = 3;
}

message Token {
    // The iam.User id of the user with whose auth the automation is running with
    string user_id = 1;
    // The scopes that have already been granted to this automation by the user
    repeated string scopes = 2;
    // When the refresh token expires
    google.protobuf.Timestamp refresh_token_expiry = 3;
    // The time at which it was discovered that the token was broken
    google.protobuf.Timestamp broken_time = 4;
    // The iam.User id of the user that consented to the scopes of the automation
    // to be run using a service account. If this value is set then the user_id
    // above is the one of a service account.
    string consenting_user_id = 5;
}

message Group {
    string id = 1;
    string name = 2;
    string notes = 3;
}

message Automation {
    string id = 1;
    // PartnerID is no longer supported and Namespace+Context combo should be used instead
    string partner_id = 2 [deprecated = true];
    // MarketID is no longer supported on new automations and will eventually be removed
    string market_id = 3 [deprecated = true];
    string trigger_definition_id = 4;
    string trigger_sub_definition_id = 31;
    string name = 5;
    RunningState running_state = 6;

    google.protobuf.Timestamp created = 7;
    google.protobuf.Timestamp updated = 8;
    google.protobuf.Timestamp deleted = 9;

    // Workflow steps
    repeated WorkflowStep workflow = 10;
    reserved 11;
    // Workflow step containing trigger definition, first step
    WorkflowStep trigger_step = 12;
    //iam.User.Id of the user that most recently modified this automation
    string last_edited_by = 13;
    AutomationSettings settings = 14;
    // Filter steps executed after the trigger and before the workflow itself
    repeated WorkflowStep trigger_filters = 15;
    // Running version for automation, empty if it's not running
    google.protobuf.Timestamp running_version = 16;

    reserved "owner";
    reserved 17;

    // Description of the automation.
    string description = 18;

    // Specifies if the automation is a template
    bool is_template = 19;
    // Time that the template was published, empty if automation is not a template or not published
    google.protobuf.Timestamp template_published_time = 20;

    // Specifies if the automation is a default automation
    bool is_default_automation = 21;
    // Time that the default automation was published, empty if automation is not a default automation or not published
    google.protobuf.Timestamp default_automation_published_time = 22;

    // The id of the template from which the automation was derived
    string origin_template_id = 23;

    // The id of the default automation from which the automation was derived
    string origin_default_automation_id = 24;

    repeated string tags = 25;

    // Flag to determine if the automation is hidden or not from partners, if it can be hidden
    bool hidden = 26;

    // The entity type that the automation centers around
    EntityType entity_type = 27;

    // The token associated with permissions for an automation
    Token token = 28;

    // The context the automation is running in
    Context context = 29;

    // 31 is being used by the trigger_sub_definition_id

    string notes = 32;

    string namespace = 33;

    string external_id = 34;
    repeated Group groups = 35;

    // Specifies if the automation is a snippet
    bool is_snippet_automation = 36;
    // Time that the default automation was published, empty if automation is not a snippet automation or not published
    google.protobuf.Timestamp snippet_automation_published_time = 37;

    // The (optional) goal of the automation, this is a filter step that when executed and passes the automation is considered to have reached its goal and will end.
    WorkflowStep goal = 38;

    // Ephemeral automations are temporary, live to serve a specific purpose and then be automatically turned off
    bool is_ephemeral_automation = 39;
}

message WorkflowStepOutputParameterContainer {
  WorkflowStep workflow_step = 1;
  repeated OutputParameter output_parameters = 2;
}

message StepDataIdentifier {
    // Identifier for the data key that needs to be passed in to complete the setup for the step
    string key = 1;
    // Identifier for the step that has the missing piece of JSON information 
    string step_id = 2;
    // Identifier for the task definition that explains the purpose of the step and what data it will need to be valid
    string task_definition_id = 3;
}

message StepDataValue {
    // Supported missing data values
    oneof value {
        // Raw string value
        string string_value = 1;
        // Boolean value
        bool boolean_value = 2;
        // Number value
        double number_value = 3;
        // more complex kinds of data could be added here later... 
    }
}

message StepData {
    // ID for user provided step data consists of data_id, step_id and a task definition 
    StepDataIdentifier id = 1;
    // Payload to set up user provided data
    StepDataValue data = 2;
}
