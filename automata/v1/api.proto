syntax = "proto3";

package automata.v1;

option go_package = "github.com/vendasta/generated-protos-go/automata/v1;automata_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.automata.v1.generated";

import "automata/v1/automation.proto";
import "automata/v1/automation_configuration.proto";
import "automata/v1/context.proto";
import "automata/v1/automation_subscription.proto";
import "automata/v1/activity.proto";
import "automata/v1/event_trigger_definition.proto";
import "automata/v1/task_definition.proto";
import "automata/v1/rate_limit.proto";
import "automata/v1/parameter.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/annotations.proto";
import "google/protobuf/struct.proto";


enum AutomationType {
    AUTOMATION_TYPE_NOT_SPECIFIED = 0;
    AUTOMATION_TYPE_STANDARD = 1;
    AUTOMATION_TYPE_DEFAULT = 2;
    AUTOMATION_TYPE_TEMPLATE = 3;
    AUTOMATION_TYPE_SNIPPET = 4;
    AUTOMATION_TYPE_EPHEMERAL = 5;
}

// whether the automation is on or off; relevant to default AutomationType
enum AutomationStatus {
  AUTOMATION_STATUS_NOT_SPECIFIED = 0;
  AUTOMATION_STATUS_ON = 1;
  AUTOMATION_STATUS_OFF = 2;
}

// whether the automation is published or not; relevant only to AutomationTypes default or template
enum AutomationPublishedState {
  AUTOMATION_PUBLISHED_STATE_NOT_SPECIFIED = 0;
  AUTOMATION_PUBLISHED_STATE_PUBLISHED = 1;
  AUTOMATION_PUBLISHED_STATE_DRAFT = 2;
}

message PagedRequestOptions {
    // cursor can be passed to retrieve the next page of results keyed by the cursor
    string cursor = 1;
    // page_size specifies the number of items to return in the next page
    int64 page_size = 2;
}

message PagedResponseMetadata {
    // A cursor that can be provided to retrieve the next page of results
    string next_cursor = 1;
    // Whether or not more results exist
    bool has_more = 2;
    // The total number of results. This is not supported on all paged apis and will be 0 if it is not supported
    int64 total_results = 3;
}

message CreateAutomationRequest {
    Automation automation = 1;
    // Another automation to create this one based off of
    // Requires namespace, name and context set on the automation on the request
    string source_id = 2;
}

message CreateAutomationResponse {
    string id = 1;
}

message GetAutomationRequest {
    string id = 1;
}

message GetAutomationResponse {
    Automation automation = 1;
}

message DeleteAutomationRequest {
    string id = 1;
}

message StartAutomationRunningRequest {
    string id = 1;
    // When starting the automation, you can provide different settings to run this automation with. The automation will be updated to have the
    // new settings, then it will be turned on.
    // If no settings are provided the current settings of the automation will be used.
    AutomationSettings settings = 2;
    //Whether or not the user will automatically be subscribed to notifications for this automation
    bool subscribe_to_notifications = 3;
}

message StopAutomationRunningRequest {
    string id = 1;
}

message SetAutomationToDrainingRequest {
    string id = 1;
}

message UpdateAutomationRequest {
    Automation automation = 1;
    vendastatypes.FieldMask field_mask = 2;
}

message ListAutomationsRequest {
    message Filters {
        string partner_id = 1 [deprecated= true]; // Use namespace instead
        repeated string market_ids = 2 [deprecated=true]; // New automations do not have markets
        string last_updated_by = 3;
        reserved 4;
        bool include_deleted = 5;
        string name = 6;
        repeated RunningState running_states = 7;
        repeated string tags = 8;
        AutomationType automation_type = 9;
        repeated AutomationPublishedState published_states = 10;
        repeated AutomationStatus status = 11;
        // Workflow Step Task Definition ID will filter automations to only those that contain a specific task definition as a workflow step
        string workflow_step_task_definition_id = 12;
        // Trigger Task Definition ID will filter automations to only those that contain a specific task definition as either the trigger step or used in a delay until event step
        string trigger_task_definition_id = 13;
        // Token User ID will filter automations to only those that are using an OAuth2 token supplied by the specified user
        string token_user_id = 14;
        // Context will filter automations to only those that the automation is intended for, e.g.: partner, smb
        Context context = 15;
        string namespace = 16;
    }
    Filters filters = 1;
    PagedRequestOptions paging_options = 2;
}

message ListAutomationsResponse {
    repeated Automation automations = 1;
    // paging metadata
    PagedResponseMetadata paging_metadata = 2;
}

message ListAutomationsRequestV2 {
    message Filters {
        // namespace e.g., "VUNI", "AG-123456"
        string namespace = 1;
        // context e.g.: partner, smb
        Context context = 2;
        repeated AutomationType automation_types = 3;
        // name of the automation (can do partial matches)
        string name = 4;
        repeated RunningState running_states = 5;
        repeated string tags = 6;
        // trigger_task_definition_ids will filter automations to only those that contain a specific task definition as either the trigger step or used in a delay until event step
        repeated string trigger_task_definition_ids = 7;
        // last_updated_by user id of the user who last updated the automation
        string last_updated_by = 8;
        // token_user_id will filter automations to only those that are using an OAuth2 token supplied by the specified user
        string token_user_id = 9;
        // an extra namespace that to look up automations for. In general, this will be used when asking for both the automations of an SMB context and it's partner's
        // namespace at the same time.
        string parent_namespace = 10;
        // Platform automations are automations that we have built and provided to customers
        bool exclude_platform_automations = 11;
    }
    Filters filters = 1;
    PagedRequestOptions paging_options = 2;
}

// Snippet is a group of actions stored as an Automation
message Snippet {
  Automation automation = 1;
  // Fueled by the ValidationData of the request. Will be false if missing required input data
  bool valid = 2;
}

message ListSnippetsRequest {
  message Filters {
    // Namespace the snippets exist in.
    string namespace = 1;
    // Optional. A parent namespace of snippets to include.
    string parent_namespace = 2;
    // Context the snippets are for. (note: partners can list snippets in the SMB context)
    Context context = 3;
    // Which published states to include. (note: partners can create draft snippets for their SMBs)
    repeated AutomationPublishedState published_states = 4;
    // Search for the name of snippet
    string name = 5;
  }
  message ValidationData {
    // The id of the automation to validate the snippet against
    string automation_id = 1;
    // The ids of the steps immediately above where we are wanting to add the snippet
    repeated string immediate_step_parent_ids = 2;
  }
  Filters filters = 1;
  // Optional. Used to see if the snippet is valid to be added to an automation at a certain point
  ValidationData validation_data = 2;
  PagedRequestOptions paging_options = 3;
}

message ListSnippetsResponse {
  repeated Snippet snippets = 1;
  PagedResponseMetadata paging_metadata = 2;
}

message ListAutomationRunsRequest {
  message Filters {
    string partner_id = 1 [deprecated= true]; //Deprecated: use namespace instead
    repeated string market_ids = 2;
    string automation_id = 3;
    string entity_id = 4;
    repeated Stage stages = 5;
    bool show_default = 6;
    string namespace = 7;
    string starting_user_id = 8;
    google.protobuf.Timestamp started_processing_min = 9;
    google.protobuf.Timestamp started_processing_max = 10;
  }
  Filters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message ListAutomationRunsResponse {
  repeated Activity automation_runs = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message GetMultiAutomationsRequest {
    string partner_id = 1 [deprecated=true];  // This does nothing, you don't need to specify it anymore
    string market_id = 2 [deprecated=true];   // This does nothing, you don't need to specify it anymore
    repeated string automation_ids = 3;
}

message GetMultiAutomationsResponse {
    repeated Automation automations = 1;
}

message GetAutomationTagsRequest {
  string partner_id = 1 [deprecated=true]; // Use namespace instead
  repeated string market_ids = 2 [deprecated=true]; // New automations do not have markets
  AutomationType automation_type = 3;
  string namespace = 4;
}

message GetAutomationTagsResponse {
  repeated string tags = 1;
}

message ListActivitiesRequest {
    message Filters {
        string automation_id = 1;
        // Deprecated: use main_run_id instead
        string run_id = 2;
        string entity_id = 3;
        string workflow_step_id = 4;
        repeated Stage stage = 5;
        string partner_id = 6 [deprecated= true]; //Deprecated: use namespace instead
        repeated string market_ids = 7;
        // will return only the latest activity per automation
        bool latest_per_automation = 8;
        // Limit the activities to a specific type
        AutomationType automation_type = 9;
        // Only returns activities that have an error
        bool only_errors = 10;
        string main_run_id = 11;
        string namespace = 12;
    }
    Filters filters = 1;
    PagedRequestOptions paging_options = 2;
}

message ListActivitiesResponse {
    repeated Activity activities = 1;
    // paging metadata
    PagedResponseMetadata paging_metadata = 2;
}

message CopyAutomationRequest {
    string original_automation_id = 1;
    string name = 2;
}

message CopyAutomationResponse {
  string id = 1;
}

message PublishRequest {
  string id = 1;
}

message UnpublishRequest {
  string id = 1;
}

message HideRequest {
  string id = 1;
}

message UnhideRequest {
  string id = 1;
}

message SubscribeRequest {
  string automation_id = 1;
  string partner_id = 2 [deprecated=true];          // Use namespace instead
  repeated string market_ids = 3 [deprecated=true]; // New automations don't have market ids
  // Namespace is used for subscriptions to default automations. We need to know what your namespace is to subscribe you to the correct subset
  string namespace = 4;
}

message UnsubscribeRequest {
  string automation_id = 1;
}

message GetSubscriptionRequest {
  string automation_id = 1;
}

message GetSubscriptionResponse {
  AutomationSubscription subscription = 1;
}

message ListActivitiesCurrentlyWaitingRequest {
  message Filters {
    string automation_id = 1;
    string workflow_step_id = 2;
    string partner_id = 3 [deprecated= true]; //Deprecated: use namespace instead
    repeated string market_ids = 4;
    string namespace = 5;
  }
  Filters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message ListActivitiesCurrentlyWaitingResponse {
  repeated Activity activities = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message GetWorkflowStepPredecessorOutputParamsRequest {
  string automation_id = 1;
  repeated string workflow_step_parent_ids = 2;
  bool only_include_named_steps = 3;
  reserved 4;
}

message GetWorkflowStepPredecessorOutputParamsResponse {
  repeated WorkflowStepOutputParameterContainer workflow_step_output_parameters = 1;
}

message CountRunningWorkflowsRequest {
  string automation_id = 1;
}

message CountRunningWorkflowsResponse {
  int64 count = 1;
}

message StopAllAutomationRunsRequest {
  string partner_id = 1 [deprecated=true]; // Use namespace instead
  string namespace = 2;
  Context context = 3;
}

message GetAutomationAccessRequirementsRequest {
  string automation_id = 1;
}

// Success and failed lists of task definition IDs
message GetAutomationAccessRequirementsResponse {
  repeated string success_ids = 1;
  repeated string failed_ids = 2;
}

message SummarizeAutomationRequest {
  string automation_id = 1;
  repeated string prompts = 2;
}

message SummarizeAutomationResponse {
  string summary = 1;
}

message ActivateOrUpdateAutomationRequest {
  string automation_id = 1;
  string run_with_user_id = 2;
  AutomationSettings settings = 3;
  bool subscribe_to_notifications = 4;
}

message EvaluateStepDataForAutomationRequest {
  string automation_id = 1;
}

message EvaluateStepDataForAutomationResponse {
  repeated StepData step_data = 1;
}

message UpdateStepDataForAutomationRequest {
  string automation_id = 1;
  repeated StepData step_data = 2;
}


service Automata {
    rpc CreateAutomation (CreateAutomationRequest) returns (CreateAutomationResponse) {
      option (vendastatypes.access) = {
        scope: "automation"
      };
    };
    rpc GetAutomation (GetAutomationRequest) returns (GetAutomationResponse) {
        option (vendastatypes.access) = {
        scope: "automation:read"
      };
    }
    rpc GetMultiAutomations (GetMultiAutomationsRequest) returns (GetMultiAutomationsResponse);
    rpc DeleteAutomation (DeleteAutomationRequest) returns (google.protobuf.Empty);
    rpc UpdateAutomation (UpdateAutomationRequest) returns (google.protobuf.Empty);
    rpc Publish (PublishRequest) returns (google.protobuf.Empty);
    rpc Unpublish (UnpublishRequest) returns (google.protobuf.Empty);
    rpc Hide (HideRequest) returns (google.protobuf.Empty);
    rpc Unhide (UnhideRequest) returns (google.protobuf.Empty);
    rpc StartAutomationRunning (StartAutomationRunningRequest) returns (google.protobuf.Empty);
    rpc StopAutomationRunning (StopAutomationRunningRequest) returns (google.protobuf.Empty) {
      option (vendastatypes.access) = {
        scope: "automation"
      };
    };
    rpc SetAutomationToDraining (SetAutomationToDrainingRequest) returns (google.protobuf.Empty);
    rpc ListAutomations (ListAutomationsRequest) returns (ListAutomationsResponse) {
      option deprecated = true;
      option (vendastatypes.access) = {
        scope: "automation:read"
      };
    }
    // ListAutomationsV2 is used to list regular, template, or system automations
    rpc ListAutomationsV2 (ListAutomationsRequestV2) returns (ListAutomationsResponse) {
      option (vendastatypes.access) = {
        scope: "automation"
        scope: "automation:read"
      };
    }
    // ListSnippets is used to list snippets
    rpc ListSnippets (ListSnippetsRequest) returns (ListSnippetsResponse) {
      option (vendastatypes.access) = {
        scope: "automation:read"
      };
    }
    rpc ListActivities (ListActivitiesRequest) returns (ListActivitiesResponse) {
      option (vendastatypes.access) = {
        scope: "automation:read"
      };
    }
    rpc CopyAutomation (CopyAutomationRequest) returns (CopyAutomationResponse);
    rpc GetAutomationTags (GetAutomationTagsRequest) returns (GetAutomationTagsResponse);
    rpc Subscribe (SubscribeRequest) returns (google.protobuf.Empty);
    rpc Unsubscribe (UnsubscribeRequest) returns (google.protobuf.Empty);
    rpc GetSubscription (GetSubscriptionRequest) returns (GetSubscriptionResponse);
    rpc ListActivitiesCurrentlyWaiting (ListActivitiesCurrentlyWaitingRequest) returns (ListActivitiesCurrentlyWaitingResponse);
    rpc GetWorkflowStepPredecessorOutputParams (GetWorkflowStepPredecessorOutputParamsRequest) returns (GetWorkflowStepPredecessorOutputParamsResponse);
    rpc ListAutomationRuns (ListAutomationRunsRequest) returns (ListAutomationRunsResponse) {
      option (vendastatypes.access) = {
        scope: "automation:read"
      };
    }
    rpc CountRunningWorkflows (CountRunningWorkflowsRequest) returns (CountRunningWorkflowsResponse);
    rpc StopAllAutomationRuns (StopAllAutomationRunsRequest) returns (google.protobuf.Empty);
    rpc GetAutomationAccessRequirements (GetAutomationAccessRequirementsRequest) returns (GetAutomationAccessRequirementsResponse);
    rpc SummarizeAutomation (SummarizeAutomationRequest) returns (SummarizeAutomationResponse);
    // ActivateOrUpdateAutomation is used to turn on a regular automation without requiring an OAuth flow.
    // The API automatically fetches access and refresh tokens from IAM and uses them to start the automation.
    // This should be used instead of StartAutomationRunning when you want to:
    // 1. Turn on a regular automation (not a system automation)
    // 2. Update the OAuth token for an automation
    rpc ActivateOrUpdateAutomation (ActivateOrUpdateAutomationRequest) returns (google.protobuf.Empty);
    // EvaluateStepDataForAutomation takes in an automation ID and returns a list of step data
    // either containing its value or not, based off of data that is marked as user data provided in task definitions used for each automation step
    rpc EvaluateStepDataForAutomation (EvaluateStepDataForAutomationRequest) returns (EvaluateStepDataForAutomationResponse);
    // UpdateStepDataForAutomation takes in step data identifiers and the data to put in each one of the fields inside of
    // the automation steps
    rpc UpdateStepDataForAutomation (UpdateStepDataForAutomationRequest) returns (google.protobuf.Empty);
}

message CreateEventTriggerDefinitionRequest {
    EventTriggerDefinition event_trigger_definition = 1;
}

message UpdateEventTriggerDefinitionRequest {
    EventTriggerDefinition event_trigger_definition = 1;
    vendastatypes.FieldMask field_mask = 2;
}

message GetEventTriggerDefinitionRequest {
    string event_type_id = 1;
}

message GetEventTriggerDefinitionResponse {
    EventTriggerDefinition event_trigger_definition = 1;
}

message DeleteEventTriggerDefinitionRequest {
    string event_type_id = 1;
}

message CreateTaskDefinitionRequest {
    TaskDefinition task_definition = 1;
}

message UpdateTaskDefinitionRequest {
    TaskDefinition task_definition = 1;
    vendastatypes.FieldMask field_mask = 2;
}

message CreateTaskDefinitionResponse {
    string id = 1;
}

message GetTaskDefinitionRequest {
    string id = 1;
}

message GetTaskDefinitionResponse {
    TaskDefinition task_definition = 1;
}

message GetMultiTaskDefinitionRequest {
    repeated string ids = 1;
}

message GetMultiTaskDefinitionResponse {
    repeated TaskDefinition task_definitions = 1;
}

message DeleteTaskDefinitionRequest {
    string id = 1;
}

message ListTaskDefinitionsRequest {
    message ValidationData {
        string automation_id = 1;
        // This api is called when adding a new step so there isn't a "current" step id to get the parents of, need the frontend to pass them in.
        repeated string workflow_step_parent_ids = 2;
        // Reserved for deprecated field
        reserved 3;
        // Use this to validate the task definitions against a provided trigger task definition ID instead of what is saved on the automation
        string trigger_task_definition_id = 4;
    }
    message Filters {
        TaskType task_type = 1;
        bool internal = 2;
        PublishedState published_state = 3;
        bool only_available_in_default_automations = 4;
        string name = 5;
        // Filters to task definitions with at least one matching parameter filter key on its optionally linked event trigger definition
        // For example, if an event trigger definition has a parameter filter key of `account_group_id` and a task definition is linked to it
        // via it's entity_type_id and has an input parameter with the same id and the `AccountGroup` type, then specifying that type will return that
        // task definition in the results
        DataType wait_for_data_type_scope = 6;
        // market reliant should no longer be used. It no longer serves a function and will be removed in the future.
        bool market_reliant = 7 [deprecated = true];
        repeated Context supported_contexts = 8;
        // Tags to filter by, will return task definitions that have at least one matching tag
        repeated string tags = 9;
    }
    Filters filters = 1;
    PagedRequestOptions paging_options = 2;
    // If validation_date is provided the task definitions will be marked as valid or not for the data provided
    ValidationData validation_data = 3;
}

message ListTaskDefinition {
    bool valid = 1;
    TaskDefinition task_definition = 2;
  // Flag indicating whether the requesting user has access to run the task
    bool has_access_to_run = 3;
}

message ListTaskDefinitionsResponse {
    repeated ListTaskDefinition task_definitions = 1;
    // paging metadata
    PagedResponseMetadata paging_metadata = 2;
}


message ListEventTriggerDefinitionRequest {
  message Filters {
    string name = 1;
  }
  Filters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message ListEventTriggerDefinitionResponse {
  repeated EventTriggerDefinition event_trigger_definitions = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

service AutomataDevelopment {
    rpc CreateEventTriggerDefinition (CreateEventTriggerDefinitionRequest) returns (google.protobuf.Empty){
      option (vendastatypes.access) = {
        scope: "eventtriggerdefinition"
      };
    }
    rpc UpdateEventTriggerDefinition (UpdateEventTriggerDefinitionRequest) returns (google.protobuf.Empty){
      option (vendastatypes.access) = {
        scope: "eventtriggerdefinition"
      };
    }
    rpc GetEventTriggerDefinition (GetEventTriggerDefinitionRequest) returns (GetEventTriggerDefinitionResponse){
      option (vendastatypes.access) = {
        scope: "eventtriggerdefinition"
        scope: "eventtriggerdefinition:read"
      };
    }
    rpc DeleteEventTriggerDefinition (DeleteEventTriggerDefinitionRequest) returns (google.protobuf.Empty){
      option (vendastatypes.access) = {
        scope: "eventtriggerdefinition"
      };
    }
    rpc ListEventTriggerDefinition (ListEventTriggerDefinitionRequest) returns (ListEventTriggerDefinitionResponse){
      option (vendastatypes.access) = {
        scope: "eventtriggerdefinition"
        scope: "eventtriggerdefinition:read"
      };
    }
    rpc CreateTaskDefinition (CreateTaskDefinitionRequest) returns (CreateTaskDefinitionResponse){
      option (vendastatypes.access) = {
        scope: "taskdefinition"
      };
    }
    rpc UpdateTaskDefinition (UpdateTaskDefinitionRequest) returns (google.protobuf.Empty){
      option (vendastatypes.access) = {
        scope: "taskdefinition"
      };
    }
    rpc GetTaskDefinition (GetTaskDefinitionRequest) returns (GetTaskDefinitionResponse){
      option (vendastatypes.access) = {
        scope: "taskdefinition:read"
        scope: "taskdefinition"
      };
    }
    rpc GetMultiTaskDefinition (GetMultiTaskDefinitionRequest) returns (GetMultiTaskDefinitionResponse){
      option (vendastatypes.access) = {
        scope: "taskdefinition:read"
        scope: "taskdefinition"
      };
    }
    rpc DeleteTaskDefinition (DeleteTaskDefinitionRequest) returns (google.protobuf.Empty){
      option (vendastatypes.access) = {
        scope: "taskdefinition"
      };
    }
    rpc ListTaskDefinitions (ListTaskDefinitionsRequest) returns (ListTaskDefinitionsResponse){
        option (vendastatypes.access) = {
          scope: "taskdefinition:read"
          scope: "taskdefinition"
      };
    }
}


message StartAccountGroupAutomationRequest {
  string automation_id = 1;
  repeated string account_group_ids = 2;
  reserved 3, 4;
  string action_list_name = 5;
  // If the automation is a default automation, this is the namespace that the automation will be run as. If the automation is just a regular automation this field will
  // be superseded by the value of the namespace on that automation
  string namespace = 6;
}

message OrderKey {
  string account_group_id = 1;
  string order_id = 2;
}

message StartAutomationRequest {
  // The automation to start
  string automation_id = 1;
  // The account groups to start this automation for (optional)
  // deprecated: use payloads instead
  repeated string account_group_ids = 2 [deprecated = true];
  // The orders to start this automation for (optional)
  // deprecated: use payloads instead, we only need the order ID, we don't need the account group id as well.
  repeated OrderKey order_keys = 3 [deprecated = true];

  message StartAutomationPayload {
    // The entity id to start the automation for
    string entity_id = 1;
    // The extra data to start the automation with
    google.protobuf.Struct data = 2;
  }
  repeated StartAutomationPayload payloads = 4;

  bool check_access_only = 5;

  // If the automation is a default automation, this is the namespace that the automation will be run as. If the automation is just a regular automation this field will
  // be superseded by the value of the namespace on that automation
  string namespace = 6;
}

message ContinueAutomationFromStepRequest {
  // The automation to continue
  string automation_id = 1;
  // The entity id to continue the automation for
  string entity_id = 2;
  // The workflow step id of the activity that was last ran (eg. and failed)
  string previous_workflow_step_id = 3;
  // The run id that the previous automation ran with
  string previous_run_id = 4;
  // The workflow step id with which to continue the automation
  string workflow_step_id_to_continue = 5;
}

message SearchManualAutomationsRequest {
  message Filters {
    string partner_id = 1 [deprecated= true]; //Deprecated: use namespace instead
    bool only_sales_runnable = 2;
    repeated string market_ids = 3;
    string namespace = 4;
    repeated EntityType entity_types = 5;
    Context context = 6;
  }
  Filters filters = 1;
  string search_term = 2;
  PagedRequestOptions paging_options = 3;
  EntityType entity_type = 4 [deprecated= true]; //Deprecated: use the entity type from the filters instead
}

message SearchManualAutomationsResponse {
  repeated Automation automations = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message RateLimitCheckRequest {
  // The rate limit to check against (will be created if it doesn't exist)
  RateLimit rate_limit = 1;
  // The time at which to check for so that it can be idempotent
  google.protobuf.Timestamp timestamp = 2;

}

message SendTestWebhookRequest {
  string partner_id = 1 [deprecated= true]; //Deprecated: use namespace instead
  string market_id = 2;
  string automation_id = 3;
  string url = 4;
  string verifier_token = 5;
  map<string, string> additional_fields = 6;
  string namespace = 7;
}

message EndAutomationRunRequest {
  // The automation to start
  string automation_id = 1;
  // Deprecated: use main_run_id and step_execution_uuid instead
  string run_id = 2;
  // Optionally, the entity id of the activity through which the request was made
  string entity_id = 3;
  // Optionally, the workflow step id of the activity through which the request was made
  string workflow_step_id = 4;
  // The main run id of the run to end
  string main_run_id = 5;
  string step_execution_uuid = 6;
}

// EmitAutomationEventRequest is the request object for EmitAutomationEvent
message EmitAutomationEventRequest {
  // the identifier of the event to be emitted
  string event_id = 1;
  // the data to be emitted
  google.protobuf.Struct event_data = 2;
}

service AutomataOperations {
  rpc StartAccountGroupAutomation (StartAccountGroupAutomationRequest) returns (google.protobuf.Empty);
  rpc SearchManualAutomations (SearchManualAutomationsRequest) returns (SearchManualAutomationsResponse);
  // RateLimitCheck will return nothing if it succeeded and a FailedPrecondition error if it failed.
  rpc RateLimitCheck (RateLimitCheckRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "automation"
    };
  }
  // Starts an automation regardless of entity type of that automation
  rpc StartAutomation (StartAutomationRequest) returns  (google.protobuf.Empty);
  // Post to a given endpoint with a verifier token
  rpc SendTestWebhook (SendTestWebhookRequest) returns (google.protobuf.Empty);
  // Ends a specific run of an automation
  rpc EndAutomationRun (EndAutomationRunRequest) returns (google.protobuf.Empty);
  // Starts a migration that is pre configured
  rpc StartMigration (google.protobuf.Empty) returns (google.protobuf.Empty);
  // Continues an automation at a specific step given the previous automation event data
  rpc ContinueAutomationFromStep (ContinueAutomationFromStepRequest) returns (google.protobuf.Empty);
  // Emits an event that will be processed as a potential trigger for automations
  rpc EmitAutomationEvent (EmitAutomationEventRequest) returns (google.protobuf.Empty);
}

// Deprecated: Use GetAutomationConfigurationV2
message GetConfigurationRequest {
  string partner_id = 1;
  string automation_id = 2;
}

message GetConfigurationResponse {
  AutomationConfiguration configuration = 1;
}

// Deprecated: there is no alternative at this time
message DeleteConfigurationRequest {
  string partner_id = 1;
  string automation_id = 2;
}

// Deprecated: use DisableAutomationConfigurationV2 instead
message DisableForMarketsRequest {
  string partner_id = 1;
  string automation_id = 2;
  repeated string market_ids = 3;
}

// Deprecated: use EnableAutomationConfigurationV2 instead
message EnableForMarketsRequest {
  string partner_id = 1;
  string automation_id = 2;
  repeated string market_ids = 3;
}

enum DefaultAutomationStatus {
  LIST_DEFAULT_AUTOMATIONS_FOR_PARTNER_REQUEST_DEFAULT_AUTOMATION_STATUS_ON = 0;
  LIST_DEFAULT_AUTOMATIONS_FOR_PARTNER_REQUEST_DEFAULT_AUTOMATION_STATUS_OFF = 1;
}

// Deprecated: use ListDefaultAutomations instead
message ListDefaultAutomationsForPartnerRequest {
  message Filters {
    string partner_id = 1;
    repeated string market_ids = 2;
    repeated DefaultAutomationStatus statuses = 3;
    repeated string tags = 4;
  }
  Filters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message DefaultAutomationWithConfiguration {
  Automation default_automation = 1;
  AutomationConfiguration configuration = 2;
}

message ListDefaultAutomationsForPartnerResponse {
  repeated DefaultAutomationWithConfiguration results = 1;
  PagedResponseMetadata paging_metadata = 2;
}

message ListDefaultAutomationsRequest {
  message Filters {
    Context context = 1;
    string partner_id = 2 [deprecated=true]; // Use namespace instead
    repeated DefaultAutomationStatus statuses = 3;
    repeated string tags = 4;
    string namespace = 5;
  }
  Filters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message DefaultAutomation {
  Automation default_automation = 1;
  AutomationConfigurationV2 configuration = 2;
}

message ListDefaultAutomationsResponse {
  repeated DefaultAutomation results = 1;
  PagedResponseMetadata paging_metadata = 2;
}

message EnableAutomationConfigurationRequest {
  AutomationConfigurationV2Identifier automation_configuration_id = 1;
}

message DisableAutomationConfigurationRequest {
  AutomationConfigurationV2Identifier automation_configuration_id = 1;
}

message GetAutomationConfigurationV2Request {
  AutomationConfigurationV2Identifier automation_configuration_id = 1;
}

message GetAutomationConfigurationV2Response {
  AutomationConfigurationV2 configuration = 1;
}

service AutomataDefaultAutomations {
  // Deprecated: Use GetAutomationConfigurationV2 instead
  rpc GetConfiguration (GetConfigurationRequest) returns (GetConfigurationResponse) {
    option (vendastatypes.access) = {
      scope: "automation:read"
    };
  }
  // Deprecated: there is no alternative at this time
  rpc DeleteConfiguration (DeleteConfigurationRequest) returns (google.protobuf.Empty);
  // Deprecated: use DisableAutomationConfigurationV2 instead
  rpc DisableForMarkets (DisableForMarketsRequest) returns (google.protobuf.Empty);
  // Deprecated: use EnableAutomationConfigurationV2 instead
  rpc EnableForMarkets (EnableForMarketsRequest) returns (google.protobuf.Empty);
  // Deprecated: use ListDefaultAutomations instead
  rpc ListDefaultAutomationsForPartner (ListDefaultAutomationsForPartnerRequest) returns (ListDefaultAutomationsForPartnerResponse) {
    option (vendastatypes.access) = {
      scope: "automation:read"
    };
    option deprecated = true;
  }
  rpc ListDefaultAutomations (ListDefaultAutomationsRequest) returns (ListDefaultAutomationsResponse) {
    option (vendastatypes.access) = {
      scope: "automation:read"
    };
  }

  rpc EnableAutomationConfigurationV2(EnableAutomationConfigurationRequest) returns (google.protobuf.Empty);
  rpc DisableAutomationConfigurationV2(DisableAutomationConfigurationRequest) returns (google.protobuf.Empty);
  rpc GetAutomationConfigurationV2(GetAutomationConfigurationV2Request) returns (GetAutomationConfigurationV2Response) {
    option (vendastatypes.access) = {
      scope: "automation:read"
    };
  }
}
