syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "ViewConfigurationsProto";
option java_package = "com.vendasta.crm.v1.generated";

import "vendasta_types/field_mask.proto";

message ViewConfiguration {
  string namespace = 1;
  string view_configuration_id = 2;

  string name = 3;
  bool enabled = 4;
  string identifier = 5;
  View view = 6;
}

message View {
  string view_id = 1;

  string component_id = 2;
  string filters = 3;
}

message CreateViewConfigurationRequest {
  string namespace = 1;
  ViewConfiguration view_configuration = 2;
}

message CreateViewConfigurationResponse {
  string view_configuration_id = 1;
}

message GetMultiViewConfigurationRequest {
  string namespace = 1;
  repeated string view_configuration_ids = 2;
}

message GetMultiViewConfigurationResponse {
  repeated ViewConfiguration view_configurations = 1;
}

message DeleteViewConfigurationRequest {
  string namespace = 1;
  string view_configuration_id = 2;
}

message ListViewConfigurationOptions {
  string namespace = 1;
  string identifier = 2;
  repeated string component_ids = 3;
}

message ListViewConfigurationsRequest {
  ListViewConfigurationOptions list_options = 1;
}

message ListViewConfigurationsResponse {
  repeated ViewConfiguration view_configurations = 1;
}

message UpdateViewConfigurationRequest {
  string namespace = 1;
  ViewConfiguration view_configuration = 2;
  vendastatypes.FieldMask field_mask = 3;
}
