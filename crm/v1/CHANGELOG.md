# Changelog
## 0.117.0
- Added `BulkDeleteContactsRequest`, `BulkDeleteCompaniesRequest`, `BulkDeleteCustomObjectsRequest`, and `BulkDeleteActivitiesRequest`

## 0.116.0
- Add `ViewConfigurations` service
- Add operations
  - CreateViewConfiguration
  - UpdateViewConfiguration
  - GetMultiViewConfiguration
  - ListViewConfigurations
  - DeleteViewConfiguration

## 0.115.0
- Remove `ImportFromFile` and `ImportFromFileRequest` from `CRMImporterService`

## 0.114.0
- Added descriptions for rpcs and msgs

## 0.113.0
- Add options for "contact" MCP server

## 0.112.0
- Add `GetMultiImportRecord` to `CRMImporterService`

## 0.111.0
- Add `MultiObjectTypeImportFromFileResponse` with `import_id` for `MultiObjectTypeImportFromFile`

## 0.110.0
- Add `object_subtype` to `SearchPITCrmObjectsRequest`

## 0.109.0
- Add `original_csv_url` and `error_csv_url` to `ImportRecord`

## 0.108.0
- Add list of field IDs to `ValidationErrorDetails`

## 0.107.0
- Add `FieldSchema` to `GetMultiActivityResponse`

## 0.106.0
- Add `SkipAutomation` to `FieldSchema`

## 0.105.0
- Add `ListImportRecords` to `CRMImporterService`

## 0.104.0
- Add `DeleteFieldSchemaCustomization` rpc to `CRMFieldSchemaCustomizationService`

## 0.103.0
- Mark `multi_location_path` as reserved in multi location RPCs

## 0.102.0
- Add `crm_object_type` and `crm_object_subtype` to `FieldSchema` for responses

## 0.101.0
- Add `CRMFieldSchemaCustomizationService`

## 0.100.0
- Deprecate `multi_location_path` in multi location RPCs

## 0.99.0
- Add `FindAssociations` to `CRMAssociationService`

## 0.98.0
- Add `PROJECTION_FILTER_TYPE_INCLUDE_DELETED` for GetMultiCrmObjectRequest

## 0.97.0
- Add scopes for `CustomObject` endpoints

## 0.96.0
- Add `namespace` on `CrmObject` for multilocation responses

## 0.95.0
- Add `CRMMultiLocationContactService`, `CRMMultiLocationFieldSchemaService`, and `CRMMultiLocationFieldLayoutService`
- Add operations
  - ListMultiLocationContacts
  - ListMultiLocationContactFieldOptions
  - ListMultiLocationContactFilters
  - ListMultiLocationFieldSchema
  - ListMultiLocationFieldGroups

## 0.94.0
- Add `crm_object_subtype` to `CrmObject` and `ListCrmObjectsRequest`
- Replace `ListCustomObjects` response/request with `ListCrmObjects` response/request
- Replace all specific custom object messages with crm object messages

## 0.93.0
- Add `namespace` on `Activity` for multilocation responses

## 0.92.0
- Add `from_crm_object_subtype` and `to_crm_object_subtype` to `Association`
- Add `crm_object_subtype` to `ActivityAssociation`

## 0.91.0
- Add `ValidateOpportunity` to `CRMOpportunityService`

## 0.90.0
- Add RPCs to `CRMCustomObjectService`
  - ListCustomObjectFilters
  - ListCustomObjectFieldOptions

## 0.89.2
- Add `filter_groups` to `SearchPITCrmObjectsRequest`

## 0.89.1
- Remove `crm_object_id` from `ListCustomObjectsRequest`

## 0.89.0
- Add `CRMCustomObjectService` service
- Add operations
  - CreateCustomObject
  - UpdateCustomObject
  - GetMultiCustomObject
  - ListCustomObjects
  - DeleteCustomObject
  - ValidateCustomObject

## 0.88.0
- Add `CRMCustomObjectTypeService` service
- Add operations
  - CreateCustomObjectType
  - UpdateCustomObjectType
  - GetMultiCustomObjectType
  - ListCustomObjectTypes
  - DeleteCustomObjectType

## 0.87.0
- Change `group_id` to `multi_location_path`

## 0.86.0
- Add `group_id` to `ListMultiLocationActivitiesRequest`, `ListMultiLocationFieldOptionsRequest`, `ListMultiLocationFiltersRequest`

## 0.85.0
- Add `ListMultiLocationActivities`, `ListMultiLocationActivityFieldOptions`, `ListMultiLocationActivityFilters` to `CRMMultiLocationActivityService`

## 0.84.0
- Add `GetOpportunityValueSummary` RPC


## 0.83.0
- Add `idempotency_id` property to `CrmObject`

## 0.82.0
- Add scopes for `Pipelines` endpoints

## 0.81.0
- Add `crm.association` and `crm.association:read` scopes to CRMAssociationService RPCs

## 0.80.0
- Add `CrmExportObjectsRequest` message

## 0.79.0
- Add `Pipelines` service
- Add operations
  - CreatePipeline
  - UpdatePipeline
  - GetMultiPipeline
  - ListPipelines
  - DeletePipeline

## 0.78.0
- Add `ObjectProjectionFilter` to `GetMultiCrmObjectRequest` and `ListCrmObjectRequest`

## 0.77.0
- Add `currency_value` to `FieldValue`

## 0.76.0
- Add `phone_field_values` to `FieldValue`

## 0.75.0
- Change `HeaderMapping` with `CSVHeaderMapping` in importer RPCs
- Add `crm_object_type` and `crm_object_subtype` for `CSVHeaderMapping` in importer RPCs

## 0.74.0
- Add `CRMOpportunityService`
- Add operations
  - CreateOpportunity 
  - UpdateOpportunity
  - GetMultiOpportunity
  - ListOpportunities
  - ListOpportunitiesFilters
  - DeleteOpportunity
  - ListOpportunityFieldOptions

## 0.73.0
- Add `MultiObjectTypeImportFromFile` to `CRMImporterService`

## 0.72.1
- Add `crm_object_id` to `extra_fields`

## 0.72.0
- Add `extra_field_id` to `SearchPITCrmObjectsRequest`
- Add `extra_fields` to `SearchPITCrmObjectsResponse`
- Add `tiebreaker` to `CrmSearchPITParams`

## 0.71.0
- Remove maps from `GetActivityMetricsResponse`
- Change `GetActivityMetricsRequest` to use galaxy filters
- Rename `Effort` to `Metric`

## 0.70.0
- Add `crm` scope to `ListCompaniesFilters` rpc

## 0.69.0
- Change `user_effort_counts` key to string

## 0.68.0
- Add `crm` scope to `ValidateCompany`, `ListCompanyFieldOptions` rpcs

## 0.67.0
- Add `GetActivityMetrics` RPC

## 0.66.0
- Add `SetScoringDefinitionsForFieldIDRequest` RPC

## 0.65.0
- Deprecate `label` for `field_id` in scoring RPCs

## 0.64.0
- Add `namespace` filter to `ListFieldSchemaRequest`

## 0.63.0
- Add `check_access_only` to `SearchPITCrmObjectsRequest

## 0.62.0
- Remove old Filters.

## 0.61.0
- Switch to use Galaxy Filters, and deprecate the old filters
- Switch Scoring Rules

## 0.60.0
- Add `ListChangeLogsRequest` and `ListChangeLogsResponse` to `crm_objects_change_log.proto`'

## 0.59.0

- Add `crm` scope to `CRMAssociationService` rpcs

## 0.58.0

- Add `crm` scope to apis

## 0.57.0

- Add `StartBulkCompanyCreate` rpc

## 0.56.0

- Add `GetScore` rpc

## 0.55.0

- Deprecate Filters in SearchPITCrmObjectsRequest
- Add `filters_v2` to SearchPITCrmObjectsRequest

## 0.54.0

- Add `ListScoringDefinitions` rpc

## 0.53.0

- Add GetMultiScoringDefinition rpc
- Add DeleteScoringDefinition rpc

## 0.52.0

- Add Scoring service
- Add CreateScoringDefinition rpc

## 0.51.3
- add scopes to `GetMultiFieldSchema`

## 0.51.2
- add scopes to `Create/UpdateCompany/Contact` 

## 0.51.1
- add scopes to `ListContacts` and `ListCompanies`

## 0.51.0
- Add `float_value` to `FieldValue` message and `FIELD_TYPE_FLOAT` to `FieldType`

## 0.50.0
- Add `field_schemas` to `GetMultiCrmObjectResponse`

## 0.49.0
- Add `auto_populate_primary_company` to `CreateActivityRequest`

## 0.48.0
- Add `SearchPITCrmObjectsRequest` to `crm_search_pit.proto`
- Add `SearchPITCrmObjectsResponse` to `crm_search_pit.proto`
- Add `CrmSearchPITService`

## 0.47.0
- Add `GetMultiCrmObjectChangeLogRequest` to `crm_objects_change_log.proto`
- Add `GetMultiCrmObjectChangeLogResponse` to `crm_objects_change_log.proto`
- Add `CRMChangeLogService`

## 0.46.0
- Add scopes to the contact/company GetMulti RPCs

## 0.45.0
- Add scopes to the Activity RPCs

## 0.44.0
- Add `sort_by` to `ListCrmObjectsRequest`
- Add `sort_by` to `ListActivitiesRequest`

## 0.43.0
- Add `ListAssociationFieldOptions` rpc

## 0.42.0
- Add `FIELD_TYPE_GEOPOINT` enum to `FieldType`
- 
## 0.41.0 
- Add `FIELD_TYPE_STRING_LIST` to `FieldType`

## 0.40.0
- Add `group_id` property to `CrmObject`

## 0.39.0
- Add `readonly` property to `FieldSchema`

## 0.38.1
- Return multiple `existing_crm_object_ids` on `uniquenessCheck`
- Change `ValidationType` to `ValidationErrorSeverity` 
- Create `UniquenessType` for `UniquenessCheck` error details

## 0.38.0
- Add `details` payload to `ValidationError` to expose `uniquenessCheck` validation details
- Add `ValidationType` to validation to differentiate between enforced/warning scenarios

## 0.37.0
- Add `CRMAssociationService`

## 0.36.0
- Add `CRMCompanyService`

## 0.35.0
- Add `DuplicateHandlingType` to the `ImportFromFileRequest` message

## 0.34.0
- Add to `FIELD_TYPE_DATE_TIME` to `FieldType`

## 0.33.0
- Add `CRMActivityService`
- Add `Association` and `AssociationChange`

## 0.32.0
- Add `mappingSuggestions` to `CSVFile` message

## 0.31.0
- Add to `FILTER_TYPE_TAG` to `FilterType`

## 0.30.0
- Add `search_term` to `ListFiltersRequest` and `ListFieldSchemaRequest`
- Add `field_name` to `ListFiltersResponse`

## 0.0.29
- Add `NamespaceAdminService`

## 0.0.28
- Add `CSVSize` to `CSVFile`

### 0.0.27
- Add `created`, `updated`, `deleted` to field extension
- Return `extension_id` on `CreateFieldExtension`

### 0.0.26
- Refactor name from `CRMFieldExtensionService` to `CRMFieldExtensionAdminService`
- Refactor name from `CRMFieldExtensionClient` to `CRMFieldExtensionService`

### 0.0.25
- Add `CRMFieldExtensionService`
- Add `CRMFieldExtensionClient`

### 0.0.24
- Add `CreateCrmObjectResponse` with `crm_object_id` field
- Add `CreateFieldSchemaResponse` with `field_id` field

### 0.0.23
- Add `Created`, `Updated`, and `Deleted` to `CrmObject`

### 0.0.22
- Add `ListContactFieldOptions` rpc in the `CRMService` to list all available contact field options.
- Rename tag_values to string_values in `FieldValue` message.

### 0.0.21
- Add Tag field type
- Add tag_values for field values of CRM object

### 0.0.20
- Add `CrmObjectSearch` to `ListCrmObjectsRequest`

### 0.0.19
- Add CRMFieldLayoutService with operations:
  - ListFieldGroups

### 0.0.18
- Add operation:
  - ValidateContact

### 0.0.17
- Remove unused `invalid` value for field data
- Add `null_value` to indicate when to remove a field / field doesn't exist

### 0.0.16
- Add Boolean field type
- Add boolean_value for field values of CRM object

### 0.0.15
- Remove FieldSchema fields from contact service responses for List, GetMulti
### 0.0.14
- Fix field_mask for GetMulti
### 0.0.13
- Rename Object to CrmObject because Object is reserved in TypeScript

### 0.0.12
- Add support for Phone field type

### 0.0.11
- Add operation:
  - ListContacts
  
### 0.0.10
- Add support for Email field type

### 0.0.9
- Use filename consistently in the importer API.

### 0.0.8
- Re-add namespace to FieldSchema

### 0.0.7
- Add external ID support for fields

### 0.0.6
- Add operation:
  - UpdateContact
### 0.0.5
- Add CRMImporterService

### 0.0.4
- Add an API to delete a contact.

### 0.0.3
- Add field schema mask to get multi proto to test if performance is just dependent on size of data

### 0.0.2

- Add operations:
  - CreateContact, GetContact
  - CreateContactFieldSchema, GetContactFieldSchema, GetMultiContactFieldSchema, ListContactFieldSchema, UpdateContactFieldSchema, ArchiveContactFieldSchema, UnarchiveContactFieldSchema

### 0.0.1

- Create empty proto for CRM
