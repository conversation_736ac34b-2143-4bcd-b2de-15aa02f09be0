syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "ActivitiesProto";
option java_package = "com.vendasta.crm.v1.generated";

import "google/protobuf/timestamp.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/paging.proto";
import "galaxytypes/v1/galaxy_filters.proto";
import "crm/v1/crm_objects.proto";
import "crm/v1/fields.proto";

message CreateActivityRequest {
  string namespace = 1;
  Activity activity = 2;
  bool auto_populate_primary_company = 3;
}

message CreateActivityResponse {
  string crm_object_id = 1;
}

message UpdateActivityRequest {
  string namespace = 1;
  Activity activity = 2;
  // a list of field identifiers that need to be updated
  vendastatypes.FieldMask field_mask = 4;
}

message GetMultiActivityRequest {
  string namespace = 1;
  // crm_object_ids of activities, not the associated object
  repeated string crm_object_ids = 2;
  vendastatypes.FieldMask field_mask = 3;
}

message GetMultiActivityResponse {
  repeated Activity activities = 1;
  repeated FieldSchema field_schemas = 2;
}

message DeleteActivityRequest {
  string namespace = 1;
  string crm_object_id = 2;
}

message ActivityAssociation {
  string crm_object_id = 1;
  string crm_object_type = 2;
  repeated string tags = 3;
  // only used by custom objects at the moment
  string crm_object_subtype = 4;
}

message Activity {
  string crm_object_id = 1;
  string crm_object_subtype = 2;
  string external_id = 3;
  string owner_id = 4;
  repeated FieldValue fields = 5;
  repeated ActivityAssociation associations = 6;
  google.protobuf.Timestamp created = 7;
  google.protobuf.Timestamp updated = 8;
  google.protobuf.Timestamp deleted = 9;
  string namespace = 10; // This is going to be used for multi response, ignore it on requests
}

message ListActivitiesRequest {
  string namespace = 1;
  // the CRM object to list activities for
  string crm_object_id = 2;
  string crm_object_type = 3;
  vendastatypes.PagedRequestOptions paging_options = 4;
  CrmObjectSearch search = 5;
  reserved 6;
  repeated SortBy sort_by = 7;
  galaxytypes.v1.FilterGroup filters_v2 = 8;
}

message ListActivitiesResponse {
  repeated Activity activities = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message ListMultiLocationActivitiesRequest {
  repeated string namespaces = 1;
  reserved 2;
  // the CRM object to list multi location activities for
  string crm_object_id = 3;
  string crm_object_type = 4;
  vendastatypes.PagedRequestOptions paging_options = 5;
  CrmObjectSearch search = 6;
  repeated SortBy sort_by = 7;
  galaxytypes.v1.FilterGroup filters_v2 = 8;
}

message ListMultiLocationActivitiesResponse {
  repeated Activity activities = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message ValidateActivityRequest {
  string namespace = 1;
  Activity activity = 2;
}

message ValidateActivityResponse {
  message ValidationError {
    string rule_id = 1;
    string message = 2; // this can be a translation key
  }
  repeated ValidationError errors = 1;
}

enum MetricType {
  METRIC_TYPE_INVALID = 0;
  METRIC_TYPE_OUTBOUND_CALLS = 1;
  METRIC_TYPE_INBOUND_CALLS = 2;
  METRIC_TYPE_EMAILS_SENT = 3;
  METRIC_TYPE_EMAILS_RECEIVED = 4;
  METRIC_TYPE_MEETINGS = 5;
}

message GetActivityMetricsRequest {
  string namespace = 1;
  repeated MetricType metric_types = 2;
  galaxytypes.v1.FilterGroup filters = 3;
}

message GetActivityMetricsResponse {
  repeated OwnerMetricCounts owner_metric_counts = 1;
  repeated MetricSummary summaries = 2;
}

message OwnerMetricCounts {
  string owner_id = 1;
  repeated MetricCount metric_counts = 2;
}

message MetricCount {
  MetricType metric_type = 1;
  int64 count = 2;
}

message MetricSummary {
  MetricType metric_type = 1;
  int64 total = 2;
  float average = 3;
}

message BulkDeleteActivitiesRequest {
  string namespace = 1;
  CrmObjectSearch search = 2;
  galaxytypes.v1.FilterGroup filters = 3;
}

