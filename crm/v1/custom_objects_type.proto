syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "CustomObjectTypeProto";
option java_package = "com.vendasta.crm.v1.generated";

import "vendasta_types/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "crm/v1/crm_objects.proto";
import "galaxytypes/v1/galaxy_filters.proto";

message CustomObjectType {
  string namespace = 1;
  string custom_object_type_id = 2;

  string singular_object_name = 3;
  string plural_object_name = 4;

  google.protobuf.Timestamp created = 5;
  google.protobuf.Timestamp updated = 6;
  google.protobuf.Timestamp deleted = 7;
}

message CreateCustomObjectTypeRequest {
  string namespace = 1;
  CustomObjectType custom_object_type = 2;
}

message CreateCustomObjectTypeResponse {
  string custom_object_type_id = 1;
}

message GetMultiCustomObjectTypeRequest {
  string namespace = 1;
  repeated string custom_object_type_ids = 2;
}

message GetMultiCustomObjectTypeResponse {
  repeated CustomObjectType custom_object_types = 1;
}

message UpdateCustomObjectTypeRequest {
  string namespace = 1;
  CustomObjectType custom_object_type = 2;
  vendastatypes.FieldMask field_mask = 3;
}

message DeleteCustomObjectTypeRequest {
  string namespace = 1;
  string custom_object_type_id = 2;
}

message ListCustomObjectTypesRequest {
  string namespace = 1;
}

message ListCustomObjectTypesResponse {
  repeated CustomObjectType custom_object_types = 1;
}

message BulkDeleteCustomObjectsRequest {
  string namespace = 1;
  string custom_object_type_id = 2;
  CrmObjectSearch search = 3;
  galaxytypes.v1.FilterGroup filters = 4;
}
