syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "FieldsProto";
option java_package = "com.vendasta.crm.v1.generated";

import "google/protobuf/timestamp.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/paging.proto";

// Supported crm_object_type
// - CONTACT
// - ACTIVITY

// Supported crm_object_subtype
// ...add more as needed

message CreateFieldSchemaRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 4;
  FieldSchema field_schema = 3;
}

message CreateFieldSchemaResponse {
  string field_id = 1;
}

message GetMultiFieldSchemaRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 4;
  repeated string field_id = 3;
}

message GetMultiFieldSchemaResponse {
  repeated FieldSchema field_schemas = 1;
}

message ListFieldSchemaRequest {
  message Filters {
    bool only_archived = 1;
    // The namespace may be used to filter the results for specific namespaces that are included automatically or through field extensions.
    string namespace = 2;
  }

  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 6;
  vendastatypes.PagedRequestOptions paging_options = 3;
  Filters filters = 4;
  string search_term = 5;
}

message ListFieldSchemaResponse {
  repeated FieldSchema field_schemas = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message ListMultiLocationFieldSchemaRequest {
  message Filters {
    bool only_archived = 1;
    string namespace = 2;
  }

  repeated string namespaces = 1;
  reserved 2;
  string crm_object_type = 3;
  string crm_object_subtype = 4;
  vendastatypes.PagedRequestOptions paging_options = 5;
  Filters filters = 6;
  string search_term = 7;
}

message ListMultiLocationFieldSchemaResponse {
  repeated FieldSchema field_schemas = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message UpdateFieldSchemaRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 5;
  FieldSchema field_schema = 3;
  vendastatypes.FieldMask field_mask = 4;
}

message ArchiveFieldSchemaRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 4;
  string field_id = 3;
}

message UnarchiveFieldSchemaRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 4;
  string field_id = 3;
}

message FieldSchema {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // Identifier of the field
  string field_id = 2;
  // Alternative identifier for the field, not required
  string external_id = 3;
  // Type of the field
  FieldType field_type = 4;
  // Name of the field
  string field_name = 5;
  // Description of the field
  string field_description = 6 ;
  // Options of values for the field
  repeated DropdownOption dropdown_options = 7;
  // ISO 4217 currency code
  string currency_code = 8;
  // If this schema was archived, this will have a value
  google.protobuf.Timestamp archived = 9;
  // Timestamp of the creation of the schema
  google.protobuf.Timestamp created = 10;
  // Timestamp of the last update of the schema
  google.protobuf.Timestamp updated = 11;
  // Flag to signal if this field cannot be updated by the user or not
  bool readonly = 12;
  // Type of the object that contains the field, only used for responses, ignoring it on requests
  string crm_object_type = 13;
  // Subtype of the object that contains the field, only used for responses, ignoring it on requests
  string crm_object_subtype = 14;
  // Flag to signal if automations should not handle changes on objects when this field is changed
  bool skip_automation = 15;
}

enum FieldType {
  FIELD_TYPE_INVALID = 0;
  FIELD_TYPE_STRING = 1;
  FIELD_TYPE_INTEGER = 2;
  FIELD_TYPE_DATE = 3;
  // FIELD_TYPE_DROPDOWN is passed as a string in the field_value
  FIELD_TYPE_DROPDOWN = 4;
  // FIELD_TYPE_CURRENCY is passed as a Currency message containing a code and value
  FIELD_TYPE_CURRENCY = 5;
  // FIELD_TYPE_EMAIL is passed as a string in the field_value
  FIELD_TYPE_EMAIL = 6;
  // FIELD_TYPE_PHONE is passed as a string in the field_value
  FIELD_TYPE_PHONE = 7;
  FIELD_TYPE_BOOLEAN = 8;
  // FIELD_TYPE_TAG is passed as a StringList in the field_value
  FIELD_TYPE_TAG = 9;
  FIELD_TYPE_DATETIME = 10;
  FIELD_TYPE_STRING_LIST = 11;
  // FIELD_TYPE_GEOPOINT is passed as a Geopoint message containing lat and long fields in the field_value
  FIELD_TYPE_GEOPOINT = 12;
  FIELD_TYPE_FLOAT = 13;
}

message DropdownOption {
  // Human readable value of the option
  string label = 1;
  // Value of the option
  string value = 2;
}
