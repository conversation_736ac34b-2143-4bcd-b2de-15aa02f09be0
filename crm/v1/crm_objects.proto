syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "CrmObjectsProto";
option java_package = "com.vendasta.crm.v1.generated";

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/paging.proto";
import "galaxytypes/v1/galaxy_filters.proto";
import "crm/v1/fields.proto";

message CreateCrmObjectRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // The CRM object to be created
  CrmObject crm_object = 2;
}

message CreateCrmObjectResponse {
  // The identifier of the object created
  string crm_object_id = 1;
}

message UpdateCrmObjectRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // The CRM object to be updated
  CrmObject crm_object = 2;
  // a list of identifiers of the fields that need to be updated, if empty all fields are updated
  vendastatypes.FieldMask field_mask = 3;
}

message GetMultiCrmObjectRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // A list of object identifiers to retrieve information about
  repeated string crm_object_ids = 2;
  // a list of identifiers of fields to be retrieved, if empty all fields are retrieved
  vendastatypes.FieldMask field_mask = 3;
  // extra options for retrieving objects
  repeated ObjectProjectionFilter projection_filters = 4;
}

message GetMultiCrmObjectResponse {
  // the list of objects retrieved
  repeated CrmObject crm_objects = 1;
  // the list of schemas of fields for the objects retrieved
  repeated FieldSchema field_schemas = 2;
}

message ObjectProjectionFilter {
  // Type of projection filter used
  ProjectionFilterType filter_type = 1;
  // When Filter Type is FIELD_TYPE we can convert the type from from_field_type on the response
  FieldType from_field_type = 2;
  // When Filter Type is FIELD_TYPE we can convert the type to to_field_type on the response
  FieldType to_field_type = 3;
  // When Filter Type is FIELD_TYPE we will convert the type for this field_id
  string field_id = 4;
}

enum ProjectionFilterType {
  PROJECTION_FILTER_TYPE_INVALID = 0;
  PROJECTION_FILTER_TYPE_FIELD_TYPE = 1;
  PROJECTION_FILTER_TYPE_INCLUDE_DELETED = 2;
}

message DeleteCrmObjectRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // The identifier of the object to be deleted
  string crm_object_id = 2;
}

message CrmObject {
  // The identifier of the object
  string crm_object_id = 1;
  // The external_id of the object, if any
  string external_id = 2;
  // Identifiee of the owner of the object
  string owner_id = 3;
  // A CRM object is composed of several fields containing values
  repeated FieldValue fields = 4;
  // When the object was created
  google.protobuf.Timestamp created = 5;
  // When the object was last updated
  google.protobuf.Timestamp updated = 6;
  // CRM do only soft delete of its objects. If the object was deleted, this is when it was deleted, otherwise it is time zero.
  google.protobuf.Timestamp deleted = 7;
  // Used for multi-location
  string group_id = 8;
  // Used to uniquely identify this object
  string idempotency_id = 9;
  // Some kind of objects have subtypes, this is used mainly by custom objects
  string crm_object_subtype = 10;
  // Namespace only used for multi-location responses, safe to ignore on requests
  string namespace = 11;
}

message StringList {
  // A list of values of type string
  repeated string values = 1;
}

// Values for geo-location
message Geopoint {
  // Latitude
  double lat = 1;
  // Longitude
  double lon = 2;
}

message Currency {
  // ISO 4217 currency code
  string currency_code = 1;
  // Amount
  double value = 2;
}

// FieldValue represents one of the fields of a crm object (Contact, Company,
// etc...) On Create/Update we need the field_id or external_id filled, with the
// field_id taking precedence over external_id.
// When retrieving both field_id and external_id will be returned.
message FieldValue {
  // The identifier of the field
  string field_id = 1;
  // An external identifier for the field, not required
  string external_id = 2;
  // Depending on type of the field, a different type of value
  oneof value {
    // Empty value
    google.protobuf.NullValue null_value = 3;
    // An Integer value
    int64 integer_value = 4;
    // A string value
    string string_value = 5;
    // A timestamp value
    google.protobuf.Timestamp date_value = 6;
    // A boolean value
    bool boolean_value = 7;
    // A list of strings value
    StringList string_values = 8;
    // A geolocation value
    Geopoint geopoint_value = 9;
    // A float number value
    float float_value = 10;
    // Phone number in small parts
    PhoneFields phone_field_values = 11;
    // Currency type and value
    Currency currency_value = 12;
  }
}

message PhoneFields {
  //ISO country code is the 2 or 3 letter code for the country, example 'CA' for Canada
  string iso_country_code = 1;
  // The main phone number without the country code
  string national_number = 2;
  // An extension number
  string extension = 3;
  // This flags signals if the number is compliant with the E164 format
  bool e164_compliant = 4;
}

// CrmObjectSearch represents the possible ways to search data in the CRM
message CrmObjectSearch {
  // As of now, we are only searching by string
  string search_term = 1;
}

enum SortDirection {
  SORT_DIRECTION_INVALID = 0;
  SORT_DIRECTION_ASCENDING = 1;
  SORT_DIRECTION_DESCENDING = 2;
}

message SortBy {
  // Id of the field to sort by
  string field_id = 1;
  // Direction used by the sorting process (ascending or descending)
  SortDirection sort_direction = 2;
}

message ListCrmObjectsRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // Options for pagination
  vendastatypes.PagedRequestOptions paging_options = 2;
  // A search term
  CrmObjectSearch search = 3;
  reserved 4;
  // Options for sorting the results
  repeated SortBy sort_by = 5;
  // List of filters to use
  galaxytypes.v1.FilterGroup filters_v2 = 6;
  // Projection filters are used to hydrate, enhance or transform the results
  repeated ObjectProjectionFilter projection_filters = 7;
  // Special filter used only for custom objects
  string crm_object_subtype = 8;
}

message ListCrmObjectsResponse {
  // List of objects returned
  repeated CrmObject crm_objects = 1;
  // Metadata for pagination
  vendastatypes.PagedResponseMetadata paging_metadata = 3;
}

message ListMultiLocationCrmObjectsRequest {
  // Namespace can be either a PID or an AGID
  repeated string namespaces = 1;
  reserved 2;
  // Options for pagination
  vendastatypes.PagedRequestOptions paging_options = 3;
  // A search term
  CrmObjectSearch search = 4;
  // Options for sorting the results
  repeated SortBy sort_by = 5;
  // List of filters to use
  galaxytypes.v1.FilterGroup filters_v2 = 6;
  // Projection filters are used to hydrate, enhance or transform the results
  repeated ObjectProjectionFilter projection_filters = 7;
  // Special filter used only for custom objects
  string crm_object_subtype = 8;
}

message ListMultiLocationCrmObjectsResponse {
  // List of objects returned
  repeated CrmObject crm_objects = 1;
  // Metadata for pagination
  vendastatypes.PagedResponseMetadata paging_metadata = 2;
}

message ValidateCrmObjectRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // Object to be validated
  CrmObject crm_object = 2;
}

message ValidateCrmObjectResponse {
  enum FieldUniqueness {
    FIELD_UNIQUENESS_INVALID = 0;
    FIELD_UNIQUENESS_NONE = 1;
    FIELD_UNIQUENESS_WARNING = 2;
    FIELD_UNIQUENESS_ENFORCED = 3;
  }
  message UniquenessCheck {
    // Id of the field that is restricted to be unique
    string field_id = 1;
    // List of IDs of objects that share the same value of the field
    repeated string existing_crm_object_ids = 2;
    // Result of the validation test
    FieldUniqueness uniqueness = 3;
  }
  message ValidationErrorDetails {
    oneof details {
      UniquenessCheck uniqueness_check = 1;
    }
    // list of fields with validation errors
    repeated string field_ids = 2;
  }
  enum ValidationErrorSeverity {
    VALIDATION_ERROR_SEVERITY_UNKNOWN = 0;
    VALIDATION_ERROR_SEVERITY_INVALID = 1;
    VALIDATION_ERROR_SEVERITY_WARNING = 2;
  }

  message ValidationError {
    // validation rule identifier
    string rule_id = 1;
    // Validation message, this can be a translation key
    string message = 2;
    // Severity of the error
    ValidationErrorSeverity severity = 3;
    // Error detail
    ValidationErrorDetails details = 4;
  }
  // List of errors
  repeated ValidationError errors = 1;
}

message ListFieldOptionsRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // Id of the field for the options search
  string field_id = 2;
  // Pagination metadata
  int64 page_size = 3;
  // Search option filter
  string search_term = 4;
  // Object subtype, used for custom objects
  string crm_object_subtype = 5;
}

message ListFieldOptionsResponse {
  // List of options
  repeated string options = 1;
}

message ListMultiLocationFieldOptionsRequest {
  // Namespaces can be either PIDs or AGIDs
  repeated string namespaces = 1;
  reserved 2;
  // Id of the field for the options search
  string field_id = 3;
  // Pagination metadata
  int64 page_size = 4;
  // Search option filter
  string search_term = 5;
}

message ListMultiLocationFieldOptionsResponse {
  // List of options
  repeated string options = 1;
}

message StartBulkCompanyCreateRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // A term that is added as a tag to the companies being created
  string search_term = 2;
  // List of companies to be created
  repeated CrmObject crm_objects = 3;
}

message StartBulkCompanyCreateResponse {
  // The tag created based on the search_term passed and timestamp of creation
  string filter_tag = 1;
}

message GetOpportunityValueSummaryRequest {
  // Namespace can be either a PID or an AGID
  string namespace = 1;
  // Search filters
  galaxytypes.v1.FilterGroup filters = 2;
}

message GetOpportunityValueSummaryResponse {
  message Amounts {
    Currency total = 1;
    Currency estimated = 2;
  }

  message StageAmounts {
    string stage_id = 1;
    Amounts amounts = 2;
  }

  repeated StageAmounts stages = 1;
  Amounts pipeline = 2;
}

message BulkDeleteCrmObjectsRequest {
  string namespace = 1;
  CrmObjectSearch search = 2;
  galaxytypes.v1.FilterGroup filters = 3;
}
