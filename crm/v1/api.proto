syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.crm.v1.generated";

import "crm/v1/fields.proto";
import "crm/v1/field_schema_customization.proto";
import "crm/v1/filters.proto";
import "crm/v1/crm_objects.proto";
import "crm/v1/activities.proto";
import "crm/v1/associations.proto";
import "crm/v1/importer.proto";
import "crm/v1/field_layout.proto";
import "crm/v1/field_extensions.proto";
import "crm/v1/namespace_admin.proto";
import "crm/v1/crm_search_pit.proto";
import "crm/v1/crm_objects_change_log.proto";
import "google/protobuf/empty.proto";
import "vendasta_types/annotations.proto";
import "crm/v1/score.proto";
import "crm/v1/pipeline.proto";
import "crm/v1/custom_objects_type.proto";
import "galaxytypes/v1/galaxy_filters.proto";
import "crm/v1/view_configurations.proto";

// CRMService - manages CRM contact records
service CRMService {
  // Creates a new contact in the CRM
  rpc CreateContact(CreateCrmObjectRequest) returns (CreateCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  }
  // Updates an existing contact in the CRM
  rpc UpdateContact(UpdateCrmObjectRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  }
  // Retrieves multiple contacts by their IDs
  rpc GetMultiContact(GetMultiCrmObjectRequest) returns (GetMultiCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  }

  // List contacts based on filters, allows sorting and pagination
  rpc ListContacts(ListCrmObjectsRequest) returns (ListCrmObjectsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  }
  // List the available filters for searching contacts
  rpc ListContactsFilters(ListFiltersRequest) returns (ListFiltersResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  };
  // Deletes a contact from the CRM
  rpc DeleteContact(DeleteCrmObjectRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact"
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  };
  // Validates that data on a Contact is good to be created/updated on the CRM
  rpc ValidateContact(ValidateCrmObjectRequest) returns (ValidateCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact"
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  };

  // Returns a list of values that are valid for string based field types.
  rpc ListContactFieldOptions(ListFieldOptionsRequest) returns (ListFieldOptionsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
  };

  rpc BulkDeleteContacts(BulkDeleteCrmObjectsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact"
    };
    option (vendastatypes.mcp) = {
      server_id: "contact"
    };
  }
}

// CRMActivityService - Manages CRM activity records
service CRMActivityService {
  // Creates a new activity in the CRM
  rpc CreateActivity(CreateActivityRequest) returns (CreateActivityResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity"
    };
  }
  // Updates an existing activity in the CRM
  rpc UpdateActivity(UpdateActivityRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity"
    };
  }
  // Retrieves multiple activity records by their IDs
  rpc GetMultiActivity(GetMultiActivityRequest) returns (GetMultiActivityResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity",
      scope: "crm.activity:read"
    };
  }
  // List activities based on filters, allows sorting and pagination
  rpc ListActivities(ListActivitiesRequest) returns (ListActivitiesResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity",
      scope: "crm.activity:read"
    };
  }
  // List the available filters for searching activities
  rpc ListActivityFilters(ListFiltersRequest) returns (ListFiltersResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity",
      scope: "crm.activity:read"
    };
  }
  // Deletes an activity from the CRM
  rpc DeleteActivity(DeleteActivityRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity",
    };
  }
  // Validates that data on an Activity is good to be created/updated on the CRM
  rpc ValidateActivity(ValidateActivityRequest) returns (ValidateActivityResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  }

  // Returns a list of values that are valid for string based field types.
  rpc ListActivityFieldOptions(ListFieldOptionsRequest) returns (ListFieldOptionsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  // Returns a list of metrics for the owner of the activity and a summary of metrics
  rpc GetActivityMetrics(GetActivityMetricsRequest) returns (GetActivityMetricsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  rpc BulkDeleteActivities(BulkDeleteActivitiesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity"
    };
  }
}

// CRMAssociationService - Manages associations between CRM objects
service CRMAssociationService {
  // Creates a new associations between CRM objects
  rpc CreateAssociation(CreateAssociationRequest) returns (CreateAssociationResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
    };
  };
  // Updates an existing association between CRM objects
  rpc UpdateAssociation(UpdateAssociationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
    };
  };
  // Retrieves multiple associations by their IDs
  rpc GetMultiAssociation(GetMultiAssociationRequest) returns (GetMultiAssociationResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
      scope: "crm.association:read",
    };
  };
  // List associations based on filters, allows sorting and pagination
  rpc ListAssociation(ListAssociationsRequest) returns (ListAssociationsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
      scope: "crm.association:read",
    };
  };
  // List the available filters for searching associations
  rpc ListAssociationFilters(ListAssociationsRequest) returns (ListAssociationsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
      scope: "crm.association:read",
    };
  };
  // Deletes an association between specified CRM objects
  rpc DeleteAssociation(DeleteAssociationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
    };
  };
  // Returns a list of values that are valid for string based field types.
  rpc ListAssociationFieldOptions(ListAssociationFieldOptionsRequest) returns (ListAssociationFieldOptionsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
      scope: "crm.association:read",
    };
  };

  // Queries association data directly from vstore
  rpc FindAssociations(FindAssociationsRequest) returns (FindAssociationsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.association",
      scope: "crm.association:read",
    };
  }
}

// CRMCompanyService - Manages CRM company records
service CRMCompanyService {
  // Creates a new company in the CRM.
  rpc CreateCompany(CreateCrmObjectRequest) returns (CreateCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
    };
  }
  // Updates an existing company in the CRM.
  rpc UpdateCompany(UpdateCrmObjectRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
    };
  }
  // Retrieves multiple companies by their IDs.
  rpc GetMultiCompany(GetMultiCrmObjectRequest) returns (GetMultiCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
      scope: "crm.company:read"
    };
  }
  // List companies based on filters, allows sorting and pagination
  rpc ListCompanies(ListCrmObjectsRequest) returns (ListCrmObjectsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
      scope: "crm.company:read"
    };
  }
  // List the available filters for searching companies
  rpc ListCompaniesFilters(ListFiltersRequest) returns (ListFiltersResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
      scope: "crm.company:read"
    };
  };
  // Deletes a company from the CRM.
  rpc DeleteCompany(DeleteCrmObjectRequest) returns (google.protobuf.Empty);
  // Validates that data on a Company is good to be created/updated on the CRM
  rpc ValidateCompany(ValidateCrmObjectRequest) returns (ValidateCrmObjectResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
    };
  };

  // Returns a list of values that are valid for string based field types.
  rpc ListCompanyFieldOptions(ListFieldOptionsRequest) returns (ListFieldOptionsResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company",
      scope: "crm.company:read"
    };
  };
  // Initiates a process to create several companies
  rpc StartBulkCompanyCreate(StartBulkCompanyCreateRequest) returns (StartBulkCompanyCreateResponse) {
    option (vendastatypes.access) = {
      scope: "crm.company",
    };
  }
  rpc BulkDeleteCompanies(BulkDeleteCrmObjectsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.company"
    };
  }
}

// CRMOpportunityService - Manages CRM opportunity records.
service CRMOpportunityService {
  // Creates a new opportunity object in the CRM.
  rpc CreateOpportunity(CreateCrmObjectRequest) returns (CreateCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
    };
  }
  // Updates an existing opportunity object in the CRM.
  rpc UpdateOpportunity(UpdateCrmObjectRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
    };
  }
  // Retrieves multiple opportunity objects by their IDs.
  rpc GetMultiOpportunity(GetMultiCrmObjectRequest) returns (GetMultiCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read"
    };
  }
  // List opportunity objects based on filters, allows sorting and pagination
  rpc ListOpportunities(ListCrmObjectsRequest) returns (ListCrmObjectsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read"
    };
  }
  // List the available filters for searching opportunity objects in the CRM.
  rpc ListOpportunitiesFilters(ListFiltersRequest) returns (ListFiltersResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read"
    };
  };
  // Deletes an opportunity object from the CRM.
  rpc DeleteOpportunity(DeleteCrmObjectRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
    };
  };
  rpc GetOpportunityValueSummary(GetOpportunityValueSummaryRequest) returns (GetOpportunityValueSummaryResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read"
    };
  };
  // Returns a list of values that are valid for string based field types.
  rpc ListOpportunityFieldOptions(ListFieldOptionsRequest) returns (ListFieldOptionsResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read"
    };
  };
  // Validates that data on a Opportunity object is good to be created/updated on the CRM
  rpc ValidateOpportunity(ValidateCrmObjectRequest) returns (ValidateCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read"
    };
  };
}

// CRMImporterService - Handles importing data into the CRM.
service CRMImporterService {
  // Retrieves data for multiple file uploads.
  rpc GetMultiFileUploadData(GetMultiFileUploadDataRequest) returns (GetMultiFileUploadDataResponse);
  // deprecated : Lists all file uploads.
  rpc ListFileUploads(ListFileUploadsRequest) returns (ListFileUploadsResponse) {
    option deprecated = true;
  };
  // Imports CRM objects for multiple object types from a file.
  rpc MultiObjectTypeImportFromFile(MultiObjectTypeImportFromFileRequest) returns (MultiObjectTypeImportFromFileResponse);
  // List information about files uploaded
  rpc ListImportRecords(ListImportRecordsRequest) returns (ListImportRecordsResponse);
  rpc GetMultiImportRecord(GetMultiImportRecordRequest) returns (GetMultiImportRecordResponse);
}

message CrmExportObjectsRequest {
  string namespace = 1;
  string object_type = 2;
  CrmObjectSearch search = 3;
  galaxytypes.v1.FilterGroup filters = 4;
}

// CRMFieldSchemaService - Manages CRM field schemas.
service CRMFieldSchemaService {
  // Creates a new field schema.
  rpc CreateFieldSchema(CreateFieldSchemaRequest) returns (CreateFieldSchemaResponse);
  // Retrieves multiple field schemas by their IDs.
  rpc GetMultiFieldSchema(GetMultiFieldSchemaRequest) returns (GetMultiFieldSchemaResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.schema:read",
    };
  }
  // List the field schemas based on specified criteria (object type, subtype or field extension)
  rpc ListFieldSchema(ListFieldSchemaRequest) returns (ListFieldSchemaResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.schema:read",
    };
  };
  // Updates an existing field schema.
  rpc UpdateFieldSchema(UpdateFieldSchemaRequest) returns (google.protobuf.Empty);
  // Archives a field schema.
  rpc ArchiveFieldSchema(ArchiveFieldSchemaRequest) returns (google.protobuf.Empty);
  // Unarchives a field schema.
  rpc UnarchiveFieldSchema(UnarchiveFieldSchemaRequest) returns (google.protobuf.Empty);
}

// CRMFieldLayoutService - Manages CRM field layouts.
service CRMFieldLayoutService {
  // Retrieve a list of fields organized in groups
  rpc ListFieldGroups(ListFieldGroupsRequest) returns (ListFieldGroupsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
}

// CRMFieldSchemaCustomizationService - Manages customization of field schemas - which allows the user to set allowed values for one field
service CRMFieldSchemaCustomizationService {
  // Creates a new field schema customization. Sets the allowed values for a field using this schema on this specific namespace
  rpc CreateFieldSchemaCustomization(CreateFieldSchemaCustomizationRequest) returns (google.protobuf.Empty);
  //  Retrieves multiple field schema customizations.
  rpc GetMultiFieldSchemaCustomization(GetMultiFieldSchemaCustomizationRequest) returns (GetMultiFieldSchemaCustomizationResponse);
  // Updates an existing field schema customization.
  rpc UpdateFieldSchemaCustomization(UpdateFieldSchemaCustomizationRequest) returns (google.protobuf.Empty);
  // Deletes a field schema customization.
  rpc DeleteFieldSchemaCustomization(DeleteFieldSchemaCustomizationRequest) returns (google.protobuf.Empty);
}

// CRMFieldExtensionAdminService - Manages CRM field extensions. Field extensions are groups of fields.
service CRMFieldExtensionAdminService {
  // CreateFieldExtension creates and holds information about a namespace containing fields that should be available for zero-to-many CRM instances
  rpc CreateFieldExtension(CreateFieldExtensionRequest) returns (CreateFieldExtensionResponse);
  // Retrieves multiple field extensions.
  rpc GetMultiFieldExtension(GetMultiFieldExtensionRequest) returns (GetMultiFieldExtensionResponse);
  // Updates an existing field extension.
  rpc UpdateFieldExtension(UpdateFieldExtensionRequest) returns (google.protobuf.Empty);
  // Experimental - DeleteFieldExtension won't remove namespaces that are extending other namespaces at the moment
  rpc DeleteFieldExtension(DeleteFieldExtensionRequest) returns (google.protobuf.Empty);
}

// CRMFieldExtensionService - Manages how field extensions are applied to namespaces
service CRMFieldExtensionService {
  // Add a field extension to a namespace
  rpc AddFieldExtension(AddFieldExtensionRequest) returns (google.protobuf.Empty);
  // Remove a field extension from a namespace
  rpc RemoveFieldExtension(RemoveFieldExtensionRequest) returns (google.protobuf.Empty);
  // List currently added field extensions to a namespace
  rpc ListAddedExtensions(ListAddedExtensionsRequest) returns (ListAddedExtensionsResponse);
}

// NamespaceAdminService - Manages how namespaces are organized in CRM. Namespaces in CRM can be PIDs or AGIDs, usually the PID is the parent of an AGID.
service NamespaceAdminService {
  // Create the relationship between a namespace and its parent
  rpc CreateParentNamespace(CreateParentNamespaceRequest) returns (google.protobuf.Empty);
  // Retrieves multiple parent namespaces for the namespaces
  rpc GetMultiParentNamespace(GetMultiParentNamespaceRequest) returns (GetMultiParentNamespaceResponse);
  // Removes the relationship between a namespace and its parent
  rpc DeleteParentNamespace(DeleteParentNamespaceRequest) returns (google.protobuf.Empty);
}

// CRMChangeLogService - Provides access to CRM object change logs.
service CRMChangeLogService {
  // Retrieves the information of change logs over objects
  rpc GetMultiChangeLog(GetMultiCrmObjectChangeLogRequest) returns (GetMultiCrmObjectChangeLogResponse);
  // List change logs for objects based on filters
  rpc ListChangeLogs(ListChangeLogsRequest) returns (ListChangeLogsResponse);
}

// CrmSearchPITService - Provides point-in-time search capabilities for CRM objects.
service CrmSearchPITService {
  // Searches CRM objects using point in time - This is used for very large responses from elastic searches that are paginated,
  // and we must keep control so we don't lost context of the results when paginating.
  rpc SearchPointInTimeCrmObjects(SearchPITCrmObjectsRequest) returns (SearchPITCrmObjectsResponse);
}

service Scoring {
  rpc CreateScoringDefinition(CreateScoringDefinitionRequest) returns (CreateScoringDefinitionResponse);
  rpc GetMultiScoringDefinition(GetMultiScoringDefinitionRequest) returns (GetMultiScoringDefinitionResponse);
  rpc DeleteScoringDefinition(DeleteScoringDefinitionRequest) returns (google.protobuf.Empty);
  rpc ListScoringDefinitions(ListScoringDefinitionsRequest) returns (ListScoringDefinitionsResponse);
  rpc SetScoringDefinitionsForFieldID(SetScoringDefinitionsForFieldIDRequest) returns (google.protobuf.Empty);
  rpc GetScore(GetScoreRequest) returns (GetScoreResponse);
}

service Pipelines {
  rpc CreatePipeline(CreatePipelineRequest) returns (CreatePipelineResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.company",
      scope: "crm.contact",
    };
  };
  rpc GetMultiPipeline(GetMultiPipelineRequest) returns (GetMultiPipelineResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read",
      scope: "crm.company",
      scope: "crm.company:read",
      scope: "crm.contact",
      scope: "crm.contact:read",
    };
  };
  rpc UpdatePipeline(UpdatePipelineRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.company",
      scope: "crm.contact",
    };
  };
  rpc DeletePipeline(DeletePipelineRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.company",
      scope: "crm.contact",
    };
  };
  rpc ListPipelines(ListPipelinesRequest) returns (ListPipelinesResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.opportunity",
      scope: "crm.opportunity:read",
      scope: "crm.company",
      scope: "crm.company:read",
      scope: "crm.contact",
      scope: "crm.contact:read",
    };
  };
}

// CRMMultiLocationActivityService - Provides access to CRM activities for multiple namespaces (multi-location)
service CRMMultiLocationActivityService {
  // List activities for multiple namespaces, allows filtering and sorting
  rpc ListMultiLocationActivities(ListMultiLocationActivitiesRequest) returns (ListMultiLocationActivitiesResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity",
      scope: "crm.activity:read"
    };
  }
  // List the filter options available for multiple namespaces
  rpc ListMultiLocationActivityFilters(ListMultiLocationFiltersRequest) returns (ListMultiLocationFiltersResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.activity",
      scope: "crm.activity:read"
    };
  }
  // List the options for fields available for multiple namespaces
  rpc ListMultiLocationActivityFieldOptions(ListMultiLocationFieldOptionsRequest) returns (ListMultiLocationFieldOptionsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
}

// Provides access to CRM contacts for multiple namespaces (multi-location)
service CRMMultiLocationContactService {
  // List contacts for multiple namespaces, allows filtering and sorting
  rpc ListMultiLocationContacts(ListMultiLocationCrmObjectsRequest) returns (ListMultiLocationCrmObjectsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
  }
  // List the filter options available for multiple namespaces
  rpc ListMultiLocationContactFilters(ListMultiLocationFiltersRequest) returns (ListMultiLocationFiltersResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
  }
  // List the options for fields available for multiple namespaces
  rpc ListMultiLocationContactFieldOptions(ListMultiLocationFieldOptionsRequest) returns (ListMultiLocationFieldOptionsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.contact",
      scope: "crm.contact:read"
    };
  };
}

// Provides access to field schemas for multiple namespaces (multi-location)
service CRMMultiLocationFieldSchemaService {
  // List field schemas for multiple namespaces, allows filtering and sorting
  rpc ListMultiLocationFieldSchema(ListMultiLocationFieldSchemaRequest) returns (ListMultiLocationFieldSchemaResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.schema:read",
    };
  }
}

// Provides access to field groups for multiple namespaces (multi-location)
service CRMMultiLocationFieldLayoutService {
  // List field groups for multiple namespaces allowing pagination
  rpc ListMultiLocationFieldGroups(ListMultiLocationFieldGroupsRequest) returns (ListMultiLocationFieldGroupsResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  }
}

// CRMCustomObjectTypeService - Manages CRM custom object types
service CRMCustomObjectTypeService {
  // Creates a new custom object type.
  rpc CreateCustomObjectType(CreateCustomObjectTypeRequest) returns (CreateCustomObjectTypeResponse){
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  // Retrieves multiple custom object types by IDs
  rpc GetMultiCustomObjectType(GetMultiCustomObjectTypeRequest) returns (GetMultiCustomObjectTypeResponse){
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  // Updates an existing custom object type.
  rpc UpdateCustomObjectType(UpdateCustomObjectTypeRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  // Deletes a custom object type.
  rpc DeleteCustomObjectType(DeleteCustomObjectTypeRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  // Lists all custom object types for a namespace.
  rpc ListCustomObjectTypes(ListCustomObjectTypesRequest) returns (ListCustomObjectTypesResponse){
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
  rpc BulkDeleteCustomObjectsType(BulkDeleteCustomObjectsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "crm",
    };
  };
}

// CRMCustomObjectService - Manages custom objects
service CRMCustomObjectService {
  // Creates a new custom object on the CRM
  rpc CreateCustomObject(CreateCrmObjectRequest) returns (CreateCrmObjectResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
    };
  };
  // Retrieves multiple custom objects from CRM by IDs
  rpc GetMultiCustomObject(GetMultiCrmObjectRequest) returns (GetMultiCrmObjectResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
      scope: "crm.custom-object:read",
    };
  };
  // Updates an existing custom object on CRM.
  rpc UpdateCustomObject(UpdateCrmObjectRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
    };
  };
  // Deletes a custom object
  rpc DeleteCustomObject(DeleteCrmObjectRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
    };
  };
  // List all custom objects, allows filtering, sorting and pagination
  rpc ListCustomObjects(ListCrmObjectsRequest) returns (ListCrmObjectsResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
      scope: "crm.custom-object:read",
    };
  };
  // Validates data on a custom object for creating/updating
  rpc ValidateCustomObject(ValidateCrmObjectRequest) returns (ValidateCrmObjectResponse) {
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
    };
  }
  // List all the filters available for search custom objects
  rpc ListCustomObjectFilters(ListFiltersRequest) returns (ListFiltersResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
      scope: "crm.custom-object:read",
    };
  };
  // List all the options available for fields on custom objects
  rpc ListCustomObjectFieldOptions(ListFieldOptionsRequest) returns (ListFieldOptionsResponse){
    option (vendastatypes.access) = {
      scope: "crm",
      scope: "crm.custom-object",
      scope: "crm.custom-object:read",
    };
  };
}

service ViewConfigurations {
  rpc CreateViewConfiguration(CreateViewConfigurationRequest) returns (CreateViewConfigurationResponse);
  rpc GetMultiViewConfiguration(GetMultiViewConfigurationRequest) returns (GetMultiViewConfigurationResponse);
  rpc DeleteViewConfiguration(DeleteViewConfigurationRequest) returns (google.protobuf.Empty);
  rpc ListViewConfiguration(ListViewConfigurationsRequest) returns (ListViewConfigurationsResponse);
  rpc UpdateViewConfigurations(UpdateViewConfigurationRequest) returns (google.protobuf.Empty);
}
