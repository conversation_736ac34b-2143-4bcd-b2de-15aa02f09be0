syntax = "proto3";

package crm.v1;

option go_package = "github.com/vendasta/generated-protos-go/crm/v1;crm_v1";
option java_outer_classname = "ImporterProto";
option java_package = "com.vendasta.crm.v1.generated";

import "google/protobuf/timestamp.proto";
import "vendasta_types/paging.proto";

message ListFileUploadsRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 3;
}

message CSVSize {
  string total_rows = 1;
  bool is_estimate = 2;
}

message Header {
  string name = 1;
  repeated string sample_data = 2;
}

message CSVFile {
  string id = 1;
  string filename = 2;
  string url = 3;
  repeated Header headers = 4;
  google.protobuf.Timestamp created = 5;
  CSVSize size = 6;
  repeated CSVHeaderMapping mapping_suggestions = 7;
}

message ListFileUploadsResponse {
  repeated CSVFile files = 1;
}

message GetMultiFileUploadDataRequest {
  string namespace = 1;
  string crm_object_type = 2;
  string crm_object_subtype = 4;
  repeated string filename = 3;
}

message GetMultiFileUploadDataResponse {
  repeated CSVFile files = 1;
}

message CSVHeaderMapping {
  string header_name = 1;
  string field_id = 2;
  string crm_object_type = 3;
  string crm_object_subtype = 4;
}

enum DuplicateHandlingType {
  DUPLICATE_HANDLING_TYPE_INVALID = 0;
  DUPLICATE_HANDLING_TYPE_SKIP = 1;
  DUPLICATE_HANDLING_TYPE_UPDATE = 2;
}

message MultiObjectTypeImportFromFileRequest {
  string namespace = 1;
  string filename = 2;
  repeated CSVHeaderMapping mappings = 3;
  DuplicateHandlingType duplicate_handling_type = 4;
}

message MultiObjectTypeImportFromFileResponse {
  string import_id = 1;
}

enum ImportRecordStatus {
   IMPORT_RECORD_STATUS_STARTED = 0;
   IMPORT_RECORD_STATUS_COMPLETED = 1;
   IMPORT_RECORD_STATUS_ERROR = 2;
}

message ImportRecord {
  string import_id = 1;
  string namespace = 2;
  string file_name = 3;
  repeated CSVHeaderMapping mapping = 4;
  ImportRecordStatus status = 5;
  int64 items_processed = 6;
  int64 items_error = 7;
  google.protobuf.Timestamp created = 8;
  google.protobuf.Timestamp updated = 9;
  // authenticated URLs for the original and error CSV files
  string original_csv_url = 10;
  string error_csv_url = 11;
}

message ListImportRecordsRequest {
  string namespace = 1;
  vendastatypes.PagedRequestOptions paging_options = 2;
}

message ListImportRecordsResponse {
  repeated ImportRecord import_record = 1;
  vendastatypes.PagedResponseMetadata paging_metadata = 3;
}

message GetMultiImportRecordRequest {
  string namespace = 1;
  repeated string import_ids = 2;
}

message GetMultiImportRecordResponse {
  repeated ImportRecord import_records = 1;
}
