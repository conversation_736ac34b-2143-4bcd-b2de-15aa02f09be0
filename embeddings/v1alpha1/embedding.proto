syntax = "proto3";

package embeddings.v1alpha1;

option go_package = "github.com/vendasta/generated-protos-go/embeddings/v1alpha1;embeddings";
option java_outer_classname = "EmbeddingProto";
option java_package = "com.vendasta.embeddings.v1alpha1.generated";

import "google/protobuf/timestamp.proto";

message Embedding {
  reserved 3;
  // The embedding ID.
  string id = 1;
  // The content vectorized into an embedding.
  string content = 2;
  EmbeddingMetadata metadata = 4;
}

message EmbeddingMetadata {
  // The IAM user that created the embedding. Can be a service account. Can be set only on create.
  string created_by = 1;
  // The time the embedding was created. Read-only.
  google.protobuf.Timestamp created_at = 2;
  // The time the embedding was last updated. Read-only.
  google.protobuf.Timestamp updated_at = 3;
  // The platform resource that this embedding is related to. Can only be set on create.
  EmbeddingRelationship relationship = 4;
  // The source of the embedding.  Can be set only on create.
  EmbeddingSourceType source_type = 5;
  // The feature or feature set that the embedding was created by/for. Can be set only on create.
  string source_feature = 6;
  // The embedding model used to create the embedding. Can be set only on create.
  EmbeddingVendorModel vendor_model = 7;
  // Content citation. Optional.
  EmbeddingCitation citation = 8;
  // Whether the embedding is disabled. Disabled embeddings are not used in search.
  bool disabled = 9;
  // Tags associated with the embedding. Optional.
  // Tags can be set on create and used to filter embeddings in search.
  repeated Tag tags = 10;
}

message Tag {
  // The tag key.
  string key = 1;
  // The tag value.
  string value = 2;
}

enum EmbeddingSourceType {
  EMBEDDING_SOURCE_TYPE_INVALID = 0;
  EMBEDDING_SOURCE_TYPE_USER_INPUT = 1;
  EMBEDDING_SOURCE_TYPE_WEBSITE = 2;
  EMBEDDING_SOURCE_TYPE_BUSINESS_PROFILE = 3;
  EMBEDDING_SOURCE_TYPE_CATEGORIES = 4;
  EMBEDDING_SOURCE_TYPE_FILE = 5;
}

enum EmbeddingVendorModel {
  EMBEDDING_MODEL_INVALID = 0;
  EMBEDDING_MODEL_OPENAI_ADA_V2 = 1;
  EMBEDDING_MODEL_GOOGLE_GECKO_V2 = 2;
  EMBEDDING_MODEL_TUNED_CATEGORY = 3;
}

enum EmbeddingRerankerMethod {
  EMBEDDING_RERANKER_METHOD_INVALID = 0;
  EMBEDDING_RERANKER_METHOD_PREQUERY_HYDE = 1;
}

message EmbeddingRelationship {
  oneof entity {
    EmbeddingRelationshipAccountGroup account_group = 1;
    EmbeddingRelationshipPartner partner = 2;
    EmbeddingRelationshipAdmin admin = 3;
  }
}

message EmbeddingRelationshipAccountGroup {
  string account_group_id = 1;
}

message EmbeddingRelationshipPartner {
  string partner_id = 1;
}

message EmbeddingRelationshipAdmin {

}

message EmbeddingCitation {
  // The link to the source of the content. Required.
  string link = 1;
  // The title of the source of the content. An application could use this to cite the content in a more readable way. Optional.
  string title = 2;
  // The description of the source of the content. An application could use this to cite the content in a more readable way. Optional.
  string description = 3;
  // A url where the file can be downloaded from
  string file_url = 4;
}
