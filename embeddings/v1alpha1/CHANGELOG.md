# CHANGELOG
## 0.53.0
- Add `EMBEDDING_SOURCE_TYPE_FILE` to support file-based embeddings

## 0.52.0
- Add `file_name` to `FileConfig`

## 0.51.0
- Add `excluded_url_paths` to `WebsiteScrapeOptions`

## 0.50.0
- Add File KnowledgeSource support with `FileConfig` and `KNOWLEDGE_SOURCE_CONFIG_TYPE_FILE`

## 0.49.0
- Add `automatic_refresh_days` and `refresh_scheduled_for` fields to `WebsiteScrapeConfig`

## 0.48.0
- add `file_url` to `EmbeddingCitation`

## 0.47.0
- @jredl-va commit that was reverted.

## 0.46.0
- Add `specific_urls` option to `ScrapeWebsiteRequest`

## 0.45.0
- Add `max_tokens` option to `GenerateChatAnswerWithEmbeddingsRequest`

## 0.44.2
- Fix docstring explanation of `similarity` on `FindSimiliarEmbeddingsResponse`

## 0.44.1
- Fix naming of `AppKeyV2` fields on `GenerateChatAnswerWithEmbeddingsRequest` and `SetAppsForKnowledgeSourceRequest`

## 0.44.0
- Deprecate `AppKey` and add `AppKeyV2` to replace it
- Add `external_id` to `App`

## 0.43.0
- Breaking Changes:
  - Remove `market_id` and `partner_id` from `EmbeddingRelationshipAccountGroup` and `AccountGroupNamespace`

## 0.42.0
- Remove `original_content` from `Embedding`

## 0.41.0
- Modifies the `GenerateChatAnswerWithEmbeddingsResponse` to include a fields that allow consumers to know that an async function started and the id of the async function.
- Adds a new rpc `GetFunctionExecutionJob` and associated messages to allow consumers to get the status of an async function.

## 0.40.0
- Add `Options` to `GenerateChatAnswerWithEmbeddingsRequest`

## 0.39.1
- Change `option` field name to `display_text` to be consistent with other places

## 0.39.0
- Add `booking_options` to `GenerateChatAnswerWithEmbeddingsResponse`

## 0.38.0
- Add `additional_instructions` to `GenerateChatAnswerWithEmbeddings`

## 0.37.0
- Add `AIAssistantService`
- Add `GenerateChatAnswerWithEmbeddings` to `AIAssistantService`

## 0.36.0
- Remove `configs` option from `UpsertKnowledgeForAppRequest`

## 0.35.0
- Add `WebsiteScrapeOptions` to `WebsiteScrapeConfig`

## 0.34.0
- Add `done_scraping` to `ScrapedPage`

## 0.33.0
- Add `in_progress_scraped_pages` to `WebsiteScrapeConfig`

## 0.32.1
- Changed name of `KnowledgeSourceTrainingProgress` to `KnowledgeSourceTraining`
- Changed name of `KnowledgeSourceTrainingProgressStatus` to `TrainingProgress`
- Simplified naming of `TrainingProgress` enum values

## 0.32.0
- Add `KnowledgeSourceTrainingProgress` to `KnowledgeSource`

## 0.31.0
- Add `error` to `WebsiteScrapeConfig`

## 0.30.0
- Add `TuneAIModel` endpoint

## 0.29.0
- Add `icon_url` to `App`

## 0.28.0
- Add `TUNED_CATEGORY` embedding model

## 0.27.1
- Fix misnamed in `AppKey` id

## 0.27.0
- Add `SetAppsForKnowledgeSource` to `KnowledgeBaseService`

## 0.26.0
- Add `app_type` and `app_type_name` to `App`

## 0.25.0
- Add `tags` to EmbeddingMetadata and create/list/search embeddings rpcs

## 0.24.3
- Add business-app/admin scope to the remaining rpcs

## 0.24.2
- Add business-app/admin scope to `GetKnowledgeSource` rpc

## 0.24.1
- Add admin scope to apis

## 0.24.0
- Add `WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_SITEMAP` to `WebsiteScrapeConfigCrawlMode` enum

## 0.23.3
- Add `EmbeddingRelationshipAdmin` to EmbeddingRelationship

## 0.23.2
- Remove unused `EmbeddingRerankerMethod` enum values

## 0.23.1
- Correct the `EmbeddingRerankerMethod` enum values ("Model" -> "Method")

## 0.23.0
- Add `AdminNamespace`

## 0.22.0
- Change RerankOptions to speak in terms of different reranking methods

## 0.21.0
- You can now leverage a reranker when finding similar embeddings by adding `RerankOptions` to `FindSimilarEmbeddingsRequest`

## 0.20.0
- Add `last_scraped` to `WebsiteScrapeConfig` and `ScrapedPage`

## 0.19.0
- Remove `scrape_in_progress` from `WebsiteScrapeConfig`

## 0.18.1
- Remove `scrape_error` from `WebsiteScrapeConfig`_

## 0.18.0
- Add `WebsiteScrapeConfigProgress` enum to more granularly track progress of website scrape
- Add `scrape_error` string property to `WebsiteScrapeConfig`

## 0.17.0
- Add `KNOWLEDGE_SOURCE_CONFIG_TYPE_WEBSITE_SCRAPE` to `KnowledgeSourceConfigType`

## 0.16.0
- Flesh out website scrape endpoint: `ScrapeWebsite`.
- Remove unused/unimplemented `GetRefreshAccountGroupWebsiteEmbeddingsProgress`

## 0.15.2
- Add `WebsiteScrapeConfig` to `KnowledgeSource`

## 0.15.1
- Add `scrape_in_progress` boolean property to `WebsiteScrapeConfig`

## 0.15.0
- Support website scrape knowledge source

## 0.14.0
- Add `business-app` scope to apis

## 0.13.0
- Add `knowledge_source_keys` to `UpsertKnowledgeForAppRequest`
- Add `config_types` to `ListKnowledgeSourcesRequest` filters
- Add `ListAllAppsForKnowledgeSource` to `KnowledgeBaseService`

## 0.12.0
- Add `ListAllKnowledgeSourcesForApp` to `KnowledgeBaseService`

## 0.11.0
- Add `associated_app_id` to `ListEmbeddingsRequest` filters

## 0.10.0
- Add `UpsertKnowledgeForApp` to `KnowledgeBaseService`

## 0.9.0
- Add `KnowledgeBaseService`
- Add `associated_app_id` to `FindSimilarEmbeddingsRequest` filters
- Add `EmbeddingRelationshipPartner` as an `EmbeddingRelationship`

## 0.8.0
- Add `RefreshAllBusinessProfileEmbeddings` to `AutomatedEmbeddingsService`

## 0.7.0
- Add `GetEmbedding` to `EmbeddingService`

## 0.6.0
- Breaking Changes:
  - Rename `RefreshAccountGroupNAPEmbeddings` to `RefreshBusinessProfileEmbeddings`
  - Rename `EMBEDDING_SOURCE_TYPE_NAP_DATA` to `EMBEDDING_SOURCE_TYPE_BUSINESS_PROFILE`

## 0.5.0
- Add `EMBEDDING_MODEL_GOOGLE_GECKO_V2` model option

## 0.4.0
- Remove `DisableEmbedding` RPC from `EmbeddingService`
- Accept multiple source types on filters in `ListEmbeddings` and `FindSimilarEmbeddings` RPCs
- Move `disabled` from `Embedding` to `EmbeddingMetadata`

## 0.3.0
- Return similarity on embeddings when they are returned via the `FindSimilarEmbeddings` RPC

## 0.2.0
- Only return id on `CreateEmbeddingResponse`

## 0.1.0
- Initial protos
