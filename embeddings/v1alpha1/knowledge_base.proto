syntax = "proto3";

package embeddings.v1alpha1;

option go_package = "github.com/vendasta/generated-protos-go/embeddings/v1alpha1;embeddings";
option java_outer_classname = "KnowledgeBaseProto";
option java_package = "com.vendasta.embeddings.v1alpha1.generated";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";

message KnowledgeBase {
  // the namespace-unique identifier for the knowledge base
  // On create, if this is empty, it will be auto-generated.
  string id = 1;
  // the namespace that the knowledge base belongs to. Can be set only on create.
  Namespace namespace = 2;
  // the name of the knowledge base
  string name = 3;
  // the description of the knowledge base
  string description = 4;
  // the knowledge sources that are used to populate the knowledge base
  repeated KnowledgeSourceKey knowledge_source_keys = 5;
}

message KnowledgeBaseKey {
  // the id of the knowledge base
  string knowledge_base_id = 1;
  // the namespace that the knowledge base belongs to.
  Namespace namespace = 2;
}

message KnowledgeSource {
  // the namespace-unique identifier for the knowledge source
  string id = 1;
  // the namespace that the knowledge source belongs to. Can be set only on create.
  Namespace namespace = 2;
  // the name of the knowledge source
  string name = 3;
  // the description of the knowledge source
  string description = 4;
  // the configuration for the knowledge source
  KnowledgeSourceConfig config = 5;
  // the knowledge bases that the knowledge source is associated with
  repeated KnowledgeBaseKey knowledge_base_keys = 6;
  // training progress information of a knowledge source
  KnowledgeSourceTraining training = 7;
}

message KnowledgeSourceKey {
  // the id of the knowledge source
  string knowledge_source_id = 1;
  // the namespace that the knowledge source belongs to.
  Namespace namespace = 2;
}

message KnowledgeSourceTraining {
  TrainingProgress progress = 1;
  int64 total_units = 2;
  int64 completed_units = 3;
}

enum TrainingProgress {
  TRAINING_INVALID = 0;
  TRAINING_QUEUED = 1;
  TRAINING_IN_PROGRESS = 2;
  TRAINING_DONE = 3;
}

message KnowledgeSourceConfig {
  message BusinessProfileConfig {
    string account_group_id = 1;
  }
  message CustomDataConfig {
    reserved 2;
    oneof content {
      // free-form text content
      string text = 1;
    }
  }
  message WebsiteScrapeConfig {
    // the url to scrape, should include the protocol (http/https)
    string url = 1;
    // the crawl mode to use when scraping the website
    WebsiteScrapeConfigCrawlMode crawl_mode = 2;
    reserved 3;
    message ScrapedPage {
      // the url of the page that was scraped
      string url = 1;
      // the title of the page
      string title = 2;
      // error message if the page could not be scraped
      string error_message = 3;
      // whether the page is enabled for training
      bool training_enabled = 4;
      // the last time the page was scraped
      google.protobuf.Timestamp last_scraped = 5;
      // a page is done scraping if it has finished retrieving content for the page or has an error message
      // this should only be relevant for in_progress_scraped_pages
      bool done_scraping = 6;
    }
    repeated ScrapedPage scraped_pages = 4;
    // the progress of the scrape
    WebsiteScrapeConfigProgress scrape_progress = 5;
    // the last time the website was scraped, which will be the oldest timestamp of all the scraped pages
    google.protobuf.Timestamp last_scraped = 6;
    // error if the provided url could not be scraped
    message WebsiteScrapeConfigError {
      // the error message
      string error_message = 1;
    }
    WebsiteScrapeConfigError error = 7;
    repeated ScrapedPage in_progress_scraped_pages = 8;
    // options for scraping the website
    message WebsiteScrapeOptions {
      // whether to disable auto training for the website scrape
      bool disable_auto_training = 1;
      // time after which the website scrape will be automatically refreshed.  If zero, the website scrape will not be automatically refreshed.
      google.protobuf.Duration automatic_refresh_time = 2;
      // url paths excluded from scrapes, example: /blog excludes all pages under the blog path of the scraped website
      repeated string excluded_url_paths = 3;
    }
    WebsiteScrapeOptions options = 9;
    // Read only - the time at which the website scrape is scheduled to be refreshed
    google.protobuf.Timestamp refresh_scheduled_for = 10;
  }
  message FileConfig {
    // the url to the uploaded file
    string file_url = 1;
    // Read only - the MIME type of the file
    string mimetype = 2;
    // the original name of the file
    string file_name = 3;
  }
  oneof config {
    // the configuration for a business profile knowledge source. Can only be used on AccountGroup namespaced sources.
    BusinessProfileConfig business_profile_config = 1;
    CustomDataConfig custom_data_config = 2;
    WebsiteScrapeConfig website_scrape_config = 3;
    FileConfig file_config = 4;
  }
}

enum KnowledgeSourceConfigType {
  KNOWLEDGE_SOURCE_CONFIG_TYPE_INVALID = 0;
  KNOWLEDGE_SOURCE_CONFIG_TYPE_BUSINESS_PROFILE = 1;
  KNOWLEDGE_SOURCE_CONFIG_TYPE_CUSTOM_DATA = 2;
  KNOWLEDGE_SOURCE_CONFIG_TYPE_WEBSITE_SCRAPE = 3;
  KNOWLEDGE_SOURCE_CONFIG_TYPE_FILE = 4;
}

enum WebsiteScrapeConfigCrawlMode {
  WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_INVALID = 0;
  // only scrape the specified url
  WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_SINGLE = 1;
  // scrape the specified url and follow links on the page
  WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_RECURSIVE = 2;
  // scrape via a sitemap either given directly by the url or on common paths relative to it (/robots.txt, /sitemap.xml, /sitemap_index.xml)
  WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_SITEMAP = 3;
}

enum WebsiteScrapeConfigProgress {
  WEBSITE_SCRAPE_CONFIG_PROGRESS_INVALID = 0;
  // the scrape is queued and waiting to start
  // A scrape may be in a queued state for some time if it is being rate limited
  WEBSITE_SCRAPE_CONFIG_PROGRESS_QUEUED = 1;
  // the scrape is in progress
  WEBSITE_SCRAPE_CONFIG_PROGRESS_IN_PROGRESS = 2;
  // the scrape has completed and/or no scrape is in progress
  WEBSITE_SCRAPE_CONFIG_PROGRESS_DONE = 3;
}

message App {
  // the id of the app that is implementing knowledge bases
  string id = 1;
  // the namespace that the app belongs to. Can be set only on create.
  Namespace namespace = 2;
  // the name of the app
  string name = 3;
  // a url for where the knowledge bases can be edited for the app
  string configuration_url = 4;
  // the knowledge bases that the app is associated with
  repeated KnowledgeBaseKey knowledge_base_keys = 5;
  // The type of app
  string app_type = 6;
  // The human-readable name of the app type. Read-only.
  string app_type_name = 7;
  // The url to an icon for the app. Read-only.
  string icon_url = 8;
  // the id of the external entity for the app
  string external_id = 9;
}

// Deprecated: Use AppKeyV2 instead.
message AppKey {
  // the id of the app
  string app_id = 1;
  // the namespace that the app belongs to.
  Namespace namespace = 2;
}

message AppKeyV2 {
  // the id of the external entity for the app
  string external_id = 1;
  // the namespace that the app belongs to.
  Namespace namespace = 2;
  // The type of app
  string app_type = 3;
}

message Namespace {
  message AccountGroupNamespace {
    string account_group_id = 1;
  }
  message PartnerNamespace {
    string partner_id = 1;
  }
  // only Vendasta admins can create and modify knowledge in this namespace
  message AdminNamespace {}
  oneof namespace {
    AccountGroupNamespace account_group_namespace = 1;
    PartnerNamespace partner_namespace = 2;
    AdminNamespace admin_namespace = 3;
  }
}
