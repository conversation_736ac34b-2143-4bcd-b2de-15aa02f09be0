syntax = "proto3";

package conversation.v1;

option go_package = "github.com/vendasta/generated-protos-go/conversation/v1;conversation_v1";
option java_outer_classname = "ConversationViewProto";
option java_package = "com.vendasta.conversation.v1.generated";
import "google/protobuf/timestamp.proto";
import "conversation/v1/conversation.proto";
import "conversation/v1/common.proto";

enum EventType {
  EVENT_TYPE_UNDEFINED = 0;
  EVENT_TYPE_SMS_UNSUBSCRIBED = 1;
  EVENT_TYPE_CAMPAIGN = 3;
  EVENT_TYPE_REVIEW_REQUEST = 4;
  EVENT_TYPE_FORM_SUBMISSION = 5;
  EVENT_TYPE_PHONE_CALL = 6;
  EVENT_TYPE_SMS_SUBSCRIBED = 7;
  EVENT_TYPE_DUPLICATE_CONVERSATION = 8;
}

message Event {
  string conversation_id = 1;
  string event_id = 2;
  google.protobuf.Timestamp happened_at = 3;
  string label_key = 4;
  string initiator_id = 5;
  google.protobuf.Timestamp created = 6;
  google.protobuf.Timestamp updated = 7;
  google.protobuf.Timestamp deleted = 8;
  string message = 9;
  ConversationChannel channel = 10;
  PlatformLocation originated_at = 11;
  EventType type = 12;
  repeated KeyValuePair metadata = 13;
}
