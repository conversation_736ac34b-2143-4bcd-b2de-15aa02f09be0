## 0.180.0
- Add `enable_ai_greeting_message` to `GetWidgetConfigResponse` proto

## 0.179.0
- Add `error_code` field to `SendStatus`

## 0.178.0
- add `EVENT_TYPE_DUPLICATE_CONVERSATION` to `EventType` enum

## 0.177.0
- Add `ai_voice_enabled` to `Configuration` and `ProductFeature` protos

## 0.176.0
- Add `skip_message_delivery` to `SendMessageRequest` proto

## 0.175.0
- Add `clicked_at` property to `UIButton` proto

## 0.174.0
- Add `widget_type` to `Widget` message and `CreateWidgetRequest` message

## 0.173.0
- Add `MakeToolCall` rpc to `ConversationService`

## 0.172.0
- Add `GetMultiConversationDetailsV2` to `ConversationService`

## 0.171.0
- Add `ui_components` field to `Message` proto

## 0.170.0
- Add annotations to `ListWidgets` to make it available as an external API

## 0.169.0
- Add `instance_id` to `CreateConversationRequest`

## 0.168.0
- Add `EVENT_TYPE_SMS_SUBSCRIBED` to `EventType` enum

## 0.167.0
- Add channel type `CONVERSATION_CHANNEL_AI_ASSISTANT` for in-platform conversations with an AI Assistant.
- Add participant type `PARTICIPANT_TYPE_AI_ASSISTANT` to represent AI Assistants in conversations.
  - This may deprecate `PARTICIPANT_TYPE_OPENAI_BOT` in the future
  - valid to appear in conversations that were not started on the channel `CONVERSATION_CHANNEL_AI_ASSISTANT`
    - example being conversations with channel `CONVERSATION_CHANNEL_WEB_CHAT` if `PARTICIPANT_TYPE_OPENAI_BOT` is deprecated
- Add `instance_id` to `Conversation` proto

## 0.166.0
- add `metadata` to `CreateWidgetConversationRequest`
- Add `metadata` field to `Conversation`proto

## 0.165.0
- add `evaluation_type` to `ListResponseEvaluationsRequest`

## 0.164.1

- fix naming `visitor` -> `visit_data`

## 0.164.0
- add `evaluated_item_id` and `conversation_id` to `EvaluateResponseRequest` proto
- add `event_id` to `ResponseEvaluation` proto

## 0.163.0

- remove `AbortAiResponder` rpc
- add `will_respond_at` to `Conversation` proto

## 0.162.0

- add `WidgetVisitor` with `visitor_id` and `visit_id` to `GetWidgetConfig` API

## 0.161.0
- add `AbortAiResponder` rpc

## 0.160.0

- add `powered_by_name`, `powered_by_website`, `show_footer_content` to `GetWidgetConfig` API

## 0.159.2

- add `subject_participant` to `ResponseEvaluation` proto

## 0.159.1

- change `summary` to `ListResponseEvaluationsResponse` to have a well defined structure

## 0.159.0

- add `namespace` to `GetWidgetConfigResponse`

## 0.158.0

- add `ListResponseEvaluations` rpc

## 0.157.0

- add `sendMessageOptions` to `sendMessageRequest`

## 0.156.0

- add `latest_relevant_activity_time` to `Conversation` proto

## 0.155.0

- add `latest_relevant_activity` to `GetMultiConversationDetailsResponse`

## 0.154.0

- add `enable_greeting_message` to `Widget` `UpdateWidgetRequest` `GetWidgetConfigResponse` `CreateWidgetRequest` proto

## 0.153.0

- add `greeting_message` to `CreateWidgetConversationRequest`
- add `greeting_message_id` to `CreateWidgetConversationResponse`

## 0.152.0

- add `custom_greeting_message` to `Widget` proto

## 0.151.0

- add `GetMultiWidget` rpc

## 0.150.0

- add `hide_mobile_cta` to `Widget` proto

## 0.149.0

- add `WhatsAppEnabled` to `Configuration` proto

## 0.148.0

- add `EVENT_TYPE_PHONE_CALL` to `EventType` enum

## 0.147.0

- add `METADATA_IDENTIFIER_WHATSAPP_TEMPLATE` to `Metadata`
- Add `MESSAGE_TYPE_TEMPLATE` to the `MessageType` enum.

## 0.146.0

- Add `RESTRICTED` to `Status` enum to indicate when a channel on a conversation is restricted in some way.

## 0.145.0

- Add `embed_history` to `Widget` proto to store the history of the widget's host sites
- This will be used in the on boarding steps to determine if the widget has been installed yet

## 0.144.2

- Add `channel`, `originated_at`, `type`, `metadata` to `Event` proto

## 0.144.1

- Add `media_url` field to `Message` proto

## 0.144.0

- Add `InjectMessage` rpc to `ConversationService`

## 0.143.0

- Add `AIConfiguration` to `Configuration` proto

## 0.142.0

- Add `subject_participant_key` to `Conversation` proto

## 0.141.1

- Add `LatestMessageSentTimeBySubjectParticipant` to `Conversation` proto

## 0.141.0

- Add WhatsApp channel to `ConversationChannel` enum

## 0.140

- Add `AIConfiguration` to hold the AI configuration details
- Add `ai_configuration` to `Conversation` proto

## 0.139.0

- Add `position` to `CreateWidgetRequest`,`GetWidgetConfigResponse` and `UpdateWidgetRequest`

## 0.138.0

- Add filter options to `ListWidgets` rpc

## 0.137.0

- Add `namespace` to `ReceiveMessageRequest` and deprecate the `partner_id` and `account_group_id` fields

## 0.136.0

- Add `assistant_name` to the chat widget.
- Add `assistant_avatar_url` to the chat widget.

## 0.135.0

- Add `widget_top_space` to `GetWidgetConfigResponse`

## 0.134.0

- Add `BOOKING_AVAILABILITY` to `MessageType`

## 0.133.0

- Add `GetMultiEvents` rpc

## 0.132.0

- Add `additional_prompt_instruction` to Widget Config rpcs
  - prompt provided by the user to be used as additional instructions for the AI assistant
- Deprecate `skip_contact_capture` from `GetWidgetConfigResponse` proto
  - skipping contact capture was not meant to be used in Web Chat client (where GetWidgetConfig rpc is used)

## 0.131.0

- Add `hydration_options` to `GetMessageTemplate` rpc

## 0.130.1

- Add admin scope to `AISystemMessageService` rpcs

## 0.130.0

- Add `AISystemMessageService` along with Upsert, Get and List rpcs and associated messages

## 0.129.0

- Add `skip_contact_capture` to `Widget` proto
- Add `skip_contact_capture` to `CreateWidget`, `GetWidgetConfig` and `UpdateWidget` rpc

## 0.128.0

- Include reason for channel not being available in `GetAvailableChannels`

## 0.127.0

- Add `business_website` to `DataSources` proto

## 0.126.0

- Add `original_content` to `Message` proto
- Add `GetMessageOptions` to `GetMultiMessagesRequest` proto

## 0.125.0

- Create `KeyValuePair` proto
- Add `metadata` field to `Message`proto

## 0.124.0

- Add `initiator_id` to `NewInboxLeadCapturedRequest` proto
- `MESSAGE_TYPE_FORM_SUBMISSION` have been deprecated

## 0.123.0

- Add `message` to `NewInboxLeadCapturedRequest` proto
- Add `MESSAGE_TYPE_FORM_SUBMISSION` to `MessageType`

## 0.122.0

- Add `lead_id` and support for `contact_id` to `CapturedLead` for `NewInboxLeadCaptured` rpc
- Deprecate `conversation_id` on `NewInboxLeadCaptured` rpc (API will create conversation for lead)

## 0.121.0

- Rename `GetForwardingEmail` rpc to `GetOwnerEmail`
- Add `EmailService` and move `GetOwnerEmail` rpc to `EmailService`

## 0.120.0

- Add `NewInboxLeadCaptured` RPC to `ConversationService`
  - This API is for leads captured outside of conversation but still relates to Inbox,
    for example, form submissions that lead in creating conversations.

## 0.119.0

- Add `instagram_messaging_enabled` field to `Configuration` and `ProductFeature` protos

## 0.118.0

- Add `GetForwardingEmail` rpc

## 0.117.0

- Add `CONVERSATION_CHANNEL_INSTAGRAM` to `ConversationChannel` types

## 0.116.0

- Add `GetConversationSummary` rpc

## 0.115.0

- Add `summary` to `DetailedConversation` proto
- Add `options` to `GetMultiConversationsRequest` proto

## 0.114.0

- add Scop `conversation:read` to `GetMultiMessages` rpc for automations usage

## 0.113.0

- Remove `DataSources` from `UpdateWidgetRequest` and `Widget` messages
  - This means it is no longer returned anywhere a `Widget` was returned (ie `GetWidget`)
- `DataSources` on `CreateWidgetRequest` has had nonfunctional/unimplemented options removed from it for clarity

## 0.112.0

- Add `accent_color` to `Widget` proto
- Add `accent_text_color` to `CreateWidget` rpc, `GetWidgetConfig` and `UpdateWidget` rpc
-

## 0.111.0

- Add `business-app` scope to APIs

## 0.110.0

- Add `SubjectParticipant` to `UpsertProductFeatureRequest`

## 0.109.0

- Add `UpsertProductFeature` rpc

## 0.108.0

- Add `chat_source_url` to `CreateWidgetConversationRequest` proto、

## 0.107.0

- Add `namespace_hierarchy` to `ParticipantKey` proto
- **BREAKING CHANGE** Rename `owner_id_path` to `namespace_hierarchy` in `Participant` proto
- Add `NamespaceDetail` to hold the namespace details in a structured way

## 0.106.0

- Add `multi_location_enabled` to `Configuration` message
- Add `PARTICIPANT_TYPE_GROUP` to the `Type` enum of `Participant` message
- Add `GLOBAL_PARTICIPANT_TYPE_GROUP` to `GlobalParticipantType` enum

## 0.105.0

**BREAKING CHANGE**: Avoid using 0.103.0 version

- Rename `last_active_channel` to `preferred_channel` in `GetAvailableChannelsForConversationResponse` proto

## 0.104.0

- Add `owner_id_path` to `Participant` proto

## 0.103.0

- Add `last_active_channel` to `GetAvailableChannelsForConversationResponse` request

## 0.102.0

- Add `field_mask` to `UpsertConfigurationRequest` request

## 0.101.0

- Add `UpdateConversation` rpc
- Add `is_open` to `Conversation` proto

## 0.100.1

- Fix `EvaluationSentiment` names

## 0.100.0

- Add `EvaluateResponse` rpc

## 0.99.0

- Add `facebook_messenger_enabled` to `Configuration` proto

## 0.98.0

- Add `text_color` to `Widget` proto
- Add `text_color` to `CreateWidget` rpc, `GetWidget` and `UpdateWidget` rpc

## 0.97.0

- add `GetMessageTemplate` rpc

## 0.96.0

- Add `created` in the `MessageTemplate` proto

## 0.95.0

- Deprecate `conversationID` in the `GetMultiParticipantsRequest` proto

## 0.94.0

- Add `data_sources` to `Widget` proto, `CreateWidget` rpc and `UpdateWidget` rpc

## 0.93.0

- add `UpdateMessageTemplate` rpc

## 0.92.0

- add `CreateMessageTemplate` rpc

## 0.91.0

- add `MessageTemplate` proto
- add `ListMessageTemplate` rpc
- add `DeleteMessageTemplate` rpc

## 0.90.0

- add `web_chat_enabled` to `Configuration` proto

## 0.89.0

- Add `welcome_message` to `CreateWidgetRequest`

## 0.88.0

- Add `Sending` status to `MessageStatus`

## 0.87.0

- Add `subject_participants_key` and `subject_particpants` to `GetMultiConversationDetails` response

## 0.86.1

- Deprecate `ConversationKey.channel`

## 0.86.0

- add scope to `CreateConversation` API

## 0.85.0

- Deprecate `inbox_pro_enabled` field in `Configuration` proto

## 0.84.0

- Add `messageID` to the `SendWidgetMessageResponse` proto

## 0.83.0

- Add `inbox_pro_enabled` field to `Configuration` proto

## 0.82.0

- Add `GetAvailableChannelsForConversation` api which returns the channels a message can be sent on for a given conversation

## 0.81.0

- Add `channel` to `SendMessageRequest` proto

## 0.80.0

- Add `channel` to `Message` proto

## 0.79.0

- Add `language_code` to `GetMultiMessagesRequest`

## 0.78.0

- Add `messageID` to the `SendMessageResponse` proto

## 0.77.0

- Add `GetWidgetConfig` api

## 0.76.0

- Add `GetWidgetConversation` api

## 0.75.0

- Add `is_enabled` and `welcome_message` to the request for `UpdateWidget` rpc and `Widget` message

## 0.74.0

- Add `SendWidgetMessage` api

## 0.73.0

- Add `SendStatus` to the `Message` proto

## 0.72.0

- Add `GetMultiWidgetMessages` rpc

## 0.71.1

- Add `GLOBAL_PARTICIPANT_TYPE_ANONYMOUS` to `GlobalParticipantType`

## 0.71.0

- Add `google_business_messages_enabled` and `sms_enabled` fields to `Configuration` proto

## 0.70.0

- Move `CreateWidget`, `GetWidget`, `UpdateWidget`, `DeleteWidget` to `Conversation` service

## 0.69.0

- Add `PARTICIPANT_TYPE_ANONYMOUS` participant type.

## 0.68.1

- Add missing `CONVERSATION_CHANNEL_WEB_CHAT` channel enum

## 0.68.0

- Add `CreateWidgetConversation` api

## 0.67.0

- Add `Widget` proto
- Add `WidgetService` and `CreateWidget`, `GetWidget`, `UpdateWidget`, `DeleteWidget` rpcs

## 0.66.0

- Add `file_size` to `Media`

## 0.65.0

- Add `conversation_view_ids` to `Conversation` proto

## 0.64.0

- Add `CONVERSATION_CHANNEL_GOOGLE_BUSINESS_COMMUNICATIONS` to `ConversationChannel` types

## 0.63.0

- Add `Media` to `SendMessageRequest`

## 0.62.0

- Deprecate `channel` from `Participant` proto

## 0.61.0

- Deprecate `channels` from `ParticipantKey` proto
- Deprecate `location` from `ParticipantKey` proto
- Deprecate `participants` from `GetParticipantsByKeyResponse` proto
- Add `participant` to `GetParticipantsByKeyResponse` proto

## 0.60.0

- Remove `ViewType` from `ConversationViews`

## 0.59.0

- Rename `ListParticipantConversationViewsRequest` to `GetConversationViewsRequest`
- Refactor `ConversationView` to contain a list of views
- Add participantID in the `AddConversationToConversationView`endpoint
- Add participantID in the `RemoveConversationToConversationView`endpoint

## 0.58.0

- Add `RemoveConversationFromConversationView` api

## 0.57.0

- Add email channel

## 0.56.0

- Add `AddConversationToConversationView` api

## 0.55.0

- Add `ConversationView` proto
- Add `ListParticipantConversationViews` api

## 0.54.0

- Add `GetMultiConfiguration` api

## 0.53.0

- Add `GetConfiguration` `UpsertConfiguration` apis

## 0.52.0

- Rename `RetrieveConversationByKey` to `GetConversationByKey`
- Add `conversation:read` scope to `GetConversationByKey`
- Add `conversation` scope to `SendMessage`

## 0.51.0

- Add `RetrieveConversationByKey` rpc to `ConversationService`
- Add `SubjectParticipant` and `ConversationKey` to `conversation.proto` file
- Move `GlobalParticipantType` to `common.proto` file

## 0.50.0

- Add `CONVERSATION_CHANNEL_OPENAI` to `ConversationChannel`

## 0.49.0

- Add `PARTICIPANT_TYPE_OPENAI_BOT` and `GLOBAL_PARTICIPANT_TYPE_OPENAI_BOT` to `Participant` proto

## 0.48.0

- Add `MESSAGE_TYPE_SYSTEM` to `MessageType`

## 0.47.0

- Add `origin_location` to `SendMessageRequest`
- Deprecate `location` from `Participant` proto

## 0.46.0

- Add `status` to `SetLastSeenRequest`
- Add `participant` to `SetLastSeenResponse`
- Remove `last_seen` from `SetLastSeenResponse`
- Add `last_seen_by_participant` in the `Conversation` proto

## 0.45.0

- Add scopes to `GetMessage` and `GetMultiParticipants`

## 0.44.0

- Add `profile_image_url` to `Participant`

## 0.43.1

- Rename `ParticipantType` to `GlobalParticipantType`

## 0.43.0

- Add `GetParticipantsByKey` rpc
  - Add `GetParticipantsByKeyRequest` and `GetParticipantsByKeyResponse`

## 0.42.0

- Add `Participant` to `SetLastSeenRequest`

## 0.41.0

- Deprecate `location` and `account_group_id` fields in `LookupConversationsRequest`

## 0.40.0

- Add `is_participant_internal_info_deleted` to `Participant`

## 0.39.0

- Scope endpoints to a conversation

## 0.38.0

- Add `SetLastSeen` endpoint

## 0.37.0

- Add `is_subject_participant` to `Participant`

## 0.36.0

- Add `AddMultiParticipants` rpc
  - Add `AddMultiParticipantsRequest` and `AddMultiParticipantsResponse` messages

## 0.35.0

- Add `first_line_address`, `second_line_address`, `city`, `state`, `country` and `zip_code` to the `Participant` message.

## 0.34.0

- Add map for `GetMultiConversationMessageCountResponse`
  - deprecate `ConversationMessageCount` and `message_counts` fields

## 0.33.0

- Add `GetMultiConversationMessageCount` rpc
  - Add `GetMultiConversationMessageCountRequest`, `GetMultiConversationMessageCountResponse` and `ConversationMessageCount`

## 0.32.0

- Add `SearchConversations` rpc
  - Add `SearchConversationsRequest` and `SearchConversationsResponse` messages for that rpc.

## 0.31.1

- Add comment to `SendMessageRequest` recipient field. The recipient field is not used for now.

## 0.31.0

- Add `PARTICIPANT_TYPE_VENDOR` to `PARTICIPANT_TYPE` enum

## 0.30.0

- Add `PARTICIPANT_TYPE_DIGITAL_AGENT` to `PARTICIPANT_TYPE` enum

## 0.29.0

- Add `origin_location` and `origin_location_external_id` filters to `LookupConversationRequest`
- Add `PLATFORM_LOCATION_SALES_CENTER` to `Location` enum

## 0.28.0

- Add `PARTICIPANT_TYPE_IAM_TEMPORARY_SUBJECT` to `ParticipantType` enum
- Add `PLATFORM_LOCATION_TASK_MANAGER` and `PLATFORM_LOCATION_PROPOSAL_BUILDER` to `Location` enum
- Add `origin_location` and `origin_location_external_id` to `CreateConversationRequest` and `Conversation`

## 0.27.0

- Include `Media` in the `ReceiveMessageRequest` and `Message` proto.
- Include `MESSAGE_TYPE_MEDIA` in the `Message` proto.

## 0.26.0

- Include `LatestMsgSentTime` in the `Conversation` proto.

## 0.25.1

- Include `created` and `updated` in the `CreateConversation` endpoint.

## 0.25.0

- Add `participant_ids` to the `Conversation` message.

## 0.24.0

- Add the `GetMultiParticipants` endpoint in the `ConversationService`

## 0.23.1

- Change the `GetMultiConversationDetails` to have a list instead of a map.

## 0.23.0

- Add the `GetMultiConversationDetails` endpoint in the `ConversationService`
- Add `email` and `phone_number` to the `Participant` message.

## 0.22.0

- Include `lastSeen` in the `CreateMultiMessages` endpoint.

## 0.21.0

- Include `ExternalConversationID` in the `CreateConversation` endpoint.
- Include `ExternalConversationID` in the `LookupConversations` endpoint.
- Add `CreateMultiMessages` endpoint.

## 0.20.0

- Include `ParticipantType` in the `Participant`

## 0.19.0

- Add new rpc service to `GetMultiMessages`

## 0.18.0

- Make `internal_participant_id` in the `LookupConversationsFilters` a list

## 0.17.0

- Include `Channel` in the `LookupConversationsFilters` and rename `user_id` to be consistent across the definitions
- Include `Participants` to the `LookupConversations` response.

## 0.16.0

- Split the parentID into `partner_id` and `account_group_id` in the `Participant` proto.

## 0.15.0

- Add metadata to the `SendMessage` endpoint.

## 0.14.0

- Adjust `SendMessage` endpoint by returning the workflowID.

## 0.13.0

- Remove `title` from `Message`;
- Replace `int64` by `int32` in the `page_size` attribute;

## 0.12.0

- Adjust `SendMessage` endpoint by replacing IDs with the `Participant` proto models.

## 0.11.0

- Add `partner_id`, `account_group_id` and `metadata` in the `ReceiveMessageRequest`

## 0.10.0

- Add `location` in the `ReceiveMessageRequest`

## 0.9.1

- Add `type` in the `ReceiveMessageRequest`

## 0.9.0

- Add new rpc service to `ReceiveMessage`

## 0.8.0

- Adjust `SendMessage` endpoint by replacing IDs with the proto models.

## 0.7.0

- Add `Participant` proto.
- Add `CreateConversation` and `DeleteConversation` endpoints.

## 0.6.0

- Remove `CreateMessage` and add `SendMessage` endpoint.

## 0.5.0

- Add `LookupConversations` endpoint and request and response messages.
- Add `conversation.proto` file for the definition of the conversation mesage.

## 0.4.0

- Create an updated field in the `ParticipantMessageStatus` entity.

## 0.3.0

- Update sender field name of `Message` entity to participant_id.
- Create a deleted field in the `Message` entity.

## 0.2.0

- Update status field name of `ParticipantMessageStatus` entity.

## 0.1.0

- Create the conversation service with the endpoints `CreateMessage`, `GetMessage`, `ListMessages` and `DeleteMessage`
