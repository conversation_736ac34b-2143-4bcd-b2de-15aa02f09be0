syntax = "proto3";

package conversation.v1;

option go_package = "github.com/vendasta/generated-protos-go/conversation/v1;conversation_v1";
option java_outer_classname = "WidgetProto";
option java_package = "com.vendasta.conversation.v1.generated";

import "google/protobuf/timestamp.proto";
// (-- TODO(WARP-1442): add openapiv2 annotations for generated docs --)
//import "protoc-gen-openapiv2/options/annotations.proto";

enum WidgetPosition {
  // Unspecified widget position
  WIDGET_POSITION_UNSPECIFIED = 0;
  // Widget position on the right side of the page
  WIDGET_POSITION_RIGHT = 1;
  // Widget position on the left side of the page
  WIDGET_POSITION_LEFT = 2;
}

message Widget {
  // (-- TODO(WARP-1442): add openapiv2 annotations for generated docs --)
  /**
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Widget"
      description: "A conversation widget object"
    }
  };
  */
  // The id of the widget
  string widget_id = 1;
  // (-- TODO(WARP-1442): replace above with openapiv2 annotations for generated docs --)
  /**
  string widget_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    title: "Conversation widget"
    description: "Unique widget id of the conversation widget"
    format: "uuid"
    example: "\"2438ac3c-37eb-4902-adef-ed16b4431030\""
    }];
  */
  // Name of the widget, e.g. "Mountain Media Support"
  string name = 2;
  // The id of the namespace, e.g. "ABC", "AG-123"
  string namespace = 3;
  // A hex color code for the widget, e.g. "#FF0000"
  string color = 4;
  // A list of urls that the widget is allowed to be embedded on
  repeated string allowed_urls = 5;
  // The position of the widget on the page
  WidgetPosition position = 6;
  // The timestamp when the widget was created
  google.protobuf.Timestamp created = 7;
  // The timestamp when the widget was updated
  google.protobuf.Timestamp updated = 8;
  // Whether the webchat for the widget config is enabled
  bool is_enabled = 9;
  // The welcome message for the webchat for the widget config
  string welcome_message = 10;
  reserved 11;
  // The primary text color for the widget
  string text_color = 12;
  // Accent color for the widget secondary elements
  string accent_color = 13;
  // Accent text color for the widget secondary elements
  string accent_text_color = 14;
  // Whether the widget has contact capturing enabled
  bool skip_contact_capture = 15;
  // The prompt provided by a user to be used as additional instructions for the web chat AI assistant
  string additional_prompt_instructions = 16;
  // The name of the AI assistant
  string assistant_name = 17;
  // The avatar URL of the AI assistant
  string assistant_avatar_url = 18;
  // The history of widget installations (typically the domain/hostname of the referer URL the widget is embedded on)
  repeated string embed_history = 19;
  // Hide the popup CTA on mobile screens
  bool hide_mobile_cta = 20;
  // The custom greeting message that shows up as first message
  string custom_greeting_message = 21;
  // Enable the greeting message from AI assistant using the custom greeting message
  bool enable_greeting_message = 22;
  // The type of the widget. Examples are: 'inbox', 'my-listings', 'snapshot', etc.
  string widget_type = 23;
}

message DataSources {
  // Will be used to determine if a business profile data source should be associated
  bool business_profile = 1;
  reserved 2,3;
  // Will be used to determine if a business website data source should be associated
  // This will also depend on the business having their website field filled
  bool business_website = 4;
}
