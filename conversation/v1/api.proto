syntax = "proto3";

package conversation.v1;

option go_package = "github.com/vendasta/generated-protos-go/conversation/v1;conversation_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.conversation.v1.generated";

import "google/protobuf/empty.proto";
import "conversation/v1/message.proto";
import "conversation/v1/conversation.proto";
import "conversation/v1/participant.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/annotations.proto";
import "conversation/v1/configuration.proto";
import "conversation/v1/conversation_view.proto";
import "conversation/v1/common.proto";
import "conversation/v1/widget.proto";
import "conversation/v1/evaluation.proto";
import "vendasta_types/field_mask.proto";
import "conversation/v1/message_template.proto";
import "conversation/v1/aisystemmessage.proto";
import "conversation/v1/event.proto";
import "google/api/client.proto";
import "google/api/annotations.proto";
// (-- TODO(WARP-1442): add google api visibility for generated docs --)
//import "google/api/visibility.proto";

message PagedRequestOptions {
  // cursor can be passed to retrieve the next page of results keyed by the cursor
  string cursor = 1;
  // page_size specifies the number of items to return in the next page
  int32 page_size = 2;
}

message PagedResponseMetadata {
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 1;
  // Whether or not more results exist
  bool has_more = 2;
  // Total number of results
  int64 total_results = 3;
}

message Metadata {
  enum Identifier {
    METADATA_IDENTIFIER_UNDEFINED = 0;
    METADATA_IDENTIFIER_RECIPIENT = 1;
    METADATA_IDENTIFIER_SENDER = 2;
    METADATA_IDENTIFIER_CONVERSATION = 3;
    METADATA_IDENTIFIER_WHATSAPP_TEMPLATE = 4;
  }

  Identifier identifier = 1;
  map <string, string> data = 2;
}


// *******************
// Requests
// *******************
message SendMessageRequest {
  // sender (IAM user) - Belongs to the Conversation µs
  Participant sender = 1;
  // (NOT USED FOR NOW) recipient (IAM user or Contact) - Belongs to the Conversation µs
  Participant recipient = 2;
  // conversation ID the message will be sent to - Belongs to the Conversation µs
  string conversation_id = 3;
  // message type
  MessageType type = 4;
  // message body
  string body = 5;
  // metadata can represents any kind of value
  repeated Metadata metadata = 6;
  // message origin location
  PlatformLocation origin_location = 7;
  // message media content
  repeated Media media = 8;
  // message channel is the channel that the message should be sent on
  ConversationChannel channel = 9;
  SendMessageOptions options = 10;
}

message SendMessageOptions {
  //option to run sendMessage synchronously
  bool run_sync = 1;
  // option to skip message delivery if the message channel is a external provider
  bool skip_message_delivery = 2;
}

message Media {
  // media content type is a two part identifier for file types (e.g. image/jpeg)
  string media_content_type = 1;
  // media location path on a GCP bucket
  string media_location_path = 2;
  // media file name
  string media_file_name = 3;
  // size of file in bytes
  int64 file_size = 4;
  // media url is the url of the media content
  string media_url = 5;
}

message GetMessageOptions {
  bool include_original_content = 1;
}

message GetMessageRequest {
  string message_id = 1;
  GetMessageOptions options = 2;
}

message DeleteMessageRequest {
  string message_id = 1;
}

message ListMessagesRequest {
  message ListMessagesFilters {
    string conversation_id = 1;
  }
  ListMessagesFilters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message UpdateMessageStatusRequest {
  string message_id = 1;
  string participant_id = 2;
  MessageStatus status = 3;
}

message LookupConversationsRequest {
  message LookupConversationsFilters {
    repeated string internal_participant_id = 1; //the internal participant id (IAM or Contact)
    // Deprecated: use origin_location instead
    PlatformLocation location = 2 [deprecated=true];
    ConversationChannel channel = 3;
    // Deprecated
    string account_group_id = 4 [deprecated=true];
    string external_conversation_id = 5;
    PlatformLocation origin_location = 6;
    string origin_location_external_id = 7;
  }
  LookupConversationsFilters filters = 1;
  PagedRequestOptions paging_options = 2;
}

message GetConversationByKeyRequest {
  ConversationKey conversation_key = 1;
}

message CreateConversationRequest {
  repeated Participant participants = 1;
  ConversationChannel channel = 2;
  string external_conversation_id = 3;
  //temporary created/updated field to run migration
  google.protobuf.Timestamp created = 4;
  google.protobuf.Timestamp updated = 5;
  // The location that the conversation originated from
  PlatformLocation origin_location = 6;
  string origin_location_external_id = 7;
  // Setting this to a UUID will allow multiple conversations to exist with the same subject participants.
  // In most cases this should be empty. Only set if you know what you're doing.
  string instance_id = 8;
}

message UpdateConversationRequest {
  Conversation conversation = 1;
  // The Field Mask indicate what fields should actually be updated.
  vendastatypes.FieldMask field_mask = 2;
}

message UpdateConversationResponse {
  Conversation conversation = 1;
}

message DeleteConversationRequest {
  string conversation_id = 1;
}

message ReceiveMessageRequest {
  // sender ID could from an internal or external provider
  string sender_id = 1;
  // recipient ID sender ID could from an internal or external provider
  string recipient_id = 2;
  // conversation ID the message will be sent to and could from an internal or external provider
  string conversation_id = 3;
  // message ID the message will be sent to and could from an internal or external provider
  string message_id = 4;
  // body is the content of the message
  string body = 5;
  // channel represents the origin from this message that could be from an internal or external provider
  ConversationChannel channel = 6;
  // message type
  MessageType type = 7;
  // platform location represents the app or location from the message that belongs
  PlatformLocation location = 8;
  // partner ID
  //
  // Deprecated: use namespace instead
  string partner_id = 9;
  // account group ID
  //
  // Deprecated: use namespace instead
  string account_group_id = 10;
  // metadata can represents any kind of value
  map <string, string> metadata = 11;
  // media is the media content of the message
  repeated string media = 12;
  // namespace is the namespace of the conversation where the participant_type the type of the participant that owns the
  // conversation, and internal_participant_id is the entity that owns the conversation
  NamespaceDetail namespace = 13;
}

message GetMultiMessagesRequest {
  // message IDs
  repeated string message_ids = 1;
  //Conversation ID
  string conversation_id = 2;
  // Code to request OpenAI translation. Provide code in ISO 639-1 format. Leave empty to skip translation.
  string language_code = 3;
}

message CreateMultiMessagesRequest {
  // messages
  repeated Message messages = 1;
  //temporary field to set in the conversation
  google.protobuf.Timestamp last_seen = 2;
}

message GetMultiConversationDetailsRequest {
  repeated string conversation_ids = 1;
  // options the requester wants to hydrate on the response
  vendastatypes.FieldMask options = 2;
}

message GetMultiConversationDetailsV2Request {
  // the IDs of the conversations to get details for
  repeated string conversation_ids = 1;
  // The fields to exclude from the response. The entire response will be filled if this is empty.
  vendastatypes.FieldMask exclusions = 2;
}

message GetMultiParticipantsRequest {
  // participant IDs
  repeated string participant_ids = 1;
  // Deprecated
  string conversation_id =2 [deprecated=true];
}

message SetLastSeenRequest {
  // LastSeenStatus
  enum Status {
    SET_LAST_SEEN_REQUEST_STATUS_READ = 0;
    SET_LAST_SEEN_REQUEST_STATUS_UNREAD = 1;
  }

  // conversation ID
  string conversation_id = 1;
  Participant participant = 2;
  Status status = 3;
}

message GetParticipantsByKeyRequest {
  // participant
  ParticipantKey participant_key = 1;
}

message InjectMessageRequest {
  // message ID could from an internal or external provider
  string message_id = 1;
  // body is the content of the message
  string body = 2;
  // channel represents the origin from this message that could be from an internal or external provider
  ConversationChannel channel = 3;
  // message type
  MessageType type = 4;
  // platform location represents the app or location from the message that belongs
  PlatformLocation location =  5;
  // metadata can represents any kind of value
  map <string, string> metadata = 6;
  // media is the media content of the message
  repeated Media media = 7;
  // namespace is the namespace of the conversation where the participant_type the type of the participant that owns the
  // conversation, and internal_participant_id is the entity that owns the conversation
  NamespaceDetail namespace = 8;
  // source of message that we're injecting into a conversation (e.g. SMS, Inbox, Email, etc...)
  string source_name = 9;
  // The original creation time of the message
  google.protobuf.Timestamp created = 10;
}

// *******************
// Responses
// *******************
message ListMessagesResponse {
  repeated Message messages = 1;
  // Metadata about the paging
  PagedResponseMetadata paging_metadata = 2;
}

message LookupConversationsResponse {
  message Conversations {
    Conversation conversation = 1;
    repeated Participant participants = 2;
  }
  repeated Conversations conversations = 1;
  PagedResponseMetadata paging_metadata = 2;
}

message GetConversationByKeyResponse {
  Conversation conversation = 1;
}

message CreateConversationResponse {
  Conversation conversation = 1;
  repeated Participant participants = 2;
}

message SendMessageResponse {
  // Deprecated: use message_id instead
  string workflow_id = 1 [deprecated=true];
  // The unique identifier of the message
  string message_id = 2;
}

message GetMultiMessagesResponse {
  // list of messages
  repeated Message messages = 1;
}

message GetMultiConversationDetailsResponse {
  message DetailedConversation {
    Conversation conversation = 1;
    // Deprecated: use latest_relevant_activity instead
    Message latest_message = 2 [deprecated = true];
    repeated Participant participants = 3;
    string summary = 4;
    oneof latest_relevant_activity {
      Event event = 5;
      Message message = 6;
    }
  }
  repeated DetailedConversation conversations = 1;
}

message GetMultiConversationDetailsV2Response {
  message DetailedConversation {
    Conversation conversation = 1;
    repeated Participant participants = 2;
    string summary = 3;
    oneof latest_relevant_activity {
      Event event = 4;
      Message message = 5;
    }
  }
  repeated DetailedConversation conversations = 1;
}

message GetMultiParticipantsResponse {
  // list of participants
  repeated Participant participants = 2;
}

message SearchConversationsRequest {
  string partner_id = 1;
  string account_group_id = 2;
  PlatformLocation location = 3;
  string search_term = 4;
  PagedRequestOptions paging_options = 5;
}

message SearchConversationsResponse {
  message DetailedConversation {
    Conversation conversation = 1;
    Message latest_message = 2;
    repeated Participant participants = 3;
  }
  repeated DetailedConversation conversations = 1;
  PagedResponseMetadata paging_metadata = 2;
}

message GetMultiConversationMessageCountRequest {
  repeated string conversation_ids = 1;
}

// Deprecated
message ConversationMessageCount {
  option deprecated = true;
  string conversation_id = 1;
  int64 count = 2;
}

message GetMultiConversationMessageCountResponse {
  // Deprecated: use counts instead
  repeated ConversationMessageCount message_counts = 1 [deprecated=true];

  map<string, int64> counts = 2;
}

message AddMultiParticipantsRequest {
  string conversation_id = 1;
  repeated Participant participants = 2;
}

message AddMultiParticipantsResponse {
  repeated Participant participants = 1;
}

message SetLastSeenResponse {
  Participant participant = 1;
}

message GetParticipantsByKeyResponse {
  // list of participants
  //Deprecated: use participant instead
  repeated Participant participants = 1 [deprecated=true];
  Participant participant = 2;
}

// UpsertConfigurationRequest is the request for creating or updating an inbox configuration for an org
message UpsertConfigurationRequest {
  // The configuration to be updated
  Configuration configuration = 1;

  // The Field Mask indicate what fields should actually be updated.
  vendastatypes.FieldMask field_mask = 2;
}

// UpsertConfigurationResponse is the response from creating or updating a a configuration
message UpsertConfigurationResponse {
  // The updated configuration
  Configuration configuration = 1;
}

// UpsertProductFeatureRequest is the request for updating or creating the feature configuration for inbox
message UpsertProductFeatureRequest {
  // The product features to be updated
  ProductFeature product_feature = 1;

  // The Field Mask indicate what fields should actually be updated.
  vendastatypes.FieldMask field_mask = 2;

  // The subject participant, whose product feature is being updated
  SubjectParticipant subject_participant = 3;
}

// UpsertProductFeatureResponse is the response from updating or creating product feature availability
message UpsertProductFeatureResponse {
  // The product feature availability
  ProductFeature product_feature = 1;
}

// GetConfigurationRequest is the request for a configuration
message GetConfigurationRequest {
  // The subject participant, whose configuration is being requested
  SubjectParticipant subject_participant = 1;
}

// GetConfigurationResponse will return a configuration for a given subject participant
message GetConfigurationResponse {
  // The configuration for a subject participant
  Configuration configuration = 1;
}

// GetMultiConfigurationRequest is the request for getting multiple configurations
message GetMultiConfigurationRequest {
  //  A list of subject participants, whose configurations are being requested
  repeated SubjectParticipant subject_participants = 1;
}

// GetMultiConfigurationResponse will return multiple configurations for a given subject participants
message GetMultiConfigurationResponse {
  // The configurations for a list of subject participants
  repeated Configuration configurations = 1;
}

// GetConversationViewsRequest is the request for getting the ConversationView for a participant
message GetConversationViewsRequest {
  string participant_id = 1;
}

// GetConversationViewsResponse will return participant conversation views
message GetConversationViewsResponse {
  repeated View views = 1;
}

// AddConversationToConversationViewRequest is the request for adding conversation to Conversation view
message AddConversationToConversationViewRequest {
  string view_id = 1;
  string conversation_id = 2;
  string participant_id = 3;
}

// RemoveConversationFromConversationViewRequest is the request for removing conversation from Conversation view
message RemoveConversationFromConversationViewRequest {
  string view_id = 1;
  string conversation_id = 2;
  string participant_id = 3;
}

message CreateWidgetConversationRequest {
  // The widget id
  string widget_id = 1;
  // The page that the conversation is start on
  string chat_source_url = 2;
  // The greeting message displayed to the user
  string greeting_message = 3;
  // The metadata passed in by the widget owner
  repeated KeyValuePair metadata = 4;
}

message CreateWidgetConversationResponse {
  // The widget_participant_token containing data about the widget user
  string widget_participant_token = 1;
  // Conversation details
  Conversation conversation = 2;
  // The message id for the greeting message
  string greeting_message_id = 3;
}

message SendWidgetMessageRequest {
  // message type
  MessageType type = 1;
  // message body
  string body = 2;
  // metadata can represent any kind of value
  repeated Metadata metadata = 3;
  // message origin location
  PlatformLocation location = 4;
}

message SendWidgetMessageResponse {
  // The unique identifier of the message
  string message_id = 1;
}

message GetAvailableChannelsForConversationRequest {
  // The conversation id
  string conversation_id = 1;
}

enum Status {
  ERROR = 0;
  WARNING = 1;
  INFO = 2;
  RESTRICTED = 3;
}

message ChannelStatus {
  Status status = 1;
  string i18n_key = 2;
}

message ChannelAvailability {
  ConversationChannel channel = 1;
  bool is_available = 2;
  repeated ChannelStatus statuses = 3;
}

message GetAvailableChannelsForConversationResponse {
  // Deprecated: The list of channels messages can be sent on
  repeated ConversationChannel channels = 1;
  ConversationChannel preferred_channel = 2;
  repeated ChannelAvailability channel_availabilities = 3;
}

message EvaluateResponseRequest {
  // Deprecated: use evaluated_message_id instead
  string message_id = 1 [deprecated = true];
  // The sentiment of the evaluation of the response
  EvaluationSentiment sentiment = 2;
  // A comment on what was helpful or not helpful about the response
  string comment = 3;
  // The id of the event or message that was evaluated
  oneof evaluated_item_id {
    string evaluated_event_id = 4;
    string evaluated_message_id = 5;
  }
  // The conversation id
  string conversation_id = 6;
}

enum EvaluateResponseType {
  EVALUATION_RESPONSE_TYPE_UNSPECIFIED = 0;
  EVALUATION_RESPONSE_TYPE_EVENT = 1;
  EVALUATION_RESPONSE_TYPE_MESSAGE = 2;
}

message ListResponseEvaluationsRequest {
  // Whether to summarize the response evaluations
  bool summarize = 1;
  // The subject participant whose response evaluations should be retrieved
  SubjectParticipant subject_participant = 2;
  // Paging options for the request
  PagedRequestOptions paging_options = 3;
  // Filter evaluations created after this timestamp (inclusive)
  google.protobuf.Timestamp created_after = 4;
  // Filter evaluations created before this timestamp (inclusive)
  google.protobuf.Timestamp created_before = 5;
  // Filter evaluations by type
  repeated EvaluateResponseType evaluation_type = 6;
}

message ListResponseEvaluationsResponse {
  // The list of response evaluations
  repeated ResponseEvaluation evaluations = 1;
  // The summary of the response evaluations, only returned if `summarize` is true in the request
  // The summary is cached based on the `subject_participant` and `created_after` and `created_before` filters for 12 hours
  // The summary is not specific to the `paging_options`, it considers as many evaluations as possible within the time range
  message Summary {
    message Theme {
      // The name of the theme
      string name = 1;
      // The description of the theme
      string description = 2;
      // Examples of the theme, these are real examples of responses that are categorized under the theme
      repeated string examples = 3;
    }
    // Feedback themes that are used to categorize the response evaluations
    repeated Theme feedback_themes = 1;
  }
  // The summary of the response evaluations generated by AI
  Summary summary = 2;
  // Paging metadata for the request
  PagedResponseMetadata paging_metadata = 3;
}

message CreateWidgetRequest {
// The widget configuration to be created
  string name = 1;
  string namespace = 2;
  string color = 3;
  repeated string allowed_urls = 4;
  // The welcome message for the webchat for the widget config
  string welcome_message = 5;
  // The sources for the data to be used by the widget AI assistant
  DataSources data_sources = 6;
  // The text color for the widget
  string text_color = 7;
  // Accent color for the widget secondary elements
  string accent_color = 8;
  // Accent text color for the widget secondary elements
  string accent_text_color = 9;
  // Whether the widget has contact capturing enabled
  bool skip_contact_capture = 10;
  // The prompt provided by a user to be used as additional instructions for the web chat AI assistant
  string additional_prompt_instructions = 11;
  // assistant_name is the name of the AI assistant
  string assistant_name = 12;
  // assistant_avatar_url is the url of the AI assistant avatar
  string assistant_avatar_url = 13;
  // The position of the widget on the page
  WidgetPosition position = 14;
  // Hide the popup CTA on mobile screens
  bool hide_mobile_cta = 15;
  // The custom greeting message that shows up as first message
  string custom_greeting_message = 16;
  // Enable the greeting message from AI assistant
  bool enable_greeting_message = 17;
  // The type of the widget. Examples are: 'inbox', 'my-listings', 'snapshot', etc.
  string widget_type = 18;
}

message CreateWidgetResponse{
  // The created widget
  Widget widget = 1;
}

message GetWidgetRequest {
  // The id of the widget to be retrieved
  string widget_id = 1;
}

message GetWidgetResponse{
  // The retrieved widget
  Widget widget = 1;
}

message GetMultiWidgetRequest {
  repeated string widget_ids = 1;
}

message GetMultiWidgetResponse {
  repeated Widget widgets = 1;
}

// Cookie "session" data for tracking a visitor to a website hosting the widget
message WidgetVisit {
  // The id of the visitor for tracking (long-lived user id of a visitor to a website hosting the widget)
  string visitor_id = 1;
  // The id of the visit for tracking (short-lived "session" id of a visitor to a website hosting the widget)
  string visit_id = 2;
}

message GetWidgetConfigRequest {
  // The id of the widget to be retrieved
  string widget_id = 1;
  // The visitor visit data for tracking a visitor to a website hosting the widget
  WidgetVisit visit_data = 2;
}

message GetWidgetConfigResponse {
  // Name of the widget, e.g. "Mountain Media Support"
  string name = 1;
  // A hex color code for the widget, e.g. "#FF0000"
  string color = 2;
  // Whether the webchat for the widget config is enabled
  bool is_enabled = 3;
  // The welcome message for the webchat for the widget config
  string welcome_message = 4;
  // The text color for the widget
  string text_color = 5;
  // Accent color for the widget secondary elements
  string accent_color = 6;
  // Accent text color for the widget secondary elements
  string accent_text_color = 7;
  // Deprecated: not meant to be used in the Web Chat client
  bool skip_contact_capture = 8 [deprecated=true];
  // Height between the widget top and the screen in pixels
  int64 widget_top_space = 9;
  // assistant_name is the name of the AI assistant
  string assistant_name = 10;
  // assistant_avatar_url is the url of the AI assistant avatar
  string assistant_avatar_url = 11;
  // The position of the widget on the page
  WidgetPosition position = 12;
  // Hide the popup CTA on mobile screens
  bool hide_mobile_cta = 13;
  // The custom greeting message that shows up as first message
  string custom_greeting_message = 14;
  // Enable the greeting message from AI assistant
  bool enable_greeting_message = 15;
  // The namespace of the widget e.g. "ABC", "AG-123"
  string namespace = 16;
  // The company name for the widget footer (either current namespace or parent namespace)
  string powered_by_name = 17;
  // The company website for the widget footer (either current namespace or parent namespace)
  string powered_by_website = 18;
  // Show the footer content on the widget (configured on the partner business app configuration)
  bool show_footer_content = 19;
  enum GreetingMessageType {
    GREETING_MESSAGE_UNDEFINED = 0;
    GREETING_MESSAGE_NONE = 1;
    GREETING_MESSAGE_CUSTOM = 2;
    GREETING_MESSAGE_DEFAULT= 3;
    GREETING_MESSAGE_AI = 4;
  }
  GreetingMessageType greeting_message_type = 20;
}

message UpdateWidgetRequest {
  // The widget information to be updated
  string widget_id = 1;
  // Name of the widget, e.g. "Mountain Media Support"
  string name = 2;
  // A hex color code for the widget, e.g. "#FF0000"
  string color = 3;
  // A list of urls that the widget is allowed to be embedded on
  repeated string allowed_urls = 4;
  // The welcome message for the webchat for the widget config
  string welcome_message = 5;
  // Whether the webchat for the widget config is enabled
  bool is_enabled = 6;
  // The text color for the widget
  string text_color = 9;
  // Accent color for the widget secondary elements
  string accent_color = 10;
  // Accent text color for the widget secondary elements
  string accent_text_color = 11;
  // Whether the widget has contact capturing enabled
  bool skip_contact_capture = 12;
  // The prompt provided by a user to be used as additional instructions for the web chat AI assistant
  string additional_prompt_instructions = 13;
  // assistant_name is the name of the AI assistant
  string assistant_name = 14;
  // assistant_avatar_url is the url of the AI assistant avatar
  string assistant_avatar_url = 15;
  // The position of the widget on the page
  WidgetPosition position = 16;
  // Hide the popup CTA on mobile screens
  bool hide_mobile_cta = 17;
  // The custom greeting message that shows up as first message
  string custom_greeting_message = 18;
  // Enable the greeting message from AI assistant
  bool enable_greeting_message = 19;

  // The Field Mask indicate what fields should actually be updated.
  vendastatypes.FieldMask field_mask = 7;
  reserved 8;
}

message UpdateWidgetResponse{
  // The updated widget
  Widget widget = 1;
}

message DeleteWidgetRequest{
  // The id of the widget to be deleted
  string widget_id = 1;
}

message ListWidgetsRequest {
  // The namespace of the widget e.g. "ABC", "AG-123"
  // Use either namespace or options.namespaces
  string namespace = 1;
  // paging options
  PagedRequestOptions paging_options = 2;
  message ListWidgetsFilters{
    // The namespaces of the widgets e.g. "ABC", "AG-123"
    // Use either namespace or options.namespaces
    repeated string namespaces = 1;
    // The types of the widget e.g. "inbox-pro", "my-listings"
    repeated string widget_types = 2;
  }
  ListWidgetsFilters options = 3;
}

message ListWidgetsResponse {
  // The list of widgets
  repeated Widget widgets = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message GetMultiWidgetMessagesRequest {
  // The ids of the messages to be retrieved
  repeated string message_ids = 1;
}

message GetMultiWidgetMessagesResponse {
  // The list of messages
  repeated Message messages = 1;
}

message GetWidgetConversationResponse {
  // Conversation details
  Conversation conversation = 1;
}

message ListMessageTemplateRequest {
  // The subject participant, whose message templates is being requested
  SubjectParticipant subject_participant = 1;
  // paging options
  PagedRequestOptions paging_options = 2;
}

message ListMessageTemplateResponse {
  // All message templates for a given SubjectParticipant
  repeated MessageTemplate templates = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message CreateMessageTemplateRequest {
  MessageTemplate template = 1;
  SubjectParticipant subject_participant = 2;
}

message CreateMessageTemplateResponse {
  MessageTemplate template = 1;
}

message UpdateMessageTemplateRequest {
  MessageTemplate template = 1;
  // The Field Mask indicate what fields should actually be updated.
  vendastatypes.FieldMask field_mask = 2;
}

message UpdateMessageTemplateResponse {
  MessageTemplate template = 1;
}

message DeleteMessageTemplateRequest {
  // The template to be deleted
  string template_id = 1;
}

message GetMessageTemplateRequest {
  message HydrationOptions {
    message HydrationEntity {
      enum EntityType {
        ENTITY_TYPE_UNDEFINED = 0;
        ENTITY_TYPE_PARTNER = 1;
        ENTITY_TYPE_ACCOUNT_GROUP = 2;
        ENTITY_TYPE_CONTACT = 3;
      }

      // The entity type used to specify the type of entity id
      EntityType entity_type = 1;
      // The id used to get hydration information
      // Such as, partner id used to get partner information to hydrate partner related dynamic fields in message template
      string entity_id = 2;
    }

    repeated HydrationEntity hydration_entities= 1;
  }

  string template_id = 1;
  // Options to hydrate the dynamic fields in provided template
  HydrationOptions hydration_options = 2;
}

message GetMessageTemplateResponse {
  message HydrationInformation {
    // If all dynamic fields in message template are hydrated
    bool all_fields_hydrated = 1;
  }
  MessageTemplate template = 1;
  HydrationInformation hydration_information = 2;
}

message GetConversationSummaryRequest {
  string conversation_id = 1;
}

message GetConversationSummaryResponse {
  string summary = 1;
}

message GetOwnerEmailRequest {
  SubjectParticipant subject_participant = 1;
}

message GetOwnerEmailResponse {
  string forwarding_email = 1;
}

// The captured lead
message CapturedLead {
  // The name of the lead
  string name = 1;
  // The email address of the lead
  string email = 2;
  // The phone number of the lead
  string phone = 3;
  // The id of the lead (e.g. id of the CRM contact or CRM company)
  oneof lead_id {
    // The id of the CRM contact
    string contact_id = 4;
  }
}

message NewInboxLeadCapturedRequest {
  // The namespace of the lead (e.g. "ABC", "AG-123" for CRM contacts that are namespaced to Vendasta's domain Account Group)
  string namespace = 1;
  // The parent namespace of the lead (e.g. "ABC", for CRM contacts that are namespace to Vendasta's domain Account Group OR "VMF", for CRM contacts that are namespaced to Vendasta's domain Partner)
  string parent_namespace = 2;
  // The identifier of the conversation the lead is associated with
  //Deprecated: use captured_lead.lead_id
  string conversation_id = 3 [deprecated=true];
  // The summary of the conversation or summary of where the lead came from
  string conversation_summary = 4;
  // The captured lead
  CapturedLead captured_lead = 5;
  // The location of the lead capture
  string source = 6;
  // The timestamp of when the lead was captured
  google.protobuf.Timestamp captured_at = 7;
  // A message to be sent to inbox
  string message = 8;
  // The id of the object initiating the lead capture. (e.g. form submission id)
  string initiator_id = 9;
}

message NewInboxLeadCapturedResponse {
}

message GetMultiEventsRequest {
  // The ids of the events to be retrieved
  repeated string event_ids = 1;
  // The conversation id
  string conversation_id = 2;
}

message GetMultiEventsResponse {
  // The list of events
  repeated Event events = 1;
}

message MakeToolCallRequest {
  // Tool calls can be suggested by AI in a message, this field contains the message and options selected by the user
  message MessageOption {
    // The message ID of the message that has the tool call definition
    string message_id = 1;
    // One or more unique identifiers representing the user's selected options
    repeated string selected_option_ids = 2;
  }
  // Requested tool call message option
  MessageOption message_option = 1;
}

message MakeToolCallResponse {
}

// Service for Conversation Microservice
service ConversationService {
  // grpc host for the service
  option (google.api.default_host) = "conversation-${ENV}.apigateway.co";
  // Send a message to a conversation
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse) {
    option (vendastatypes.access) = {
      scope: "conversation"
      scope: "business-app"
    };
  };
  // Get a message for a conversation
  rpc GetMessage (GetMessageRequest) returns (Message) {
    option (vendastatypes.access) = {
      scope: "conversation:read"
      scope: "business-app"
    };
  };
  // List messages of a conversation
  rpc ListMessages (ListMessagesRequest) returns (ListMessagesResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Delete a message of a conversation
  rpc DeleteMessage (DeleteMessageRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Update a message status of a conversation
  rpc UpdateMessageStatus (UpdateMessageStatusRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Lookup conversations based on a set uf participant attributes
  rpc LookupConversations (LookupConversationsRequest) returns (LookupConversationsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get a conversation based on the fields of conversation unique index
  rpc GetConversationByKey (GetConversationByKeyRequest) returns (GetConversationByKeyResponse) {
    option (vendastatypes.access) = {
      scope: "conversation:read"
      scope: "business-app"
    };
  };
  // Create a conversation
  rpc CreateConversation (CreateConversationRequest) returns (CreateConversationResponse) {
    option (vendastatypes.access) = {
      scope: "conversation"
      scope: "business-app"
    };
  };
  // Update a conversation
  rpc UpdateConversation (UpdateConversationRequest) returns (UpdateConversationResponse) {
    option (vendastatypes.access) = {
      scope: "conversation"
      scope: "business-app"
    };
  };
  // Delete a conversation
  rpc DeleteConversation (DeleteConversationRequest) returns (google.protobuf.Empty)  {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Receive a message in a conversation
  rpc ReceiveMessage (ReceiveMessageRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get a list of messages by ids
  rpc GetMultiMessages (GetMultiMessagesRequest) returns (GetMultiMessagesResponse) {
    option (vendastatypes.access) = {
      scope: "conversation:read"
      scope: "business-app"
    };
  };
  // Create a list of messages
  rpc CreateMultiMessages (CreateMultiMessagesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get the details for a conversation, including participants and latest message, for a list of conversation ids.
  rpc GetMultiConversationDetails (GetMultiConversationDetailsRequest) returns (GetMultiConversationDetailsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get the details for a conversation, including participants and latest message, for a list of conversation ids.
  rpc GetMultiConversationDetailsV2 (GetMultiConversationDetailsV2Request) returns (GetMultiConversationDetailsV2Response) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get a list of participants by ids
  rpc GetMultiParticipants (GetMultiParticipantsRequest) returns (GetMultiParticipantsResponse) {
    option (vendastatypes.access) = {
      scope: "conversation:read"
      scope: "business-app"
    };
  };
  // Search for conversations for an account and a search term.
  rpc SearchConversations (SearchConversationsRequest) returns (SearchConversationsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get a count of the number of messages on a conversation
  rpc GetMultiConversationMessageCount (GetMultiConversationMessageCountRequest) returns (GetMultiConversationMessageCountResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Adds multiple participants to an existing conversation
  rpc AddMultiParticipants (AddMultiParticipantsRequest) returns (AddMultiParticipantsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Set conversation lastSeen field
  rpc SetLastSeen (SetLastSeenRequest) returns (SetLastSeenResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get a list of participants by participant key
  rpc GetParticipantsByKey(GetParticipantsByKeyRequest) returns (GetParticipantsByKeyResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get conversation views for participant
  rpc GetConversationViews(GetConversationViewsRequest) returns (GetConversationViewsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Add conversation to Conversation view
  rpc AddConversationToConversationView(AddConversationToConversationViewRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Remove conversation from Conversation view
  rpc RemoveConversationFromConversationView(RemoveConversationFromConversationViewRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Create a new widget conversation
  rpc CreateWidgetConversation(CreateWidgetConversationRequest) returns (CreateWidgetConversationResponse);
  // Get a list of messages by ids
  rpc GetMultiWidgetMessages(GetMultiWidgetMessagesRequest) returns (GetMultiWidgetMessagesResponse);
  // Send message in a widget conversation
  rpc SendWidgetMessage(SendWidgetMessageRequest) returns (SendWidgetMessageResponse);
  // Get a widget conversation using the widget participant token
  rpc GetWidgetConversation(google.protobuf.Empty) returns (GetWidgetConversationResponse);
  // Get the list of channels that messages can be sent on for the provided conversation
  rpc GetAvailableChannelsForConversation(GetAvailableChannelsForConversationRequest) returns (GetAvailableChannelsForConversationResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Create a record of the evaluation of a response in a conversation
  rpc EvaluateResponse(EvaluateResponseRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // List response evaluations
  rpc ListResponseEvaluations(ListResponseEvaluationsRequest) returns (ListResponseEvaluationsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };

  // Widget Configuration CRUD
  // Create a new widget
  rpc CreateWidget(CreateWidgetRequest) returns (CreateWidgetResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get widget by id
  rpc GetWidget(GetWidgetRequest) returns (GetWidgetResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get widgets by id
  rpc GetMultiWidget(GetMultiWidgetRequest) returns (GetMultiWidgetResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Update a widget
  rpc UpdateWidget(UpdateWidgetRequest) returns (UpdateWidgetResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Delete a widget
  rpc DeleteWidget(DeleteWidgetRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // List widgets
  rpc ListWidgets(ListWidgetsRequest) returns (ListWidgetsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
      scope: "conversation.widget"
    };
    option (google.api.http) = {
      get: "/v1beta/widgets"
    };
    // TODO(WARP-1442): add google api visibility for generated docs
    //option (google.api.method_signature) = "";
    //option (google.api.method_visibility).restriction = "PUBLIC";
  };
  // Get widget config by id
  rpc GetWidgetConfig(GetWidgetConfigRequest) returns (GetWidgetConfigResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // List message templates for a subject participant
  rpc ListMessageTemplate(ListMessageTemplateRequest) returns (ListMessageTemplateResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Delete message template
  rpc DeleteMessageTemplate(DeleteMessageTemplateRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Create message template
  rpc CreateMessageTemplate(CreateMessageTemplateRequest) returns (CreateMessageTemplateResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Update message template
  rpc UpdateMessageTemplate(UpdateMessageTemplateRequest) returns (UpdateMessageTemplateResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get message template
  rpc GetMessageTemplate(GetMessageTemplateRequest) returns (GetMessageTemplateResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get conversation summary
  rpc GetConversationSummary(GetConversationSummaryRequest) returns (GetConversationSummaryResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Informs conversation that a new Inbox lead was captured, for example: form submissions
  rpc NewInboxLeadCaptured(NewInboxLeadCapturedRequest) returns (NewInboxLeadCapturedResponse);

  rpc GetMultiEvents(GetMultiEventsRequest) returns (GetMultiEventsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };

  // Inject message into a conversation
  rpc InjectMessage (InjectMessageRequest) returns (google.protobuf.Empty);

  rpc MakeToolCall(MakeToolCallRequest) returns (MakeToolCallResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
}

service InboxService {
  // Get a configuration for inbox
  rpc GetConfiguration(GetConfigurationRequest) returns (GetConfigurationResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get multiple configurations for inbox
  rpc GetMultiConfiguration(GetMultiConfigurationRequest) returns (GetMultiConfigurationResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Upsert will create or update a configuration for inbox
  rpc UpsertConfiguration(UpsertConfigurationRequest) returns (UpsertConfigurationResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  //  Upsert will update or create product feature availaiblity for inbox
  rpc UpsertProductFeature(UpsertProductFeatureRequest) returns (UpsertProductFeatureResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
}

service EmailService {
  // Get owner email
  rpc GetOwnerEmail(GetOwnerEmailRequest) returns (GetOwnerEmailResponse)
  {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
}

service AISystemMessageService {
  // Upsert will create or update an AI System Message
  rpc UpsertAISystemMessage(UpsertAISystemMessageRequest) returns (google.protobuf.Empty)
  {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Get will retrieve an AI System Message
  rpc GetAISystemMessage(GetAISystemMessageRequest) returns (GetAISystemMessageResponse)
  {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // List will retrieve a list of AI System Messages
  rpc ListAISystemMessages(ListAISystemMessagesRequest) returns (ListAISystemMessagesResponse)
  {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}
