syntax = "proto3";

package conversation.v1;

option go_package = "github.com/vendasta/generated-protos-go/conversation/v1;conversation_v1";
option java_outer_classname = "ConfigurationProto";
option java_package = "com.vendasta.conversation.v1.generated";

import "google/protobuf/timestamp.proto";
import "conversation/v1/common.proto";

// Configuration for Inbox
message Configuration {
  message AIConfiguration {
    bool sms_responder_enabled = 1;
  }

  // The subject participant that this configuration is for.
  SubjectParticipant subject_participant = 1;
  // The time that the configuration was created at
  google.protobuf.Timestamp created_at = 2;
  // The last time the configuration was updated
  google.protobuf.Timestamp updated_at = 3;
  // Availability Message is a message displayed at the top of inbox for your organization
  string availability_message = 4;
  // Determines if a availability message is displayed
  bool show_availability_message = 5;
  // Determines if google business messages is available for the organization referred to by subject_participant
  bool google_business_messages_enabled = 6;
  // Determines if sms is available for the organization referred to by subject_participant
  bool sms_enabled = 7;
  // Deprecated
  bool inbox_pro_enabled = 8 [deprecated=true];
  // Determines if web chat is available for the organization referred to by subject_participant
  bool web_chat_enabled = 9;
  // Determines if facebook messenger is available for the organization referred to by subject_participant
  bool facebook_messenger_enabled = 10;
  // Determine if Multi location is available for the organization referred to by subject_participant
  bool multi_location_enabled = 11;
  // Determines if instagram messaging is available for the organization referred to by subject_participant
  bool instagram_messaging_enabled = 12;
  // AI Configuration
  AIConfiguration ai_configuration = 13;
  // Determines if whats app is available for the organization referred to by subject_participant
  bool whatsapp_enabled = 14;
  // Determines if ai voice capabilities are available for the organization referred to by subject_participant
  bool ai_voice_enabled = 15;
}

message ProductFeature {
  // Determines if google business messages is available for the organization referred to by subject_participant
  bool google_business_messages_enabled = 1;
  // Determines if sms is available for the organization referred to by subject_participant
  bool sms_enabled = 2;
  // Determine if Multi location is available for the organization referred to by subject_participant
  bool multi_location_enabled = 3;
  // Determines if web chat is available for the organization referred to by subject_participant
  bool web_chat_enabled = 4;
  // Determines if facebook messenger is available for the organization referred to by subject_participant
  bool facebook_messenger_enabled = 5;
  // Determines if instagram messaging is available for the organization referred to by subject_participant
  bool instagram_messaging_enabled = 6;
  // Determines if ai sms responder is available for the organization referred to by subject_participant
  bool ai_sms_responder_enabled = 7;
  // Determines if whats app is available for the organization referred to by subject_participant
  bool whatsapp_enabled = 8;
  // Determines if ai voice capabilities are available for the organization referred to by subject_participant
  bool ai_voice_enabled = 9;
}
