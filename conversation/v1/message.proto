syntax = "proto3";

package conversation.v1;

option go_package = "github.com/vendasta/generated-protos-go/conversation/v1;conversation_v1";
option java_outer_classname = "MessageProto";
option java_package = "com.vendasta.conversation.v1.generated";
import "google/protobuf/timestamp.proto";
import "conversation/v1/conversation.proto";
import "conversation/v1/common.proto";
import "conversation/v1/ui_components.proto";

// MessageStatus represents the status a message is
enum MessageStatus {
  MESSAGE_STATUS_NOT_READ = 0;
  MESSAGE_STATUS_READ = 1;
  MESSAGE_STATUS_SENT = 2;
  MESSAGE_STATUS_DELIVERED = 3;
  MESSAGE_STATUS_FAILED = 4;
  MESSAGE_STATUS_UNDELIVERED = 5;
  MESSAGE_STATUS_SENDING = 6;
}

// ParticipantMessageStatus represents the status a message is for each for each of the participants
message ParticipantMessageStatus {
  string participant_id = 1;
  string message_id = 2;
  MessageStatus status = 3;
  google.protobuf.Timestamp updated = 4;
}

// MessageType represents the type of a message
enum MessageType {
  MESSAGE_TYPE_MESSAGE = 0;
  MESSAGE_TYPE_MEDIA = 1;
  MESSAGE_TYPE_SYSTEM = 2;
  // Form Submission messages have been moved to events
  MESSAGE_TYPE_FORM_SUBMISSION = 3 [deprecated=true];
  MESSAGE_TYPE_BOOKING_AVAILABILITY = 4;
  MESSAGE_TYPE_TEMPLATE = 5;
}

enum UIComponentType {
  UI_COMPONENT_TYPE_UNDEFINED = 0;
  UI_COMPONENT_TYPE_BUTTON = 1;
}

message UIComponent {
  UIComponentType type = 1;
  UIButton button = 2;
}
message SendStatus {
  MessageStatus status = 1;
  string reason = 2;
  google.protobuf.Timestamp created = 3;
  string error_code = 4;
}

// Message represents a message of a conversation
message Message {
  string message_id = 1;
  string external_message_id = 2;
  string conversation_id = 3;
  string participant_id = 4;
  MessageType type = 5;
  string body = 6;
  string error = 7;
  google.protobuf.Timestamp created = 8;
  google.protobuf.Timestamp updated = 9;
  google.protobuf.Timestamp deleted = 10;
  // media is the media content of the message
  repeated string media = 11;
  SendStatus send_status = 12;
  ConversationChannel channel = 13;
  // metadata can represent any kind of value
  repeated KeyValuePair metadata = 14;
  // original_content is the original content of the message
  string original_content = 15;
  repeated UIComponent ui_components = 16;
}
