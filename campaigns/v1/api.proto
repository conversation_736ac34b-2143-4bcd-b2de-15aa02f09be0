syntax = "proto3";

package campaigns.v1;

option go_package = "github.com/vendasta/generated-protos-go/campaigns/v1;campaigns";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.campaigns.v1.generated";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "campaigns/v1/email_builder.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/annotations.proto";
import "galaxytypes/v1/galaxy_filters.proto";

enum SenderType {
  SENDER_TYPE_INVALID = 0;
  SENDER_TYPE_PARTNER = 1;
  SENDER_TYPE_BUSINESS = 2;
}

enum SortField{
  SORT_FIELDS_INVALID = 0;
  SORT_FIELDS_NAME = 1;
  SORT_FIELDS_CREATED_AT = 2;
  SORT_FIELDS_UPDATED_AT = 3;
}

enum SortDirection{
  SORT_ORDER_INVALID = 0;
  SORT_ORDER_ASCENDING = 1;
  SORT_ORDER_DESCENDING = 2;
}

message Sender {
  // E.g. Partner, Business, Etc.
  SenderType type = 1;
  // E.g. Partner ID, Account Group ID, Etc.
  string id = 2;
}

message SenderOptions {
  // The custom user name for the email address. ie; `hello` in `Jane Miles <<EMAIL>>`
  string username = 1;
  // The display name. ie; `Jane Miles` in `Jane Miles <<EMAIL>>`
  string name = 2;
  // The reply to address
  string reply_to = 4;
  // the salesperson's id (company's or contact's)
  string salesperson_id = 5;
  // To check whether to send from the assigned salesperson's email
  bool send_from_assigned_salesperson = 6;
}

message SortOptions{
  // The field to sort on
  SortField field = 1;
  // The direction to sort: ie; ascending or descending
  SortDirection direction = 2;
}


enum ContactType {
  CONTACT_TYPE_INVALID = 0;
  CONTACT_TYPE_EMAIL = 1;
}

message Contact {
  // invalid or email for now
  ContactType type = 1;
  // value of type
  string value = 2;
}

message RecipientV2 {
  // E.g. Partner ID, Account Group ID, Etc.
  // DEPRECATED: Use external_id instead
  string id = 1;
  // E.g. Partner, Business, Etc.
  RecipientType type = 2;
  //contact info for the recipient
  // DEPRECATED: Use phone_number and email_address instead
  Contact contact = 3;
  // The phone number of the recipient
  string phone_number = 4;
  // The email address of the recipient
  string email_address = 5;
  // The unique ID of the recipient
  string recipient_id = 6;
  // The Id that identifies the recipient outside the campaigns service
  string external_id = 7;
}

enum RecipientType {
  RECIPIENT_TYPE_INVALID = 0;
  RECIPIENT_TYPE_CRM_CONTACT = 1;
  RECIPIENT_TYPE_BUSINESS = 2;
}

message DuplicateCampaignStepRequest {
  // The ID of the source and destination campaign where the step will be copied
  string campaign_id = 1;
  // The ID of the campaign step that will be copied
  string campaign_step_id = 2;
}

message DuplicateCampaignStepResponse {
  // An ID which can be fed into the
  string status_id = 1;
}

message CheckDuplicateCampaignStepStatusRequest {
  string status_id = 1;
}

// Deprecated: use AsyncWorkStatus
enum DuplicateCampaignStepStatus {
  DUPLICATE_CAMPAIGN_STEP_STATUS_INVALID = 0;
  DUPLICATE_CAMPAIGN_STEP_STATUS_IN_PROGRESS = 1;
  DUPLICATE_CAMPAIGN_STEP_STATUS_COMPLETE = 2;
}

message CheckDuplicateCampaignStepStatusResponse {
  DuplicateCampaignStepStatus status = 1;
}

message DuplicateCampaignDetails {
  // The name of the duplicated campaign
  string name = 1;
}

message DuplicateCampaignRequest {
  // The ID of the source campaign to copy
  string campaign_id = 1;
  // The ID of the partner who should own the new campaign
  string partner_id = 2;
  // Details for the duplicated campaign
  DuplicateCampaignDetails campaign_details = 3;
}
message DuplicateCampaignRequestV2 {
  // The ID of the source campaign to copy
  string campaign_id = 1;
  // The ID of the sender who should own the new campaign
  Sender sender = 2;
  // Details for the duplicated campaign
  DuplicateCampaignDetails campaign_details = 3;
}

message DuplicateCampaignResponse {
  // An ID which can be fed into the
  string status_id = 1;
}

message CheckDuplicateCampaignStatusRequest {
  string status_id = 1;
}

enum AsyncWorkStatus {
  ASYNC_WORK_STATUS_INVALID = 0;
  ASYNC_WORK_STATUS_IN_PROGRESS = 1;
  ASYNC_WORK_STATUS_COMPLETE = 2;
  ASYNC_WORK_STATUS_ERROR = 3;
}

message CheckDuplicateCampaignStatusResponse {
  AsyncWorkStatus status = 1;
  // ID of the new campaign ID. Should only be provided when status is "success".
  string campaign_id = 2;
}

message DuplicateAndRecommendCampaignRequest {
  // ID of the campaign to duplicate
  string campaign_id = 1;
  // Details for the duplicated campaign
  DuplicateCampaignDetails campaign_details = 3;
}

message ListLinksInTemplateRequest {
  // ID of the template to list links for
  string template_id = 1;
}

message ListLinksInTemplateResponse {
  // List of clickable links found in template
  repeated string links = 1;
}

message ExtractLinkTrackingDataForTemplateRequest {
  // ID of the template to list links for
  string template_id = 1;
  // DEPRECATED: Use hydration parameters instead
  string hydrated_template_content = 2 [deprecated = true];
  // Account group ID to hydrate templated links with
  string account_group_id = 3;
  // Partner ID to hydrate templated links with
  string partner_id = 4;
  // Market ID to hydrate templated links with
  string market_id = 5;
  // User ID to hydrate templated links with
  string user_id = 6;
}

message LinkTrackingData {
  // Normalized (i.e. unhydrated) version of the link to track
  string normalized_url = 1;
  // Hydrated version of the link to track
  string hydrated_url = 2;
}

message LinkStats{
  // The normalized url
  string url = 1;
  // Total number of times this link has been clicked
  int64 total_clicks = 2;
  // Number of unique clicks on this link
  int64 unique_clicks = 3;
  // Rate of clicks per delivered email
  double click_rate = 4;
}

message ExtractLinkTrackingDataForTemplateResponse {
  // List of clickable links found in template
  repeated LinkTrackingData links = 1;
}

message SendRecipientCampaignStepEmailRequest {
  // Usable by developers only. It bypasses any state checks and just sends the email.
  bool ignore_campaign_state = 1;
  // Sender is the entity who owns the campaign. E.g. partner, business, vendor.
  Sender sender = 2;
  // Identifies the recipient campaign that owns the step
  string recipient_campaign_id = 3;
  // Identified the step whose email will be sent via this RPC
  string recipient_campaign_step_id = 4;
}

message ExecuteStepRequest {
  // Identifies the recipient campaign that owns the step
  string recipient_campaign_id = 1;
  // Identified the step being executed via this RPC
  string recipient_campaign_step_id = 2;
  // Owner identifier
  Sender sender = 3;
}

message AddStepToCampaignRequest {
  // The ID of the campaign to add the step to
  string campaign_id = 1;
  // The type of campaign step to add
  CampaignStepType step_type = 2;
  // the ID of the content (email template ID, automation workflow ID, etc)
  string content_id = 3;
  // Owner identifier for step
  Sender sender = 4;
  // name of step
  string name = 5;
}

message StepDataResponse {
  // The ID of the template on the returned step
  string template_id = 1;
  // The ID of the returned step
  string campaign_step_id = 2;
}

message UpdateStepOnCampaignRequest {
  // The ID of the campaign to update the step on
  string campaign_id = 1;
  // The ID of the campaign step to update
  string campaign_step_id = 2;
  // Owner identifier for step
  Sender sender = 3;
  // the ID of the content (email template ID, automation workflow ID, etc)
  string content_id = 4;
  // name of step
  string name = 5;
  // seconds after the previous step in the campaign
  int64 seconds_after_last_step = 6;
  // Field mask to only update the needed fields
  vendastatypes.FieldMask field_mask = 7;
}

message GetCampaignStepContentRequest {
  // The ID of the campaign to get the step from
  string campaign_id = 1;
  // The ID of the campaign step to get the HTML for
  string campaign_step_id = 2;
  // The data required for hydrating the template's HTML
  map<string, string> step_data = 3;
}

message GetCampaignStepContentResponse {
  // The HTML for the campaign step
  string html = 1;
  // The subject line for the campaign step
  string subject = 2;
}

message SendCampaignStepTestEmailRequest {
  // The sender of the email
  Sender sender = 1;
  // The ID of the campaign to send the test email for
  string campaign_id = 2;
  // The ID of the campaign step to send the test email for
  string campaign_step_id = 3;
  // The ID of the user to send the test email to (this may be different from
  // the "recipient" that is provided in the step data)
  string send_to_user_id = 4;
  // The data required for hydrating the template's HTML
  map<string, string> step_data = 5;
}

message SendCampaignStepTestEmailResponse {
  // The ID of the email that was sent
  string email_id = 1;
}

message GetConfigResponse {
  // the timezone associated with the campaign
  string timezone = 1;
  // Days that the emails are able to be sent out
  repeated string days = 2;
  // Whether or not a buffer is enabled to limit the amount emails sent per sales person per day
  bool rate_limited = 3;
  // The size of the buffer for limiting the amount of emails sent
  int64 rate_limit = 4;
}

message GetConfigRequest {
  // The ID of the campaign to get config for
  string campaign_id = 1;
}

message UpdateConfigResponse{
  // the timezone associated with the campaign
  string timezone = 1;
  // Days that the emails are able to be sent out
  repeated string days = 2;
  // Whether or not a buffer is enabled to limit the amount emails sent per sales person per day
  bool rate_limited = 3;
  // The size of the buffer for limiting the amount of emails sent
  int64 rate_limit = 4;
}

message UpdateConfigRequest{
  // Unique ID associated to the campaign
  string campaign_id = 1;
  string time_zone_for_emails_sent = 2;
  // DEPRECATED: Use string array instead
  repeated DaysOfTheWeek days_emails_can_be_sent = 3;
  // Whether or not a buffer is enabled to limit the amount emails sent per sales person per day
  bool recipients_added_rate_limit_enabled = 4;
  // The size of the buffer for limiting the amount of emails sent
  int64 recipients_added_rate_limit = 5;
  //sender making the request
  Sender sender = 6;
  // The days that the email can be sent on
  repeated string email_scheduling_day = 7;
}

// Cursor and page size for paging request
message PagedRequestOptions {
  // cursor can be passed to retrieve the next page of results keyed by the cursor
  string cursor = 1;
  // page_size specifies the number of items to return in the next page
  int64 page_size = 2;
}

message ListCampaignsV2Request{
  // The ID of the sender
  Sender sender = 1;
  // The statuses of the campaigns
  repeated Statuses statuses = 2;
  // Paging information
  PagedRequestOptions paging_options = 3;
  // term to search with.
  string search_term = 4;
  // sort to use
  SortOptions sort_options = 5;
}


message PauseCampaignRequest {
  // The ID of the campaign to pause
  string campaign_id = 1;
}

message UnpauseCampaignRequest {
  // The ID of the campaign to unpause
  string campaign_id = 1;
}

enum CampaignQuotaCategory {
  CAMPAIGN_QUOTA_TYPE_INVALID = 0;
  CAMPAIGN_QUOTA_TYPE_EMAIL = 1;
  CAMPAIGN_QUOTA_TYPE_SMS = 2;
}

enum Period {
  PERIOD_INVALID = 0;
  PERIOD_MONTH = 1;
}

message GetCampaignQuotaRequest{
  // The ID and type of the sender
  Sender sender = 1;
  // The type of the quota
  CampaignQuotaCategory type = 2;
  // The period of the quota
  Period date_period = 3;
}

message GetCampaignQuotaResponse{
  // The count of the quota
  int64 count = 1;
  // The limit of the quota
  int64 limit = 2;
  // The start date of the quota
  google.protobuf.Timestamp quota_start_date = 3;
  // The end date of the quota
  google.protobuf.Timestamp quota_end_date = 4;
  // The last updated date of the quota
  google.protobuf.Timestamp updated = 5;
}

message CreateQuotaRequest {
  // The ID and type of the sender
  Sender sender = 1;
  // The type of the quota
  CampaignQuotaCategory type = 2;
  // The limit of the quota
  int64 limit = 4;
  // The start date that the quota is in effect for
  google.protobuf.Timestamp quota_start_date = 5;
  // The end date that the quota is in effect for
  google.protobuf.Timestamp quota_end_date = 6;
}

message UpdateQuotaLimitRequest {
  // The ID and type of the sender
  Sender sender = 1;
  // The type of the quota
  CampaignQuotaCategory type = 2;
  // The limit to set the quota to
  int64 limit = 4;
}

service CampaignQuota{
  // Gets the quota for a sender
  rpc GetCampaignQuota(GetCampaignQuotaRequest) returns (GetCampaignQuotaResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc CreateQuota(CreateQuotaRequest) returns (google.protobuf.Empty);
  rpc UpdateQuotaLimit(UpdateQuotaLimitRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
}

enum Feature {
  SEND_UNKNOWN = 0;
  SEND_SMS = 1;
  SEND_EMAIL = 2;
  SEND_SNAPSHOT = 3;
  SEND_EMAIL_ADDONS = 4;
}

message GetAvailableStepTypesForSenderRequest {
  // The campaign sender to check for
  Sender sender = 1;
}

message GetAvailableStepTypesForSenderResponse {
  // The available step types for the sender
  repeated Feature available_step_types = 1;
}

message SendPreviewEmailRequest {
  Sender sender = 1;
  string html_content = 2;
  string subject = 3;
  string recipient_user_id = 4;
}

service Campaigns {
  // Deletes a campaign based on a campaign ID
  rpc DeleteCampaign(DeleteCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Upserts a campaign
  rpc UpsertCampaign(UpsertCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Updates a given campaign's name
  rpc UpdateCampaignName(UpdateCampaignNameRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Gets multiple campaigns
  rpc ListCampaigns(ListCampaignsRequest) returns (ListCampaignsResponse);
  // Gets recommended campaigns
  rpc ListRecommendedCampaigns(ListRecommendedCampaignsRequest) returns (ListCampaignsResponse);
  // Gets campaign data based on a campaign ID
  rpc GetCampaign(GetCampaignRequest) returns (GetCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "business-app"
    };
  };
  // GetMultipleCampaigns gets multiple campaigns based on a list of campaign IDs
  rpc GetMultiCampaigns(GetMultiCampaignsRequest) returns (GetMultiCampaignsResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Starts a background process to duplicate an existing step and add it to the same campaign
  rpc DuplicateCampaignStepAsync(DuplicateCampaignStepRequest) returns (DuplicateCampaignStepResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Checks the background process started by DuplicateCampaignStepAsync
  rpc CheckDuplicateCampaignStepStatus(CheckDuplicateCampaignStepStatusRequest) returns (CheckDuplicateCampaignStepStatusResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Starts a background process to duplicate an existing step and add it to the same campaign
  rpc DuplicateCampaignAsync(DuplicateCampaignRequest) returns (DuplicateCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Starts a background process to duplicate an existing campaign with a sender
  rpc DuplicateCampaignAsyncV2(DuplicateCampaignRequestV2) returns (DuplicateCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Checks the background process started by DuplicateCampaignStepAsync
  rpc CheckDuplicateCampaignStatus(CheckDuplicateCampaignStatusRequest) returns (CheckDuplicateCampaignStatusResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Duplicates the campaign and recommends the duplicated campaign
  rpc DuplicateAndRecommendCampaign(DuplicateAndRecommendCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Sends an email as part of a campaign
  rpc SendRecipientCampaignStepEmail(SendRecipientCampaignStepEmailRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Executes a step as part of a campaign
  rpc ExecuteStep(ExecuteStepRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  //creates a campaign using a senderID
  rpc CreateCampaign(CreateCampaignRequest) returns (CreateCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Adds a step to a campaign
  rpc AddStepToCampaign(AddStepToCampaignRequest) returns (StepDataResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Gets the content for an email-type campaign step, with any dynamic content hydrated
  rpc GetCampaignStepContent(GetCampaignStepContentRequest) returns (GetCampaignStepContentResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Sends a campaign step's content to a user, with any dynamic content hydrated, for testing purposes
  rpc SendCampaignStepTestEmail(SendCampaignStepTestEmailRequest) returns (SendCampaignStepTestEmailResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Updates a step on a campaign
  rpc UpdateStepOnCampaign(UpdateStepOnCampaignRequest) returns (StepDataResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // RPC to list all campaigns for a sender.
  rpc ListCampaignsV2(ListCampaignsV2Request) returns (ListCampaignsV2Response) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // RPC to pause a campaign
  rpc PauseCampaign(PauseCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // RPC to unpause a campaign
  rpc UnpauseCampaign(UnpauseCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Gets which step types this sender can use as part of a campaign
  rpc GetAvailableStepTypesForSender(GetAvailableStepTypesForSenderRequest) returns (GetAvailableStepTypesForSenderResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
}

service Recipient {
  // Creates a campaign recipient
  rpc CreateRecipient(CreateRecipientRequest) returns (google.protobuf.Empty);
  rpc UpdateRecipient(UpdateRecipientRequest) returns (google.protobuf.Empty);
  rpc GetRecipient(GetRecipientRequest) returns (GetRecipientResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc ListRecipients(ListRecipientsRequest) returns (ListRecipientsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
}

service CampaignConfig {
  // Upserts a campaign config
  rpc UpsertConfig(UpsertConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Gets the campaign options
  rpc GetConfig(GetConfigRequest) returns (GetConfigResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "business-app"
    };
  };
  // Updates the campaign options
  rpc UpdateConfig(UpdateConfigRequest) returns (UpdateConfigResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
}

service EmailTemplate {
  rpc CreateEmailTemplate(CreateEmailTemplateRequest) returns (google.protobuf.Empty);
  rpc UpdateEmailTemplate(UpdateEmailTemplateRequest) returns (google.protobuf.Empty);
  rpc GetEmailTemplate(GetEmailTemplateRequest) returns (GetEmailTemplateResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc GetEmailTemplates(GetEmailTemplatesRequest)  returns (GetEmailTemplatesResponse);
  rpc PreviewEmailTemplate(PreviewEmailTemplateRequest) returns (PreviewEmailTemplateResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  rpc ListLinksInTemplate(ListLinksInTemplateRequest) returns (ListLinksInTemplateResponse);
  rpc ExtractLinkTrackingDataForTemplate(ExtractLinkTrackingDataForTemplateRequest) returns (ExtractLinkTrackingDataForTemplateResponse);
  rpc SendPreview(SendPreviewEmailRequest) returns (google.protobuf.Empty);
}

message CreateCampaignRequest{
  // the info about the sender creating the campaign
  Sender sender = 1;
  // The intent/goal of the campaign
  Focuses focus = 2;
  // The name of the campaign
  string name = 3;
}

message CreateCampaignResponse{
  string campaign_id = 1;
}

message CampaignStepEvent {
  // The event type of the step
  CampaignStepEventType event_type = 1;
  // The date for the event
  google.protobuf.Timestamp event_date = 2;
}

message ScheduledCampaignStepInterface {
  // Version of the campaign step
  int64 version = 1;
  // Unique identifier of the campaign step
  string campaign_step_id = 2;
  // Unique identifier of the template for the step
  string template_id = 3;
  // Unique identifier for the recipient campaign
  string recipient_campaign_id = 4;
  // Unique identifier of the scheduled step
  string step_id = 5;
  // Whether the step is an email or a snapshot creation event
  CampaignStepType campaign_step_type = 6;
  // Name of the scheduled campaign step
  string name = 7;
  // The date the campaign step is scheduled
  google.protobuf.Timestamp scheduled_date = 8;
  // The date the campaign step is sent
  google.protobuf.Timestamp date_sent = 9;
  // Delay between this step and the previous one
  int64 seconds_after_last_email = 10;
  // Send status of the step
  CampaignStepSendStatusType send_status = 11;
  // Whether the scheduled campaign step has been completed
  bool completed = 12;
  // The events that have occurred for the scheduled campaign step
  repeated CampaignStepEvent events = 13;
}

message UpsertRecipientCampaignRequest {
  // Version of the recipient campaign
  int64 version = 1;
  // Unique identifier of the recipient campaign
  string recipient_campaign_id = 2;
  // Unique identifier of the recipient
  string recipient_id = 3;
  // Unique identifier of the campaign
  string campaign_id = 4;
  // Partner ID that this recipient campaign belongs to
  string partner_id = 5;
  // Account group that this recipient campaign belongs to
  string account_group_id = 6;
  // ID of the market for the recipient campaign
  string market_id = 7;
  // Unique ID of the user that initiated the recipient campaign
  string from_id = 8;
  // Email of the user that initiated the recipient campaign
  string from_email = 9;
  // Name of the user that initiated the recipient campaign
  string from_name = 10;
  // Whether the recipient campaign is active or not
  bool is_active = 11;
  // Whether the recipient campaign is completed or not
  bool is_completed = 12;
  // Status of the recipient campaign
  CampaignScheduleStatus status = 13;
  // Scheduled campaign steps
  repeated ScheduledCampaignStepInterface schedule = 14;
  // Current step in the schedule that the campaign is on
  int64 schedule_step = 15;
  // Error message
  string error_message = 16;
  // Date/time when the recipient campaign was paused
  google.protobuf.Timestamp paused_date_time = 17;
  // Field mask to only update the needed fields
  vendastatypes.FieldMask field_mask = 18;
  // Email replies should be sent to
  string reply_to = 19;
  // If campaign config should be ignored in favour of sending now
  bool send_now = 20;
}

message ListRecipientCampaignsRequest {
  // Unique ID associated to the account group of the recipient
  string account_group_id = 1;
  // The sender which owns the recipient campaign
  Sender sender = 2;
}

message PauseRecipientCampaignRequest {
  // Unique identifier of the recipient campaign
  string recipient_campaign_id = 1;
}

message ResumeRecipientCampaignRequest {
  // Unique identifier of the recipient campaign
  string recipient_campaign_id = 1;
}


message RecipientCampaignStruct {
  // Version of the recipient campaign
  int64 version = 1;
  // Unique identifier of the recipient campaign
  string recipient_campaign_id = 2;
  // Unique identifier of the recipient
  string recipient_id = 3;
  // Unique identifier of the campaign
  string campaign_id = 4;
  // Partner ID that this recipient campaign belongs to
  string partner_id = 5;
  // Account group that this recipient campaign belongs to
  string account_group_id = 6;
  // ID of the market for the recipient campaign
  string market_id = 7;
  // Unique ID of the user that initiated the recipient campaign
  string from_id = 8;
  // Email of the user that initiated the recipient campaign
  string from_email = 9;
  // Name of the user that initiated the recipient campaign
  string from_name = 10;
  // Whether the recipient campaign is active or not
  bool is_active = 11;
  // Whether the recipient campaign is completed or not
  bool is_completed = 12;
  // Status of the recipient campaign
  CampaignScheduleStatus status = 13;
  // Scheduled campaign steps
  repeated ScheduledCampaignStepInterface schedule = 14;
  // Current step in the schedule that the campaign is on
  int64 schedule_step = 15;
  // Error message
  string error_message = 16;
  // Date/time when the recipient campaign was paused
  google.protobuf.Timestamp paused_date_time = 17;
}

message ListRecipientCampaignsResponse {
  repeated RecipientCampaignStruct recipient_campaign = 1;
}

message GetRecipientCampaignRequest {
  // Unique identifier of the recipient campaign
  string recipient_campaign_id = 1;
}

message GetRecipientCampaignResponse {
  // Recipient campaign
  RecipientCampaignStruct recipient_campaign = 1;
}

message AddContactToCampaignRequest {
  // Unique identifier of the recipient campaign
  string campaign_id = 1;
  // Unique identifier of the sender
  Sender sender = 2;
  // Unique identifier of the recipient
  RecipientV2 recipient = 3;
  // Date/time when the recipient campaign will start
  google.protobuf.Timestamp date = 4;
  // Optional sender arguments
  SenderOptions sender_options = 5;
  // Whether to start the campaign now
  bool send_now = 6;

}

message AddContactToCampaignResponse{
  string recipient_campaign_id = 1;
}

message RecipientCriteria {
  string search_term = 1;
  reserved 2, 3, 4;
  galaxytypes.v1.FilterGroup filter_group_v2 = 5;
}

message BulkAddContactsToCampaignRequest {
  // Unique identifier of the recipient campaign
  string campaign_id = 1;
  // Unique identifier of the sender
  Sender sender = 2;
  // Date/time when the recipient campaign will start
  google.protobuf.Timestamp date = 3;
  // Criteria for selecting  recipients
  RecipientCriteria criteria = 4;
  // Optional sender arguments
  SenderOptions sender_options = 5;
  // Whether to start the campaign now
  bool send_now = 6;

}

message LookupRecipientCampaignRequest {
  Sender sender = 1;
  RecipientV2 recipient = 2;
  string campaign_id = 3;
}

message LookupRecipientCampaignResponse {
  RecipientCampaignStruct recipient_campaign = 1;
}

message LookupRecipientCampaignsRequest {
  Sender sender = 1;
  repeated RecipientV2 recipients = 2;
  string campaign_id = 3;
}

message LookupRecipientCampaignsResponse {
  repeated RecipientCampaignStruct recipient_campaigns = 1;
}

message BulkAddContactIdsToCampaignRequest {
  // Unique identifier of the recipient campaign
  string campaign_id = 1;
  // Unique identifier of the sender
  Sender sender = 2;
  // List of contact ids to add to the campaign
  repeated string contact_ids = 3;
  // Date/time when the recipient campaign will start
  google.protobuf.Timestamp date = 4;
}

service RecipientCampaign {
  // Upserts a recipient campaign
  rpc UpsertRecipientCampaign(UpsertRecipientCampaignRequest) returns (google.protobuf.Empty);
  // Lists recipient campaigns by business (AGID)
  rpc ListRecipientCampaigns(ListRecipientCampaignsRequest) returns (ListRecipientCampaignsResponse) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
      scope: "business-app"
    };
  };

  // Pauses a recipient campaign
  rpc PauseRecipientCampaign(PauseRecipientCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
  // Resumes a recipient campaign
  rpc ResumeRecipientCampaign(ResumeRecipientCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
  // Gets a recipient campaign by ID
  rpc GetRecipientCampaign(GetRecipientCampaignRequest) returns (GetRecipientCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // Lookup Campaign Recipient
  rpc LookupRecipientCampaign(LookupRecipientCampaignRequest) returns (LookupRecipientCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "campaign.recipient"
    };
  };
  // Lookup Multiple Campaign Recipients (for bulk)
  rpc LookupRecipientCampaigns(LookupRecipientCampaignsRequest) returns (LookupRecipientCampaignsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "campaign.recipient"
      scope: "business-app"
    };
  };
  // Add contact to Campaign
  rpc AddContactToCampaign(AddContactToCampaignRequest) returns (AddContactToCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "admin",
      scope: "campaign.recipient"
      scope: "business-app"
    };
  };
  // Bulk Add contacts to Campaign
  rpc BulkAddContactsToCampaign(BulkAddContactsToCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
      scope: "business-app"
    };
  };
  // Bulk Add contacts to Campaign with only contact ids
  rpc BulkAddContactsIdsToCampaign(BulkAddContactIdsToCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
      scope: "business-app"
    };
  };
}

service RecipientCampaignStepEvent {
  // Creates a recipient campaign step event
  rpc CreateRecipientCampaignStepEvent(CreateRecipientCampaignStepEventRequest) returns (google.protobuf.Empty);
  // Upserts a recipient campaign step event
  rpc UpsertRecipientCampaignStepEvent(UpsertRecipientCampaignStepEventRequest) returns (google.protobuf.Empty);
}

service RecipientCampaignActivatedEvent {
  // Upserts a RecipientCampaignActivatedEvent
  rpc UpsertRecipientCampaignActivatedEvent(UpsertRecipientCampaignActivatedEventRequest) returns (google.protobuf.Empty);
}

service CampaignStats {
  // Gets some campaign stats for all filtered campaigns on a given pid
  rpc GetAggregatedCampaignStats(GetAggregatedCampaignStatsRequest) returns (GetAggregatedCampaignStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // Lists campaign stats for a given pid associated to specific filtered campaigns
  rpc ListCampaignStats(ListCampaignStatsRequest) returns (ListCampaignStatsResponse);
  // GetCampaignDetailsStats returns the overall stats for a given campaign
  rpc GetCampaignDetailsStats(GetCampaignDetailsStatsRequest) returns (GetCampaignDetailsStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "business-app"
    };
  };
  // GetLinkStats returns the link stats for a particular campaign step
  rpc GetLinkStats(GetLinkStatsRequest) returns (GetLinkStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  //  GetRecipientLinkClickStats returns details regarding the recipients that clicked on a specific link
  rpc GetRecipientLinkClickStats(GetRecipientLinkClickStatsRequest) returns (GetRecipientLinkClickStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  //  GetCampaignStepStats returns the stats for each step in a given campaign
  rpc GetCampaignStepStats(GetCampaignStepStatsRequest) returns (GetCampaignStepStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // ExportRecipientLinkClickStats kicks off a process to return a csv of the requested data
  rpc ExportRecipientLinkClickStats(ExportRecipientLinkClickStatsRequest) returns (google.protobuf.Empty);
  rpc ListCampaignStatsForSender(ListCampaignStatsForSenderRequest) returns (ListCampaignStatsForSenderResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "business-app"
    };
  };
}

message ActivateProductForSenderRequest {
  // The campaign sender to activate for
  Sender sender = 1;
  // The activation sku to activate a product for
  string sku = 2;
}
message IsProductActiveForSenderRequest{
  // The campaign sender to activate for
  Sender sender = 1;
  // The activation sku to check if a product is active for
  string sku = 2;
}
message IsProductActiveForSenderResponse{
  // Whether the product is active for the sender
  bool is_active = 1;
}

message DeactivateProductForSenderRequest {
  // The campaign sender to deactivate a product for
  Sender sender = 1;
  // The activation sku to deactivate a product for
  string sku = 2;
}

message IsFeatureActiveForSenderRequest {
  // The campaign sender to check for
  Sender sender = 1;
  // The activation featureId to check if a feature is active for the sender
  Feature feature_id = 2;
}

message IsFeatureActiveForSenderResponse {
  // Whether the feature is active for the sender
  bool is_active = 1;
}

service Activations {
  rpc ActivateProductForSender(ActivateProductForSenderRequest) returns (google.protobuf.Empty);
  rpc IsProductActive(IsProductActiveForSenderRequest) returns (IsProductActiveForSenderResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc DeactivateProduct(DeactivateProductForSenderRequest) returns (google.protobuf.Empty);
  rpc IsFeatureActive(IsFeatureActiveForSenderRequest) returns (IsFeatureActiveForSenderResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
}

// LegacyAppengineAPIService holds the RPCs that are a migration from the old appengine project these apis should only be used after talking
// to the team that owns campaigns
service LegacyAppengineAPIService {
  // Adds each recipient on an account group to a specific campaign
  rpc RateLimitedAddAccountGroupToCampaign(LegacyAddAccountGroupToCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
  // Adds a recipient to a specific campaign
  rpc RateLimitedAddToCampaign(LegacyRateLimitedAddToCampaignRequest) returns (LegacyRateLimitedAddToCampaignResponse) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
  // Pauses each recipient on an account group for a specific campaign
  rpc PauseAccountGroupOnCampaign(LegacyPauseAccountGroupOnCampaignRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
  // Get recipient campaigns associated with an Account Group Id
  rpc GetRecipientCampaignsByAgid(LegacyGetRecipientCampaignsByAgidRequest) returns (LegacyGetRecipientCampaignsByAgidResponse) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
  // Update active state of a recipient campaign
  rpc UpdateRecipientCampaignStatus(LegacyUpdateRecipientCampaignStatusRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "campaign.recipient"
    };
  }
}

// Enumeration of possible focus types a campaign can be
enum Focuses {
  FOCUSES_UNSPECIFIED = 0;
  FOCUSES_ACQUIRE = 1;
  FOCUSES_UPSELL = 2;
  FOCUSES_ADOPT = 3;
  FOCUSES_NONE = 4;
}

// Enumeration of possible statuses a campaign can have
enum Statuses {
  STATUSES_UNSPECIFIED = 0;
  STATUSES_DRAFT = 1;
  STATUSES_PUBLISHED = 2;
  STATUSES_ACTIVE = 3;
  STATUSES_ARCHIVED = 4;
}

enum DaysOfTheWeek {
  DAYS_OF_THE_WEEK_MONDAY = 0;
  DAYS_OF_THE_WEEK_TUESDAY = 1;
  DAYS_OF_THE_WEEK_WEDNESDAY = 2;
  DAYS_OF_THE_WEEK_THURSDAY = 3;
  DAYS_OF_THE_WEEK_FRIDAY = 4;
  DAYS_OF_THE_WEEK_SATURDAY = 5;
  DAYS_OF_THE_WEEK_SUNDAY = 6;
}

enum TemplateStatus {
  TEMPLATE_STATUS_INVALID = 0;
  TEMPLATE_STATUS_DRAFT = 1;
  TEMPLATE_STATUS_PUBLISHED = 2;
}

// Enumeration of possible campaign step event types
enum CampaignStepEventType {
  CAMPAIGN_STEP_EVENT_TYPE_UNSPECIFIED = 0;
  CAMPAIGN_STEP_EVENT_TYPE_PROCESSED = 1;
  CAMPAIGN_STEP_EVENT_TYPE_DROPPED = 2;
  CAMPAIGN_STEP_EVENT_TYPE_DELIVERED = 3;
  CAMPAIGN_STEP_EVENT_TYPE_DEFERRED = 4;
  CAMPAIGN_STEP_EVENT_TYPE_BOUNCE = 5;
  CAMPAIGN_STEP_EVENT_TYPE_OPEN = 6;
  CAMPAIGN_STEP_EVENT_TYPE_CLICK = 7;
  CAMPAIGN_STEP_EVENT_TYPE_SPAM_REPORT = 8;
  CAMPAIGN_STEP_EVENT_TYPE_UNSUBSCRIBE = 9;
  CAMPAIGN_STEP_EVENT_TYPE_CREATED = 10;
  CAMPAIGN_STEP_EVENT_TYPE_NOT_REQUIRED = 11;
  CAMPAIGN_STEP_EVENT_TYPE_REFRESHED = 12;
}

// Enumeration of possible campaign step types
enum CampaignStepType {
  CAMPAIGN_STEP_TYPE_UNSPECIFIED = 0;
  CAMPAIGN_STEP_TYPE_EMAIL = 1;
  CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION = 2;
  CAMPAIGN_STEP_TYPE_SMS = 3;
}

// Enumeration of possible campaign step send status types
enum CampaignStepSendStatusType {
  CAMPAIGN_STEP_SEND_STATUS_TYPE_UNSPECIFIED = 0;
  CAMPAIGN_STEP_SEND_STATUS_TYPE_UNSENT = 1;
  CAMPAIGN_STEP_SEND_STATUS_TYPE_QUEUED = 2;
  CAMPAIGN_STEP_SEND_STATUS_TYPE_SENDING = 3;
  CAMPAIGN_STEP_SEND_STATUS_TYPE_SENT = 4;
}

// Enumeration of possible campaign schedule statuses
enum CampaignScheduleStatus {
  CAMPAIGN_SCHEDULE_STATUS_UNSPECIFIED = 0;
  CAMPAIGN_SCHEDULE_STATUS_ACTIVE = 1;
  CAMPAIGN_SCHEDULE_STATUS_WAITING_ON_RATE_LIMIT = 2;
  CAMPAIGN_SCHEDULE_STATUS_STOPPED = 3;
  CAMPAIGN_SCHEDULE_STATUS_COMPLETED = 4;
}

// Enumeration of possible campaign states
enum CampaignState {
  CAMPAIGN_STATE_UNSPECIFIED = 0;
  CAMPAIGN_STATE_ACTIVE = 1;
  CAMPAIGN_STATE_IDLE = 2;
}

// Enumeration of possible campaign states
enum RecipientCampaignStatus {
  RECIPIENT_CAMPAIGN_STATUS_UNSPECIFIED = 0;
  RECIPIENT_CAMPAIGN_STATUS_ACTIVE = 1;
  RECIPIENT_CAMPAIGN_STATUS_PAUSED = 2;
  RECIPIENT_CAMPAIGN_STATUS_COMPLETED = 3;
}


message CampaignStepInterface {
  // Identifier of the campaign step
  string campaign_step_id = 1;
  // Whether the step is an email or a snapshot creation event
  CampaignStepType step_type = 2;
  // The unique identifier of the template for the step
  string template_id = 3;
  // Delay between this step and the previous one
  int64 seconds_after_last_email = 4;
  // Name of the step
  string name = 5;
}


message GetterCampaignData {
  // Version of the campaign
  int64 version = 1;
  // The ID of the partner
  string partner_id = 2;
  // ID for market that the stats pertain to
  string market_id = 3;
  // Unique identifier of the campaign
  string campaign_id = 4;
  // The intent/goal of the campaign
  Focuses focus = 5;
  // Details of the steps in the campaign
  repeated CampaignStepInterface campaign_schedule = 6;
  // Version of the campaign schedule
  int64 campaign_schedule_version = 7;
  // Numerical count for number of events/steps in the campaign
  int64 events_count = 8;
  // The name of the campaign
  string name = 9;
  // Status of the campaign
  Statuses status = 10;
  // The localization code for the campaign
  string locale = 11;
  // If the campaign is enabled or not
  bool is_enabled = 12;
  // If the campaign can be edited
  bool is_editable = 13;
  // Which partners this campaign is hidden for
  repeated string hidden_for_partners = 14;
  // If a campaign is premade/recommended or not
  bool is_premade = 15;
  // If duplicated, the campaign id of the campaign which this campaign was directly duplicated from.
  string parent_campaign_id = 16;
  // If duplicated, the campaign id of the campaign which this campaign was ultimately duplicated from.
  string root_campaign_id = 17;
  // User that created the campaign
  string created_user = 18;
  // User that last updated the campaign
  string updated_user = 19;
  // User that published the campaign
  string published_user = 20;
  // email_category_id is the category identifier of the campaign
  string email_category_id = 21;
  // The date that the campaign was published
  google.protobuf.Timestamp published = 22;
  // Time the campaign was created
  google.protobuf.Timestamp created = 23;
  // Time the campaign was updated
  google.protobuf.Timestamp updated = 24;
}

// Cursor and has more flag for paging response
message PagedResponseMetadata {
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 1;
  // Whether or not more results exist
  bool has_more = 2;
  // The total amount of results
  int64 total_results = 3;
}

message ListCampaignsV2Response{
  //list of campaigns for a sender
  repeated GetterCampaignData campaigns = 1;
  //Paging meta data
  PagedResponseMetadata metadata = 2;
}

// Request to upsert a new or existing campaign
message UpsertCampaignRequest {
  // The intent/goal of the campaign
  Focuses focus = 1;
  // The name of the campaign
  string name = 2;
  // The ID of the partner
  // DEPRECATED: Use sender instead
  string partner_id = 3;
  // Unique identifier of the campaign
  string campaign_id = 4;
  // ID for market that the stats pertain to
  // DEPRECATED: Use sender instead
  string market_id = 5;
  // If duplicated, the campaign id of the campaign which this campaign was directly duplicated from.
  string parent_campaign_id = 6;
  // If duplicated, the campaign id of the campaign which this campaign was ultimately duplicated from.
  string root_campaign_id = 7;
  // User that created the campaign
  string created_user = 8;
  // User that last updated the campaign
  string updated_user = 9;
  // User that published the campaign
  string published_user = 10;
  // The localization code for the campaign
  string locale = 11;
  // Status of the campaign
  Statuses status = 12;
  // Details of the steps in the campaign
  repeated CampaignStepInterface campaign_schedule = 13;
  // Whether the campaign is active or not
  bool is_enabled = 14;
  // Which partners this campaign is hidden for
  repeated string hidden_for_partners = 15;
  // Version of the campaign schedule
  int64 campaign_schedule_version = 16;
  // Version of the campaign
  int64 version = 17;
  // The date that the campaign was published
  google.protobuf.Timestamp published = 18;
  // email_category_id is the category identifier of the campaign
  string email_category_id = 19;
  // If a campaign is premade/recommended or not
  bool is_premade = 20;
  // Field mask to only update the needed fields
  vendastatypes.FieldMask field_mask = 21;
  // The ID and type of the sender
  Sender sender = 22;
}


// Request to delete an existing campaign
message DeleteCampaignRequest {
  // The corresponding campaign ID associated with the campaign
  string campaign_id = 1;
}

// Request to update the name of a campaign
message UpdateCampaignNameRequest {
  // The corresponding campaign ID associated with the campaign
  string campaign_id = 1;
  // The localization code for the campaign
  string locale = 2;
}

// Request to get a list of campaigns
message ListCampaignsRequest {
  // The ID of the partner
  string partner_id = 1;
  // The intents/goals of the campaigns
  repeated Focuses focuses = 2;
  // The statuses of the campaigns
  repeated Statuses statuses = 4;
  // Paging information
  PagedRequestOptions paging_options = 5;
  // Name of the campaigns to search/filter by
  string name = 6;
}

// Request to get a list of  recommended campaigns
message ListRecommendedCampaignsRequest {
  // The intents/goals of the campaigns
  repeated Focuses focuses = 1;
  // The statuses of the campaigns
  repeated Statuses statuses = 2;
  // Paging information
  PagedRequestOptions paging_options = 3;
}

// Response from getting a list of campaigns
message ListCampaignsResponse {
  // List of campaign data
  repeated GetterCampaignData campaigns = 1;
  // Paging information
  PagedResponseMetadata paging_metadata = 2;
}

// Request for getting a single campaign
message GetCampaignRequest {
  // The corresponding campaign ID associated with the campaign
  string campaign_id = 1;
}

// Response for getting a single campaign
message GetCampaignResponse {
  // The corresponding campaign data
  GetterCampaignData campaign = 1;
}

// Request for getting multiple campaigns
message GetMultiCampaignsRequest {
  // The corresponding campaign IDs associated with the campaigns
  repeated string campaign_ids = 1;
}

// Response when getting multiple campaigns
message GetMultiCampaignsResponse {
  // Thee corresponding campaign data
  repeated GetterCampaignData campaigns = 1;
}

// Request for creating a recipient
message CreateRecipientRequest {
  // Unique ID associated to the recipient
  string recipient_id = 1;
  // Unique ID associated to the partner
  string partner_id = 2;
  // Unique ID associated to the account group of the recipient
  string account_group_id = 3;
  // The email address of the recipient
  string email = 4;
  // The sender
  Sender sender = 5;
  // The recipient
  RecipientV2 recipient = 6;
  // The phone number of the recipient
  string phone_number = 7;
}

message GetRecipientRequest {
  // Unique ID associated to the recipient
  string recipient_id = 1;
}

message GetRecipientResponse {
  // Unique ID associated to the recipient
  string recipient_id = 1;
  // The email address associated with the Recipient
  string email = 2;
  // The type of the sender (partner, business), and the associated ID
  Sender sender = 3;
  // The type of the recipient (business, crm) and the associated ID
  RecipientV2 recipient = 4;
}

message ListRecipientsRequest {
  // Email of the recipient
  string email = 1;
  // Phone number of the recipient
  string phone_number = 2;
  // Paging information
  PagedRequestOptions paging_options = 3;
}

message ListRecipientsResponse {
  // List of recipients found
  repeated RecipientV2 recipients = 1;
  // Paging information
  PagedResponseMetadata paging_metadata = 2;
}

message UpdateRecipientRequest {
  // Unique ID associated to the recipient
  string recipient_id = 1;
  // Unique ID associated to the partner
  string partner_id = 2;
  // Unique ID associated to the account group of the recipient
  string account_group_id = 3;
  // The email address of the recipient
  string email = 4;
  // The sender
  Sender sender = 5;
  // The recipient
  RecipientV2 recipient = 6;
  // The phone number of the recipient
  string phone_number = 7;
}

// Request for creating or updating a config
message UpsertConfigRequest {
  // Unique ID associated to the campaign
  string campaign_id = 1;
  // Unique ID associated to the partner
  string partner_id = 2;
  // The timezone for the emails being sent
  string time_zone_for_emails_sent = 3;
  // The days that the email can be sent on
  repeated DaysOfTheWeek days_emails_can_be_sent = 4;
  // Whether or not a buffer is enabled to limit the amount emails sent per sales person per day
  bool recipients_added_rate_limit_enabled = 5;
  // The size of the buffer for limiting the amount of emails sent
  int64 recipients_added_rate_limit = 6;
}

message EmailTemplateStruct {
  // Unique identifier of the template
  string template_id = 1;
  // Unique identifier of the partner that owns this template
  string partner_id = 2;
  // Unique identifier of the market that this template belongs to
  string market_id = 3;
  // Name of the template
  string name = 4;
  // The focus of the campaign that this template belongs to
  Focuses campaign_focus = 5;
  // The status of the template (Draft, Published)
  TemplateStatus status = 6;
  // Whether the template is editable or not
  bool is_editable = 7;
  // User that last updated the template
  string updated_by_user_email = 8;
  // User that created the template
  string created_by_user_email = 9;
  // User that published the template
  string published_by_user_email = 10;
  // The subject of the template
  string subject = 11;
  // The html body of the template
  string html_body = 12;
  // Whether full html should be used or not
  bool use_full_html = 13;
  // The full html body of the template
  string full_html = 14;
  // The version number of the template
  int64 version = 15;
}

message CreateEmailTemplateRequest {
  EmailTemplateStruct template = 1;
}

message UpdateEmailTemplateRequest {
  // Unique identifier of the template
  string template_id = 1;
  // Name of the template
  string name = 4;
  // The subject of the template
  string subject = 11;
  // The html body of the template
  string html_body = 12;
  // The full html body of the template
  string full_html = 14;
  // User that last updated the template
  string updated_by_user_email = 8;
}

message RecipientCampaignStepEventStruct {
  // The version number of the recipient campaign step
  int64 version = 1;
  // Unique identifier of the event
  string event_id = 2;
  // Unique identifier of the partner that this campaign step event belongs to
  string partner_id = 3;
  // Unique identifier of the market that this campaign step event belongs to
  string market_id = 4;
  // Unique identifier of the account group that this campaign step event belongs to
  string account_group_id = 5;
  // Unique identifier of the campaign
  string campaign_id = 6;
  // Unique identifier of the campaign step
  string campaign_step_id = 7;
  // Unique identifier created for the recipient
  string recipient_id = 8;
  // Unique identifier for the recipient campaign
  string recipient_campaign_id = 9;
  // Unique identifier for the recipient campaign step
  string recipient_campaign_step_id = 10;
  // The event type of the step
  CampaignStepEventType event_type = 11;
  // The date for the event
  google.protobuf.Timestamp event_date = 12;
  // The unique identifier of the template for the step
  string template_id = 13;
  // Bot or not
  bool probably_bot = 14;
  // The url for a click event
  string url = 15;
  // The normalized url for a click event
  string normalized_url = 16;
}

message CreateRecipientCampaignStepEventRequest {
  // The version number of the recipient campaign step
  int64 version = 1;
  // Unique identifier of the event
  string event_id = 2;
  // Unique identifier of the partner that this campaign step event belongs to
  string partner_id = 3;
  // Unique identifier of the market that this campaign step event belongs to
  string market_id = 4;
  // Unique identifier of the account group that this campaign step event belongs to
  string account_group_id = 5;
  // Unique identifier of the campaign
  string campaign_id = 6;
  // Unique identifier of the campaign step
  string campaign_step_id = 7;
  // Unique identifier created for the recipient
  string recipient_id = 8;
  // Unique identifier for the recipient campaign
  string recipient_campaign_id = 9;
  // Unique identifier for the recipient campaign step
  string recipient_campaign_step_id = 10;
  // The event type of the step
  CampaignStepEventType event_type = 11;
  // The date for the event
  google.protobuf.Timestamp event_date = 12;
  // The unique identifier of the template for the step
  string template_id = 13;
  // Bot or not
  bool probably_bot = 14;
  // The url for a click event
  string url = 15;
  // The normalized url for a click event
  string normalized_url = 16;
}

message UpsertRecipientCampaignStepEventRequest {
  // The event to either update or create
  RecipientCampaignStepEventStruct step_event = 1;
  // Field mask to only update the needed fields
  vendastatypes.FieldMask field_mask = 2;
}

message UpsertRecipientCampaignActivatedEventRequest {
  // The version of the recipient campaign activated event
  int64 version = 1;
  /// The unique identifier of the recipient campaign activated event
  string recipient_campaign_id = 2;
  // The unique identifier of the campaign
  string campaign_id = 3;
  // The unique identifier of the partner
  string partner_id = 4;
  // The unique identifier of the account group
  string account_group_id = 5 ;
  // The unique identifier of the person responsible for this activated event
  string from_id = 6;
  // Field mask to only update the needed fields
  vendastatypes.FieldMask field_mask = 7;

}

message RenderRequest {
  EmailBuilderTemplate email_builder_template = 1;
}

message RenderResponse {
  string content = 1;
}

message CreateRequest {
  EmailBuilderTemplate email_builder_template = 1;
}

message CreateResponse {
  string id = 1;
}

message GetRequest {
  string id = 1;
}

message GetResponse {
  EmailBuilderTemplate email_builder_template = 1;
}

message UpdateRequest {
  EmailBuilderTemplate email_builder_template = 1;
  // paths to the properties to update - if not specified all will update
  vendastatypes.FieldMask field_mask = 2;
}

message DeleteRequest {
  string id = 1;
}

message GetEmailTemplateRequest {
  string template_id = 1;
}

message GetEmailTemplateResponse {
  EmailTemplateStruct template = 1;
}

message GetEmailTemplatesRequest {
  repeated string template_ids = 1;
}

message GetEmailTemplatesResponse {
  repeated EmailTemplateStruct templates = 1;
}

message PreviewEmailTemplateRequest {
  string template_id = 1;
  //The account group ID to be used as preview context
  string account_group_id = 2;
  //DEPRECATED: Use sender instead
  string partner_id = 3;
  //DEPRECATED: Use sender instead
  string market_id = 4;
  string locale = 5;
  // The user ID to be used as preview context
  string user_id = 6;
  // The contact ID to be used as preview context
  string contact_id = 7;
  //The sender to be used as preview context
  Sender sender = 8;
}

message PreviewEmailTemplateResponse {
  string template_html = 1;
  string subject = 2;
  string name = 3;
}


message DateFilter {
  // The inclusively get campaigns that is less than or equal to this date
  google.protobuf.Timestamp date_lte = 1;
  // The inclusively get campaigns that is greater than or equal to this date
  google.protobuf.Timestamp date_gte = 2;
}

message GetAggregatedCampaignStatsRequest {
  // The ID of the partner
  string partner_id = 1;
  // The intent/goal of the campaign
  Focuses focus = 2;
  // ID of the market to filter on
  string market_id = 3;
  // The selected statuses to filter on
  repeated Statuses statuses = 4;
  // The selected states to filter on
  repeated CampaignState states = 5;
  // The search term entered to filter campaign names
  string search_term = 6;
  // The dates to filter on
  DateFilter date_filter = 7;
}

message GetAggregatedCampaignStatsResponse {
  // Sum of all unique accounts for campaign(s) after filtering
  int64 total_accounts = 1;
  // Average click through rate of filtered campaign(s)
  double average_click_through_rate = 2;
  // Average open rate of filtered campaign(s)
  double average_open_rate = 3;
  // Sum of active campaigns
  int64 total_active_campaigns = 4;
}

enum DeletedTagsPolicy {
  // If a campaign is associated to a tag ID that has been deleted or is
  // otherwise missing, that tag will be excluded from the response
  DELETED_TAGS_POLICY_EXCLUDE_FROM_RESPONSE = 0;
}

message ConvertLegacyTemplateRequest{
  string template_id = 1;
  string partner_id = 2;
}

message ConvertLegacyTemplateResponse{
  string template_id = 1;
}

message GetCampaignDetailsStatsRequest {
  // The ID of the partner
  string partner_id = 1;
  // The ID of the campaign
  string campaign_id = 2;
  // The ID of the market to filter on
  string market_id = 3;
  // The date range to filter within
  DateFilter date_filter = 4;
}

message CampaignDetailStats {
  // Number of active accounts on a campaign
  int64 active = 1;
  // Click through rate of opened emails
  int64 clicks_per_opened_email = 2;
  // How may recipient campaigns have completed
  int64 completed = 3;
  // How many emails have been delivered on the campaign
  int64 delivered = 4;
  // Open rate of a campaign
  int64 opened = 5;
  // How many emails have been sent
  int64 sent = 6;
  // How may recipient campaigns have stopped
  int64 stopped = 7;
  // Total number of accounts on this campaign
  int64 total_accounts = 8;
  // Total number of unique accounts with opened emails
  int64 total_leads = 9;
  // Total number of recipients
  int64 total_recipients = 10;
  // How many emails have been undelivered
  int64 undeliverable = 11;
  // How may recipient campaigns are waiting on rate limit
  int64 waiting_on_rate_limit = 12;
}

message GetCampaignDetailsStatsResponse {
  // The overall stats for a given campaign
  CampaignDetailStats stats = 1;
}

message GetLinkStatsRequest{
  string partner_id = 1;
  string template_id = 2;
  string campaign_step_id = 3;
}

message GetLinkStatsResponse{
  // a LinkStats item per link in template
  repeated LinkStats stats = 1;
  // Total clicks across all links on this campaign step
  int64 total_clicks_on_campaign_step = 2;
}

message GetRecipientLinkClickStatsRequest{
  message PagedRequestOptions {
    // cursor can be passed to retrieve the next page of results keyed by the cursor
    string cursor = 1;
    // page_size specifies the number of items to return in the next page
    int64 page_size = 2;
  }
  // paging options
  PagedRequestOptions paging_options = 1;
  // Partner id
  string partner_id = 2;
  // The ID for a campaign Step
  string campaign_step_id = 3;
  // The normalized url to get stats for
  string normalized_url = 4;
}

message RecipientClickStats{
  message Recipient {
    // recipient email address
    string email_address = 3;
  }
  message Account {
    // the account group id
    string account_group_id = 1;
  }
  // The recipient of the campaign
  Recipient recipient = 1;
  // The account the campaign was sent to
  Account account = 2;
  // The recent click date
  google.protobuf.Timestamp recent_date = 3;
  // The number  of clicks
  int64 count = 4;
  // The partner ID
  string partner_id = 5;
}

message GetRecipientLinkClickStatsResponse{

  PagedResponseMetadata paging_metadata = 1;
  repeated RecipientClickStats data = 2;
}

message ListCampaignStatsRequest {
  // The ID of the partner
  string partner_id = 1;
  // The intent/goal of the campaign
  Focuses focus = 2;
  // ID of the market to filter on
  string market_id = 3;
  // The selected statuses to filter on
  repeated Statuses statuses = 4;
  // The selected states to filter on
  repeated CampaignState states = 5;
  // The search term entered to filter campaign names
  string search_term = 6;
  // Paging information
  PagedRequestOptions paging_options = 7;
  // How we should handle campaigns when their associated tags are missing or deleted
  DeletedTagsPolicy deleted_tags_policy = 8;
  // Tags to filter campaigns by
  repeated string tag_ids = 9;
}

message ListCampaignStatsForSenderRequest{
  // The ID and type of sender
  Sender sender = 1;
  // The intent/goal of the campaign
  Focuses focus = 2;
  // ID of the market to filter on
  string market_id = 3;
  // The selected statuses to filter on
  repeated Statuses statuses = 4;
  // The selected states to filter on
  repeated CampaignState states = 5;
  // The search term entered to filter campaign names
  string search_term = 6;
  // Paging information
  PagedRequestOptions paging_options = 7;
  // How we should handle campaigns when their associated tags are missing or deleted
  DeletedTagsPolicy deleted_tags_policy = 8;
  // Tags to filter campaigns by
  repeated string tag_ids = 9;
  //sorting options for campaigns
  SortOptions sort_options = 10;

}

message ListCampaignTagDetails {
  string tag_id = 1;
  string text = 2;
  string color = 3;
}

message ListCampaignStatsStruct {
  // The name of the campaign
  string name = 1;
  // Sum of all unique accounts for on a campaign
  int64 total_accounts = 2;
  // Number of active accounts on a campaign
  int64 active_accounts = 3;
  // How many emails have been delivered on the campaign
  int64 emails_delivered = 4;
  // Click through rate of a campaign
  double click_through_rate = 5;
  // Open rate of a campaign
  double open_rate = 6;
  // The status of the campaign
  Statuses status = 7;
  // The state the campaign is in
  CampaignState state = 8;
  // If the campaign has automations on it or not
  bool automations = 9;
  // Unique identifier of the campaign
  string campaign_id = 10;
  // How many events have/will occur on a campaign
  int64 events_count = 11;
  // If a campaign is editable or not
  bool is_editable = 12;
  // Any non-deleted tags on the
  repeated ListCampaignTagDetails tags = 13;
  // Time the campaign was updated
  google.protobuf.Timestamp updated = 14;
}

message ListCampaignStatsResponse {
  // Repeated values defining the list's response
  repeated ListCampaignStatsStruct stats = 1;
  // Paging information
  PagedResponseMetadata paging_metadata = 2;
}

message ListCampaignStatsForSenderResponse{
  repeated ListCampaignStatsStruct stats = 1;
  PagedResponseMetadata paging_metadata = 2;
}

message GetCampaignStepStatsRequest {
  string partner_id = 1;
  string campaign_id = 2;
  string market_id = 3;
  DateFilter date_filter = 4;
}

message CampaignStepStats{
  string campaign_step_id = 1;
  int64 delivered = 2;
  int64 opens = 3;
  double open_rate = 4;
  double click_to_open_rate = 5;
  int64 bounced = 6;
  int64 spam = 7;
  int64 unsubscribed = 8;
  int64 dropped = 9;
  int64 clicks = 10;
  int64 pending = 11;
  int64 created = 12;
  int64 refreshed = 13;
  int64 not_required = 14;
  int64 sent = 15;
  int64 stopped = 16;
}

message GetCampaignStepStatsResponse{
  repeated CampaignStepStats step_stats = 1;
}

message ExportRecipientLinkClickStatsRequest {
  // Partner id
  string partner_id = 1;
  // The ID for a campaign Step
  string campaign_step_id = 2;
  // The normalized url to get stats for
  string normalized_url = 3;
  // The user id of the requester
  string user_id = 4;
}

message LegacyAddAccountGroupToCampaignRequest {
  string campaign_id = 1;
  string account_group_id = 2;
  google.protobuf.Timestamp scheduled_date = 3;
}

message LegacyRateLimitedAddToCampaignRequest {
  string campaign_id = 1;
  string email = 2;
  string partner_id = 3;
  string account_group_id = 4;
  string market_id = 5;
  google.protobuf.Timestamp scheduled_date = 6;
  string from_email = 7;
  string from_name = 8;
  string from_id = 9;
}

message LegacyRateLimitedAddToCampaignResponse {
  string recipient_campaign_id = 1;
}

message LegacyPauseAccountGroupOnCampaignRequest {
  string campaign_id = 1;
  string account_group_id = 2;
}

message LegacyGetRecipientCampaignsByAgidRequest {
  string partner_id = 1;
  string account_group_id = 2;
}

message CampaignRecipient {
  string recipient_id = 1;
  string partner_id = 2;
  string account_group_id = 3;
  string email = 4;
}

message RecipientCampaignPair {
  RecipientCampaignStruct recipient_campaign = 1;
  CampaignRecipient recipient = 2;
}

message LegacyGetRecipientCampaignsByAgidResponse {
  repeated RecipientCampaignPair recipient_campaign_pairs = 1;
}

message LegacyUpdateRecipientCampaignStatusRequest {
  string recipient_campaign_id = 1;
  RecipientCampaignStatus status = 2;
}

service EmailBuilder {
  rpc Create(CreateRequest) returns (CreateResponse);
  rpc Get(GetRequest) returns (GetResponse);
  rpc Update(UpdateRequest) returns (google.protobuf.Empty);
  rpc Delete(DeleteRequest) returns (google.protobuf.Empty);
  rpc Render(RenderRequest) returns (RenderResponse);
  rpc Convert(ConvertLegacyTemplateRequest) returns (ConvertLegacyTemplateResponse);
}

message CampaignTag {
  SenderType sender_type = 1;
  string sender_id = 2;
  string tag_id = 3;
  string text = 4;
  string colour = 5;
}

message CreateTagRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string text = 3;
  string colour = 4;
}

message CreateTagResponse {
  CampaignTag campaign_tag = 1;
}

message ListTagsForSenderRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string cursor = 3;
  int64 page_size = 4;
}

message ListTagsForSenderResponse {
  repeated CampaignTag campaign_tags = 1;
  string cursor = 2;
  bool has_more = 3;
}

message AddTagToCampaignRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string campaign_id = 3;
  string tag_id = 4;
}

message RemoveTagFromCampaignRequest {
  string campaign_id = 3;
  string tag_id = 4;
}

message ListTagsForCampaignRequest {
  string campaign_id = 1;
  string cursor = 2;
  int64 page_size = 3;
}

message ListTagsForCampaignResponse {
  repeated CampaignTag campaign_tags = 1;
  string cursor = 2;
  bool has_more = 3;
}

message DeleteTagRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string tag_id = 3;
}

service CampaignTagging {
  rpc Create(CreateTagRequest) returns (CreateTagResponse);
  rpc ListTagsForSender(ListTagsForSenderRequest) returns (ListTagsForSenderResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  rpc AddTagToCampaign(AddTagToCampaignRequest) returns (google.protobuf.Empty);
  rpc RemoveTagFromCampaign(RemoveTagFromCampaignRequest) returns (google.protobuf.Empty);
  rpc ListTagsForCampaign(ListTagsForCampaignRequest) returns (ListTagsForCampaignResponse);
  rpc Delete(DeleteTagRequest) returns (google.protobuf.Empty);
}

message UpdateTemplateRequest {
  // The unique id for the campaign this template is on
  string campaign_id = 1;
  // The unique id for a template
  string template_id = 2;
  // The name of this template
  string name = 3;
  // The content for this template
  string content = 4;
}

service Templates {
  //  Updates a template
  rpc Update(UpdateTemplateRequest) returns (google.protobuf.Empty);
}
