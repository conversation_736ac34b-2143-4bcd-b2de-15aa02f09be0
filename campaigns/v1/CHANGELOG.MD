## 7.96.0
- Add  `Sender` to ListRecipientCampaigns rpc

## 7.95.0
- Add `campaign.recipient` and `business-app` scopes to `ListRecipientCampaigns`

## 7.94.0
- Add `updated` to `GetCampaignQuotaResponse`

## 7.93.0
- Add `send_now` to `UpsertRecipientCampaign`

## 7.92.0
- Add `send_now` to `AddContactToCampaignRequest` and `BulkAddContactsToCampaignRequest`

## 7.91.0
- Add SendPreview rpc to the EmailTemplate service

## 7.90.0
- Add `updateTemplate` rpc and `Templates` service

## 7.89.0
- Add `PauseRecipientCampaign` and `ResumeRecipientCampaign` to `RecipientCampaign` service

## 7.88.0
- Add `campaign.recipient` scope to `LookupRecipientCampaign` and `LookupRecipientCampaigns`

## 7.87.0
- Add `rate_limit` and `rate_limited` to `GetConfigResponse` and `UpdateConfigResponse`

## 7.86.0
- Adds `reply_to` to `UpsertRecipientCampaignRequest`

## 7.85.0
- Add `salesperson id` and `send_from_assigned_salesperson` to `SenderOptions` in the `AddContactToCampaignRequest`

## 7.84.0
- Add `SenderOptions` message and add it to the `AddContactToCampaignRequest` message

## 7.83.0
- Add `ListRecipients` to `Recipient` service

## 7.82.0
- Add `SEND_EMAIL_ADDONS` to `Feature`

## 7.81.0
- Add `BulkAddContactIdsToCampaign` to `Campaigns` service

## 7.80.0
- Add `business-app` scope to RPCs with admin scopes already and `PreviewEmailTemplate`

- ## 7.79.0
- Add `business-app` scope to RPCs

## 7.78.1
- Add campaign.recipient scope back to `AddContactToCampaign`

## 7.78.0
- Remove deprecated filters from `BulkAddContactsToCampaignRequest`

## 7.77.0
- Switch to galaxy filters for the `BulkAddContactsToCampaignRequest`

## 7.76.0
- Add `GetAvailableStepTypesForSender` RPC to `Campaigns` service

## 7.75.0
- Use a `FilterGroup` on the `BulkAddContactsToCampaignRequest`

## 7.74.0
- Add `BulkAddContactsToCampaign` to `Campaigns` service

## 7.73.0
- Add `isFeatureActive` to `Activations` service

## 7.72.1
- Add scope to `AddContactToCampaign`

## 7.72.0
- Add `admin` scope to read type RPCs

## 7.71.0
- Add `UpdateQuota` to `CampaignQuota` service

## 7.70.0
- Remove phoneNumber from AddContactToCampaignRequest

## 7.69.0
- Add phone_number and email_address to the RecipientV2 message

## 7.68.0
- Adds phoneNumber to AddContactToCampaignRequest

## 7.67.0
- Add `SortOptions` to `ListCampaignStatsForSender` and `updated` to `ListCampaignStatsStruct` rpc

## 7.66.0
- Add `ContactID` and `Sender` to `PreviewEmailTemplateRequest`. Soft deprecates partner ID and market

## 7.65.0
- Add `Sender` to `ExecuteStepRequest`

## 7.64.0
- Add `PhoneNumber` to `Recipient`

## 7.63.0
- Add `ExecuteStep` to `Campaigns` service

## 7.62.1
- Reorder protos to fix sdk build

## 7.62.0
- Add `sms` to `CampaignStepType` enum

## 7.61.0
- Add `GetRecipientCampaign` to the `Recipient` service

## 7.60.0
- Change `days_emails_can_be_sent` type in `UpdateConfigRequest`

## 7.59.0
- Add sorting to `ListCampaignsV2Request`

## 7.58.0
- Add `CreateQuota` to `CampaignQuota` service

## 7.57.0
- Add 'GetCampaignQuota' to 'CampaignQuota' service

## 7.56.0
- Add 'DuplicateCampaignAsyncV2' to 'campaigns' service

## 7.55.0
- Add 'PauseCampaign' and 'UnpauseCampaign' rpc to the 'Campaigns' service

## 7.54.0
- Add 'ListCampaignsV2' rpc

## 7.53.0
- Add 'DeactivateProduct' rpc to the 'Activation' service

## 7.52.0
- Add 'IsProductActive' rpc to the 'Activation' service

## 7.51.0
- Add Activation service and ActivateProductForSender RPC

## 7.50.0
- Add `lookupRecipientCampaign` rpc

## 7.49.2
- Fix declaration order of `RecipientCampaignStepEvent` message again

## 7.49.1
- Fix declaration order of `RecipientCampaignStepEvent` message

## 7.49.0
- Add Sender and Recipient to the Recipient type
## 7.48.0
-Add `UpdateConfig` rpc

## 7.47.0
- Add `LookupRecipientCampaign` Request to the RecipientCampaign service

## 7.46.0
- Add `AddContactToCampaign` rpc

## 7.45.0
- Add `UpdateStepOnCampaign` rpc

## 7.44.1
- Change return type of `AddStepToCampaign` rpc

## 7.44.0
- Add `sender` to the UpsertCampaignRequest

## 7.43.0
- Add `GetCampaignConfig` rpc

## 7.42.0
- Add `SendCampaignStepTestEmail` rpc to `Campaigns` service

## 7.41.0
- Add `GetCampaignStepContent` rpc to `Campaigns` service

## 7.40.0
- Add `AddStepToCampaign` rpc

## 7.39.0
- Adds `ListCampaignStatsForSender` endpoint

## 7.38.0
- Adds `CreateCampaign, CreateCampaignRequest and CreateCampaignResponse

## 7.37.0
- Adds the `ExportRecipientLinkClickStats` endpoint

## 7.36.1
- Remove unused field

## 7.36.0
- Add `normailized_url` to `LinkStats`

## 7.35.0
- Add a GetMulti request for getting multiple campaigns

## 7.34.0
- Add `GetCampaignStepStats` rpc

## 7.33.1
- Move `LinkStats` object above where it is used

## 7.33.0
- Adds `UpsertRecipientCampaignStepEvent` rpc

## 7.32.0
- RPC for GetRecipientLinkClickStats

## 7.31.1
- Return `click_rate` as a double in `GetLinkStats`

## 7.31.0
- Add `total_clicks_on_campaign_step` to GetLinkStats rpc

## 7.30.0
- Add `GetLinkStats` rpc

## 7.29.0
- Add hydration params to ExtractLinkTrackingDataForTemplateRequest
- Deprecate `hydrated_template_content`

## 7.28.0
- Add ExtractLinkTrackingDataForTemplate to EmailTemplate service

## 7.27.0
- Add campaign_details to the DuplicateAndRecommendCampaignRequest
- Add campaign_details to the DuplicateCampaignRequest

## 7.26.0
- Add url and normalized_url to CreateRecipientCampaignStepEventRequest

## 7.25.0
- Add ListLinksInTemplate to EmailTemplate service

## 7.24.0
- Add GetCampaignDetailsStats to the CampaignStats service

## 7.23.0
- Add DuplicateAndRecommend to the Campaign service

## 7.22.0
- Add GetEmailTemplates RPC to the EmailTemplate service

## 7.21.0
- Add `Delete` RPC to Campaign tagging

## 7.20.0
- Add `PreviewEmailTemplate` RPC

## 7.19.0
- Add `ConvertLegacyTemplate` RPC

## 7.18.0
- Add the parent to the EmailBuilderTemplate

## 7.17.0
- Add `tags` filter to `ListCampaignStats` RPC

## 7.16.0
- Include tags in response from `ListCampaignStats` RPC

## 7.15.0
- Add `ListTagsForCampaign` RPC

## 7.14.0
- Add `AddTagToCampaign` to `CampaignTagging` service.

## 7.13.0
- Add new service for creating and listing campaign tags

## 7.12.0
- Add `DateFilter` to `GetAggregatedCampaignStatsRequest` message

## 7.11.0
- Add `Colors` and `link` (in `topLogo`) in `EmailBuilderTemplate` messages

## 7.10.0
- Add `active_campaigns` to `GetAggregatedCampaignStatsResponse` to include this

## 7.9.0
- Add `partner_id` to `DuplicateCampaignRequest` so we know where to put the
  newly created campaigns.

## 7.8.0
- Add 'none' type focus campaigns

## 7.7.1
- Change the responses of `LegacyAppengineAPIService` to match that of the golang sdk

## 7.7.0
- Add `campaign_id` to `CheckDuplicateCampaignStatusResponse`

## 7.6.0
- Add `LegacyAppengineAPIService` to hold RPCs that are passthroughs to the marketing-automation appengine project

## 7.5.0
- Add `DuplicateCampaignStepAsync` and `DuplicateCampaignAsync` to `Campaigns`
  service, with supplementary RPCS: `CheckDuplicateCampaignStatusRequest` and
  `CheckDuplicateCampaignStepStatusRequest`.

## 7.4.1
- Add `is_editable` to `ListCampaignStatsStruct` to fully know what actions can be taken on a campaign (ex: delete vs archive)

## 7.4.0
- Add `events_count` to `ListCampaignStatsStruct` to know what actions can be taken on a campaign (ex: delete vs archive)

## 7.3.0
- Add `campaign_id` to `ListCampaignStatsStruct` to allow for listed items to link to their respective campaigns

## 7.2.0
- Define `CampaignState` enum
- Define `CampaignStats` service with `GetAggregatedCampaignStats` and `ListCampaignStats` rpcs
- Add `GetAggregatedCampaignStatsRequest` and `GetAggregatedCampaignStatsResponse` to `GetAggregatedCampaignStats`
- Add `ListCampaignStatsRequest`, `ListCampaignStatsResponse`, and `ListCampaignStatsStruct` to `ListCampaignStats`

## 7.1.0
- Add `name` to `ListCampaignsRequest`
- Add 'TotalResults' to `PagedResponseMetadata`
- Add `ListRecommendedCampaigns` to `campaigns` service

## 7.0.0
- Refactor `LookupCampaigns` to `ListCampaigns`

## 6.1.0
- Add `ListRecipientCampaigns` rpc to `RecipientCampaign` service with `ListRecipientCampaignsRequest` and `ListRecipientCampaignsResponse`
- Create `RecipientCampaignStruct` currently used as a limited repeated field for listing

## 6.0.0
- Add `GetEmailTemplate` rpc to `EmailTemplate` service with `GetEmailTemplateRequest` and `GetEmailTemplateResponse`
- Create `EmailTemplateStruct` to define the email template

## 5.3.0
- Add `UpsertRecipientCampaignActivatedEventRequest` to `RecipientCampaignActivatedEvent` service

## 5.2.0
- Add `field_mask` to `UpsertCampaignRequest` and `UpsertRecipientCampaignRequest` message

## 5.1.0
- Add `UpsertRecipientCampaignRequest` to `RecipientCampaign` service

## 5.0.0
- Refactor Campaigns and CampaignConfig services

## 4.1.0
- Add `email_category_id` to `CreateCampaignRequest`

## 4.0.0
- Use `CampaignStepType` instead of string in `CampaignStepInterface`

## 3.1.0
- Update CreateCampaign with missing parameters required for creation

## 3.0.0
- Delete `ScheduledCampaignStep` service

## 2.1.0
- Add `UpdateEmailTemplateRequest` to `EmailTemplate` service

## 2.0.0
- Remove `UpdateConfigRequest` from `CampaignConfig` service

## 1.10.0
- Add `UpdateConfigRequest` to `CampaignConfig` service

## 1.9.0
- Add `CreateScheduledCampaignStep` to `ScheduledCampaignStep` service

## 1.8.0
- Add `probably_bot` to `RecipientCampaignStepEvent` message

## 1.7.0
- Add `CreateRecipientCampaignStepEvent` to `RecipientCampaignStepEvent` service

## 1.6.0
- Make fullHTML property not a list

## 1.5.0
- Add rpcs to the `EmailBuilder` service: `Create`, `Get`, `Update`, `Delete`
- Add more properties to the `EmailBuilderTemplate` and `EmailBuilderConfiguration` messages

## 1.4.0
- Add `EmailBuilder ` service for apis interacting with emails separate from campaigns

## 1.3.0
- Add `CreateEmailTemplate` to `EmailTemplate` Service

## 1.2.0
- Add `CreateConfig` to `CampaignConfig` service

## 1.1.0
- Add `CreateRecipient` to `Recipient` service

## 1.0.0
- Initial commit of campaigns protos
