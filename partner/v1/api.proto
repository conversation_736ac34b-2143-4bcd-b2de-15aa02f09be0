syntax = "proto3";

package partner.v1;

option go_package = "github.com/vendasta/generated-protos-go/partner/v1;partner_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.partner.v1.generated";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/annotations.proto";
import "vendasta_types/field_mask.proto";
import "partner/v1/subscription_levels.proto";
import "partner/v1/trial.proto";
import "partner/v1/addon.proto";
import "partner/v1/stats.proto";
import "partner/v1/partner.proto";
import "partner/v1/feature_flags.proto";
import "partner/v1/partner_admin.proto";
import "partner/v1/partner_health.proto";
import "partner/v1/partner_legacy.proto";
import "partner/v1/partner_signup.proto";
import "partner/v1/rewards.proto";
import "partner/v1/subscription_failure.proto";
import "partner/v1/sales.proto";
import "partner/v1/business_center.proto";
import "partner/v1/subscription_tiers.proto";
import "partner/v1/terms_of_service.proto";
import "partner/v1/user_terms_of_service.proto";
import "partner/v1/user_onboarding.proto";
import "partner/v1/customer_survey.proto";
import "partner/v1/branding_v2.proto";
import "partner/v1/acquisition_data.proto";
import "partner/v1/free_trial.proto";
import "partner/v1/partner_model.proto";
import "partner/v1/navigation_item.proto";
import "partner/v1/task_manager.proto";
import "partner/v1/market.proto";
import "google/protobuf/struct.proto";

message PagedResponseMetadata {
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 1;
  // Whether or not more results exist
  bool has_more = 2;
}

message PagedRequestOptions {
  // cursor can be passed to retrieve the next page of results keyed by the cursor
  string cursor = 1;
  // page_size specifies the number of items to return in the next page
  int64 page_size = 2;
}

// URL stores both secure/insecure versions of a URL
message URL {
  string secure = 1;
  string insecure = 2;
}

message Theme {
  string name = 1;
  string primary_color = 2;
  string primary_hover_color = 3;
  string primary_active_color = 4;
  string secondary_color = 5;
  string secondary_hover_color = 6;
  string secondary_active_color = 7;
  string focus_color = 8;
  string font_color = 9;
  string font_disabled_color = 10;
  string accents_color = 11;
  string accents_active_color = 12;
  string border_color = 13;
}

// DEPRECATED: Use BrandingV2Service instead
message Branding {
  enum UITheme {
    Dark = 0;
    Light = 1;
  }
  message Assets {
    // Favicon is the small icon that will appear in browser tabs
    URL favicon_url = 1;
    // Shortut Icon is a mid-size icon that will appear in other browsing contexts, such as when the page is added to homescreen
    URL shortcut_icon_url = 3;
    // Logo is the image that will be used to brand the platform within pages
    URL logo_url = 2;
  }
  message App {
    // The whitelabeled name of the application
    string name = 2;
  }
  // Major theme of the UI -- Dark/Light
  UITheme ui_theme = 1 [deprecated = true];
  // Primary Color
  string primary_color = 2 [deprecated = true];
  // Assets such as logos and icons
  Assets assets = 3 [deprecated = true];
  // Application branding information
  map<string, App> apps = 4 [deprecated = true];
  // The partner / market name of the request
  string name = 5 [deprecated = true];
  // The partner name regardless of market
  string partner_name = 6 [deprecated = true];
  // Theme the partner/market selected theme
  Theme theme = 7 [deprecated = true];
}

// DEPRECATED: Use BrandingV2Service instead
message GetBrandingRequest {
  string partner_id = 1 [deprecated = true];
  string market_id = 2 [deprecated = true];
}

// DEPRECATED: Use BrandingV2Service instead
message GetBrandingResponse {
  Branding branding = 1 [deprecated = true];
}

// DEPRECATED: Use BrandingV2Service instead
message GetMultiBrandingRequest {
  repeated GetBrandingRequest requests = 1 [deprecated = true];
}

message SinglePartnerMarketBranding {
  string partner_id = 1;
  string market_id = 2;
  Branding branding = 3;
}

// DEPRECATED: Use BrandingV2Service instead
message GetMultiBrandingResponse {
  repeated SinglePartnerMarketBranding brandings = 1 [deprecated = true];
}

message SnapshotConfiguration {
  bool ppc = 1;
  bool remarketing = 2;
  bool use_custom_snapshot_header = 3;
  string custom_snapshot_header = 4;
  bool use_custom_snapshot_footer = 5;
  string custom_snapshot_footer = 6;
  bool use_custom_snapshot_popup_message = 7;
  string custom_snapshot_popup_message = 8;
  bool allow_claim_user = 9;
  int64 seo_advertising_data_provider = 10;
  string snapshot_report_name = 11;
}

message ExitLinkConfiguration {
  // the text to display for the exit link (back to dashboard)
  string exit_link_text = 1;
  // the url to redirect to when the exit link is clicked (https://sso.your-identity-provider.com)
  string exit_link_url = 2;
}

message ReputationConfiguration {
  int64 max_competitors = 1;
  int64 max_services = 2;
  string address_match_strictness = 3;
  string company_name_match_strictness = 4;
  string website_match_strictness = 5;
}

message EmailConfiguration {
  string sender_name = 1;
  string sender_email = 2;
  string reply_email = 3;
}

message MailingConfiguration {
  string mailing_company_name = 1;
  string mailing_address = 2;
  string mailing_address_2 = 3;
  string mailing_city = 4;
  string mailing_state = 5;
  string mailing_country = 6;
  string mailing_postal_code = 7;
  string mailing_phone_number = 8;
  string mailing_website_address = 9;
  string mailing_email_address = 10;
}

enum SegmentedOnboardingConfiguration {
  SEGMENTED_ONBOARDING_UNSET = 0;
  SEGMENTED_ONBOARDING_OPTIONAL = 1;
  SEGMENTED_ONBOARDING_REQUIRED = 2;
  SEGMENTED_ONBOARDING_DISABLED = 3;
}

message Configuration {
  repeated string geo_region_for_performance_metrics = 1;
  SnapshotConfiguration snapshot_configuration = 2;
  repeated string enabled_features = 3;
  repeated string tags = 4;
  // Deprecated: use SubscriptionTier instead
  SubscriptionLevel subscription_level = 5 [deprecated = true];
  string social_profile_group_id = 6;
  ReputationConfiguration reputation_configuration = 7;
  SalesConfiguration sales_configuration = 8;
  BusinessCenterConfiguration business_center_configuration = 9;
  bool has_brands_enabled = 10;
  bool has_product_marketplace = 11;
  bool adintel_is_enabled = 12;
  bool ms_always_enabled = 13;
  // Deprecated: use PartnerHealths service instead
  SubscriptionFailure subscription_failure = 14 [deprecated = true];
  EmailConfiguration email_configuration = 15;
  MailingConfiguration mailing_configuration = 16;
  // Deprecated: Retrieve subscription tier from Partner model in partner µService
  string subscription_tier_id = 17 [deprecated = true];
  // Deprecated: use UserOnboardingConfiguration instead
  SegmentedOnboardingConfiguration segmented_onboarding_configuration = 18 [deprecated = true];
  TaskManagerConfiguration task_manager_configuration = 19;
  // Local SEO App's Web Chat Widget for Business App is enabled at the partner level
  bool ms_chat_widget_enabled = 20;
  // Whether business nav is disabled in O&O products
  bool disable_business_nav = 21;
  // Used to configure the exit link / back to dashboard link within the atlas navigation bar
  ExitLinkConfiguration exit_link_configuration = 22;
  // Whether the product switcher is disabled within the atlas navigation bar
  bool disable_product_switcher = 23;
}

message GetConfigurationRequest {
  string partner_id = 1;
  // Market ID is optional and will return the partner configuration if not provided
  string market_id = 2;
}

message GetConfigurationResponse {
  Configuration configuration = 1;
}

message CreateTrialRequest {
  // The partner ID of the partner who is on the trial
  string partner_id = 1;
  // The subscription level that the trial will be emulating
  SubscriptionLevel subscription_level = 2 [deprecated = true];
  // The start date of the trial period
  google.protobuf.Timestamp start = 3;
  // The end date of the trial period
  google.protobuf.Timestamp end = 4;
  // The subscription tier ID that the trial will be emulating
  string subscription_tier_id = 5;
}

message GetTrialRequest {
  // The id of the partner who's trial is returned
  string partner_id = 1;
}

message GetTrialResponse {
  // the trial identifier
  string trial_id = 1 [deprecated = true];
  // The id of the partner who this trial will be applied to
  string partner_id = 2 [deprecated = true];
  // The date the trial object was created
  google.protobuf.Timestamp created = 3 [deprecated = true];
  // The date the trial object was updated
  google.protobuf.Timestamp updated = 4 [deprecated = true];
  // The subscription level the trial is emulating
  SubscriptionLevel subscription_level = 5 [deprecated = true];
  // The start date of this trial period
  google.protobuf.Timestamp start = 6 [deprecated = true];
  // The end date of this trial period
  google.protobuf.Timestamp end = 7 [deprecated = true];
  // The date this trial was cancelled
  google.protobuf.Timestamp cancel = 8 [deprecated = true];
  // the trial which is returned
  Trial trial = 9;
}

message CancelTrialRequest {
  // the trial identifier
  string trial_id = 1;
  // the partner id whose trial is being cancelled
  string partner_id = 2;
}

message ListTrialRequest {
  // the partner id whose trials are being listed
  string partner_id = 1;
  // the page size for the response
  int64 page_size = 2;
  // the cursor
  string cursor = 3;
}

message ListTrialResponse {
  // the trials that are returned
  repeated Trial trials = 1;
  // the cursor for paging with trials
  string cursor = 2;
  // whether there are more trials
  bool has_more = 3;
  // number of trials returned
  int64 hits = 4;
}

message ActivateAddonRequest {
  // identifier of the partner the addon is being activated for
  string partner_id = 1;
  // identifier of the addon being activated
  string addon_id = 2;
  // quantity of the addon being activated
  int32 quantity = 3;
}

message ActivateAddonResponse {
  // the newly activated activation
  AddonActivation activation = 1;
}

message DeactivateAddonRequest {
  // identifier of the activation to deactivate
  string activation_id = 1;
  string partner_id = 2;
  string addon_id = 3;
}

message ListAddonActivationsRequest {
  // identifier of the partner to list addon activations for
  string partner_id = 1;
  // paging metadata
  PagedRequestOptions paging_options = 2;
}

message ListAddonActivationsResponse {
  // activations found satisfying the request
  repeated AddonActivation activations = 1;
  // paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message GetAddonActivationRequest {
  // identifier of the partner
  string partner_id = 1;
  // identifier of the addon
  string addon_id = 2;
  // identifier of the activation
  string activation_id = 3;
}

message GetAddonActivationResponse {
  // activation found satisfying the request
  AddonActivation activation = 1;
}

message ListPartnerAddonsRequest {
}

message ListPartnerAddonsResponse {
  repeated PartnerAddon addons = 1;
}

message ResetPartnerRequest {
  // identifier of the partner to be reset
  string partner_id = 1;
  // if true, cancel all products and addons for all accounts.
  // If false, set subscription to free tier, keep everything else activated
  bool cancel_all_products = 2;
}

message GenerateAccountGroupSessionRequest {
  // The Partner ID associated to the Account Group
  string partner_id = 1;
  // The Account Group ID to generate a session for
  string account_group_id = 2;
  // Optionally provide an email for this session for tracking purposes
  string email = 3;
  // Whether or not this session should include write access to the account group
  bool write_access = 4;
  // Whether or not this session should include delete access to the account group
  bool delete_access = 5;
  // Optionally provide a user id for this session for tracking purposes
  string user_id = 6;
}

message GenerateAccountGroupSessionResponse {
  string id_token = 1;
}

service Whitelabel {
  // DEPRECATED: Use BrandingV2Service instead
  rpc GetBranding (GetBrandingRequest) returns (GetBrandingResponse);
  // DEPRECATED: Use BrandingV2Service instead
  rpc GetMultiBranding (GetMultiBrandingRequest) returns (GetMultiBrandingResponse);
  // Gets the configuration for a partner/market context
  rpc GetConfiguration (GetConfigurationRequest) returns (GetConfigurationResponse) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
}

service PartnerTrial {
  // Gets the trial object for a partner
  rpc GetTrial (GetTrialRequest) returns (GetTrialResponse);
  // Create a trial for a partner
  rpc CreateTrial (CreateTrialRequest) returns (google.protobuf.Empty);
  // Cancels a trial for a partner
  rpc CancelTrial (CancelTrialRequest) returns (google.protobuf.Empty);
  // Lists trials for a partner
  rpc ListTrial (ListTrialRequest) returns (ListTrialResponse);
}

service Addon {
  // Activate an addon for a partner
  rpc Activate (ActivateAddonRequest) returns (ActivateAddonResponse);
  // Deactivate an addon for a partner
  rpc Deactivate (DeactivateAddonRequest) returns (google.protobuf.Empty);
  // List addon activations for a partner
  rpc ListActivations (ListAddonActivationsRequest) returns (ListAddonActivationsResponse);
  // Get a specific addon activation
  rpc GetActivation (GetAddonActivationRequest) returns (GetAddonActivationResponse);
  // List the available addons for partners
  rpc ListPartnerAddons (ListPartnerAddonsRequest) returns (ListPartnerAddonsResponse);
}

// DEPRECATED: Not implemented in partner microservice; leaving to not introduce breaking changes to SDKs
// Get partner stats
service Stats {
  // List top partners and their relevant stats
  rpc ListTop (ListTopRequest) returns (ListTopResponse) {
    option deprecated = true;
  };
}

message PartnerCanDoActionRequest {
  // The Partner ID
  string partner_id = 1;
  // Specifies a particular action that can have a limit
  Action action_id = 2;
  // The ID of the business that the action is being performed for
  string business_id = 3;
}

message PartnerCanDoActionResponse {
  // Specifies if the action can be performed
  bool result = 1;
  // Gives the reason for rejections
  string reason = 2;
}

// GetPartnerAccountGroupMappingRequest is used to get the mapped AccountGroupID given a PartnerID, or the mapped PartnerID given an AccountGroupID
message GetPartnerAccountGroupMappingRequest {
  oneof identifier {
    // ID of the Partner
    string partner_id = 1;
    // ID of the Account Group
    string account_group_id = 2;
  }
  // ID of the partner that the Account Group is on, if it doesn't match the partner on record it will 404
  // Currently, optional may become required in the future.
  string account_group_partner_id = 3;
}

// DeletePartnerAccountGroupRequest is used to update the PartnerAccountGroup entity
message DeletePartnerAccountGroupRequest {
  // ID of the Partner (key value)
  string partner_id = 1;
}

// GetPartnerAccountGroupMappingResponse returns the mapped partnerID and accountGroupID
message GetPartnerAccountGroupMappingResponse {
  // ID of the Partner
  string partner_id = 1;
  // ID of the Account Group
  string account_group_id = 2;
  // ID of the partner that the Account Group is on
  string account_group_partner_id = 3;
}

// UpdatePartnerAccountGroupRequest is used to update the PartnerAccountGroup entity
message UpdatePartnerAccountGroupRequest {
  // ID of the Partner (key value)
  string partner_id = 1;
  // ID of the Account Group
  string account_group_id = 2;
}

// CreatePartnerAccountGroupRequest is used to create a PartnerAccountGroup entity
message CreatePartnerAccountGroupRequest {
  // ID of the Partner (key value)
  string partner_id = 1;
  // ID of the Account Group
  string account_group_id = 2;
}

// SwapPartnerAccountGroupRequest is used to swap the values of one PartnerAccountGroup with the values of another
message SwapPartnerAccountGroupRequest {
  // ID of the first Partner (key value)
  string partner_id = 1;
  // ID of the AccountGroup the Partner is currently mapped to
  string account_group_id = 3;
  // ID of the second Partner (key value)
  string with_partner_id = 4;
  // ID of the AccountGroup the second Partner is currently mapped to
  string with_account_group_id = 5;
}

service Partner {
  rpc Reset (ResetPartnerRequest) returns (google.protobuf.Empty);
  // Start the asynchronous process to update a partners subscription
  rpc UpdateSubscriptionTier (UpdateSubscriptionTierRequest) returns (google.protobuf.Empty);
  // Get the state of the partner's subscription update
  rpc GetUpdateSubscriptionTierStatus (GetUpdateSubscriptionTierStatusRequest) returns (GetUpdateSubscriptionTierStatusResponse);
  // Returns if the partner can perform the action specified
  rpc CanDoAction(PartnerCanDoActionRequest) returns (PartnerCanDoActionResponse);
  // Update the PartnerAccountGroup entity with the provided partner_id to have the given values
  rpc DeletePartnerAccountGroup (DeletePartnerAccountGroupRequest) returns (google.protobuf.Empty);
  // Returns the mapped PartnerID and AccountGroupID
  rpc GetPartnerAccountGroupMapping (GetPartnerAccountGroupMappingRequest) returns (GetPartnerAccountGroupMappingResponse) {
    option (vendastatypes.access) = {
      scope: "partner:read"
    };
  };
  // Create a new PartnerAccountGroup entity
  rpc CreatePartnerAccountGroup (CreatePartnerAccountGroupRequest) returns (google.protobuf.Empty);
  // Update the PartnerAccountGroup entity with the provided partner_id to have the given values
  rpc UpdatePartnerAccountGroup (UpdatePartnerAccountGroupRequest) returns (google.protobuf.Empty);
  // Swap the mapped values of one partner_id with another partner_id
  rpc SwapPartnerAccountGroup (SwapPartnerAccountGroupRequest) returns (google.protobuf.Empty);
  // Initiated the Update subscription tier action
  rpc InitiateSubscriptionTierUpdate (InitiateSubscriptionTierUpdateRequest) returns (google.protobuf.Empty);
  // GetPartnerLeadStats
  rpc GetPartnerLeadStats (GetPartnerLeadStatsRequest) returns (GetPartnerLeadStatsResponse);
}

service PartnerSignup {
  rpc SelfSignUp (SelfSignUpRequest) returns (google.protobuf.Empty) {
    option deprecated = true;
  };
  // SelfSignUpWithGoogle and SelfSignUpWithLinkedIn
  // the main difference for SelfSignUp
  // is that it uses a token to retrieve user information
  rpc SelfSignUpWithGoogle (SelfSignUpWithGoogleRequest) returns (google.protobuf.Empty) {
    option deprecated = true;
  };
  rpc SelfSignUpWithLinkedIn (SelfSignUpWithLinkedRequest) returns (google.protobuf.Empty) {
    option deprecated = true;
  };
  // Self sign up a partner when the email submitted was already identified as an existent account group in VMF
  rpc SelfSignUpExistingAccountGroup(SelfSignUpExistingAccountGroupRequest) returns (google.protobuf.Empty) {
    option deprecated = true;
  };
  rpc IsEmailInUse (IsEmailInUseRequest) returns (IsEmailInUseResponse);
  rpc IsEmailDisposable (IsEmailDisposableRequest) returns (IsEmailDisposableResponse);
  // Partner self sign up
  // GetSelfSignUpStatus is an endpoint to check the partner creation status
  rpc GetSelfSignUpStatus (GetSelfSignUpStatusRequest) returns (GetSelfSignUpStatusResponse);
  // login for self sign up, this operation works for with email/password or a google token
  rpc Login (LoginRequest) returns (LoginResponse);
  // Sign up is used for all possible sign up operations
  // supports normal sign up, using google, or linkedin as retrievers of user information
  rpc SignUp (SignUpRequest) returns (google.protobuf.Empty);
  // receives a Google Code and returns a Google Profile
  rpc GetGoogleProfile(GetGoogleProfileRequest) returns (GetGoogleProfileResponse);
  // receives a LinkedIn Authorization Code and returns a LinkedIn Profile
  rpc GetLinkedInProfile(GetLinkedinProfileRequest) returns (GetLinkedinProfileResponse);
  // receives a Microsoft Authorization Code and returns a Microsoft Profile
  rpc GetMicrosoftProfile(GetMicrosoftProfileRequest) returns (GetMicrosoftProfileResponse);
  // SignupAgent is called by AI agent with minimal information and no password or reCaptcha
  rpc SignUpAgent (SignUpAgentRequest) returns (google.protobuf.Empty);
}

message ResendPartnerAdminWelcomeEmailRequest {
  // Partner ID of the admin to send the email to
  string partner_id = 1;
  // User ID of the admin to send the email to
  string user_id = 2;
  // If true, the email will be sent using the platform default domail rather than the partner's domain
  bool use_platform_default_domain = 3;
}

message UpdatePartnerAdminRequest {
  // User ID of the partner admin to update permissions for
  string user_id = 1;
  // Mask for what fields should be updated
  vendastatypes.FieldMask field_mask = 2;
  // The permissions to update for the partner admin
  PartnerAdminPermissions permissions = 3;
}

service IAM {
  // Generates a session that is scoped to a given account group
  rpc GenerateAccountGroupSession (GenerateAccountGroupSessionRequest) returns (GenerateAccountGroupSessionResponse);

  // Resend welcome email for partner admins
  rpc ResendPartnerAdminWelcomeEmail (ResendPartnerAdminWelcomeEmailRequest) returns (google.protobuf.Empty);

  // Update a partner admin
  rpc UpdatePartnerAdmin (UpdatePartnerAdminRequest) returns (google.protobuf.Empty);
}

// ---- FEATURE FLAGS ----

// Request message for FeatureFlags.Create
message CreateFeatureFlagRequest {
  // The ID of the feature
  string id = 1;
  // The name of the feature
  string name = 2;
  // The description of the feature
  string description = 3;
  // The access level for which this feature is released to
  AccessLevel access_level = 4;
  // A url to go to for more info
  string more_info_url = 5;
  // Partners who are given explicit access to a feature, regardless of access_level value
  repeated PartnerAccess whitelist = 6;
  // Which, if any, partner ids are blacklisted from receiving access to this feature. Highest priority of evaluation.
  repeated PartnerAccess blacklist = 7;
  // Experiment Settings
  ExperimentSetting experiment_settings = 8;
  // Owner Name is the name of the person who owns this feature flag
  string owner_name = 9;
  // Expiration is the amount of time after which the flag will self-disable
  Expiration expiration = 10;
}

// Request message for FeatureFlags.Update
message UpdateFeatureFlagRequest {
  message Mutation {
    message PartnerAccessMutation {
      repeated PartnerAccess access_list = 1;
    }
    oneof mutation {
      // The name of the feature
      string name = 1;
      // The description of the feature
      string description = 2;
      // The level of access it is
      AccessLevel access_level = 3;
      // A url to go to for more info
      string more_info_url = 4;
      // a boolean representing whether or not this has been marked as archived
      bool archived = 5;
      // Partners who are given explicit access to a feature, regardless of access_level value
      PartnerAccessMutation whitelist = 6;
      // Which, if any, partner ids are blacklisted from receiving access to this feature. Highest priority of evaluation.
      PartnerAccessMutation blacklist = 7;
      // Experiment Settings
      ExperimentSetting experiment_settings = 8;
      string owner_name = 9;
      // Expiration is the amount of time after which the flag will self-disable
      Expiration expiration = 10;
      // RestartExpiry causes the expiration to restart from today
      bool restart_expiry = 11;
    }
  }
  // The ID of the feature
  string id = 1;
  // A list of mutations to be able to individually specify what fields are being updated
  repeated Mutation mutations = 2;
}

// Request message for FeatureFlags.BatchGetStatus
message BatchGetStatusFeatureFlagRequest {
  // a list of feature IDs (MUST be snake_case) to check if the status of
  repeated string feature_ids = 1;
  // the ID of a partner (3-4 alphanumeric characters) for which to check if features are enabled
  string partner_id = 2;
  // if included, the ID of a market within a partner for which to check if features are enabled
  string market_id = 3;
}

// Response message for FeatureFlags.BatchGetStatus
message BatchGetStatusFeatureFlagResponse {
  // A list of true/false for whether or not the specified partner+market has access to the requested features
  // respective of input feature ids order.
  // --------------------------------------
  // ** NOTE 1 **: If a feature flag is archived we will still perform the check as if it wasn't. This is to preserve
  // the previous functionality from va-vconfig.
  // ** NOTE 2 **: If a specified feature id on the request does not exist then the
  // respective entry in this response will be true.
  repeated bool has_access = 1;
}

// Request message for FeatureFlags.BatchGet
message BatchGetFeatureFlagRequest {
  // the ids of the feature flags to get
  repeated string ids = 1;
}

// Response message for FeatureFlags.BatchGet
message BatchGetFeatureFlagResponse {
  // feature flags found based on request ids
  repeated FeatureFlag flags = 1;
}

message ListFeatureFlagFilters {
  // Filter (not scored) results to only include that contain the search term in the ID, name, white list, or black list
  string search_term = 1;
  // Match Access Level of feature flag if provided.
  repeated AccessLevel access_level = 2;
}

// A request for FeatureFlags.List
// This can be used to list either normal, or archived flags
message ListFeatureFlagRequest {
  // only include archived feature flags, omit for normal list of non-archived
  bool archived_only = 1;
  // a cursor will allow iteration over searched results in a sane, bite-sized chunk, fashion
  string cursor = 2;
  // page_size defines the maximum number of results to return, if set to 0 it will default to 25
  int64 page_size = 3;
  // Filters
  ListFeatureFlagFilters filters = 4;
}

// A response for FeatureFlags.List
message ListFeatureFlagResponse {
  // A list of the feature flags found based on the input request
  repeated FeatureFlag flags = 1;
  // a cursor for continued iteration over search results
  string next_cursor = 2;
  // whether or not there are more entries to iterate over
  bool has_more = 3;
  // Total results
  int64 total_results = 4;
}

// Request message for FeatureFlags.CreateAnalyticsDashboard
message CreateAnalyticsDashboardRequest {
  // ID of the feature flag
  string feature_flag_id = 1;
  // Id of the user in vendasta platform
  string user_id = 2;
}


// Request message for FeatureFlags.AddPartnerToWhitelist
message AddPartnerToWhitelistRequest {
  // the id of the feature flag that will have an extra whitelisted PID added to
  string feature_flag_id = 1;
  // the id of the partner who will be added to list of whitelisted PIDs of the feature flag
  string partner_id = 2;
}

// Service to manage Feature Flags
service FeatureFlags {
  // Creates a feature flag
  rpc Create (CreateFeatureFlagRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
        scope: "admin"
    };
  };
  // Updates a feature flag
  rpc Update (UpdateFeatureFlagRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
        scope: "admin"
    };
  };
  // Gets a boolean answer on whether or not a feature flag is enabled for specified arguments
  rpc BatchGetStatus (BatchGetStatusFeatureFlagRequest) returns (BatchGetStatusFeatureFlagResponse) {
    option (vendastatypes.access) = {
        scope: "admin"
        scope: "business-app"
    };
  };
  // Gets a specified set of feature flag entities
  rpc BatchGet (BatchGetFeatureFlagRequest) returns (BatchGetFeatureFlagResponse) {
    option (vendastatypes.access) = {
        scope: "admin"
    };
  };
  // Lists feature flag entities
  rpc List (ListFeatureFlagRequest) returns (ListFeatureFlagResponse) {
    option (vendastatypes.access) = {
        scope: "admin"
    };
  };
  // CreateAnalyticsDashboard will create a dashboard used for experiment feature flags
  rpc CreateAnalyticsDashboard (CreateAnalyticsDashboardRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
        scope: "admin"
    };
  };
  // Adds a partner_id to a specific feature flag's list of whitelisted PIDs
  rpc AddPartnerToWhitelist (AddPartnerToWhitelistRequest) returns (google.protobuf.Empty) {
  };
}

// ---- END FEATURE FLAGS ----

// ---- START PARTNER HEALTH ----

// A request message to create partner health
message SetPartnerHealthRequest {
  // Identifier of a partner
  string partner_id = 1;
  // State to set for a partner  health
  PartnerHealthState state = 2;
  // Sub-State to set for a partner  health
  PartnerHealthSubState sub_state = 3;
  // Reason for setting the partner health
  string reason = 4;
  // If they are in retention or not
  bool in_retention = 5;
}

message GetPartnerHealthRequest {
  // Identifier of a partner
  string partner_id = 1;
}

message GetPartnerHealthResponse {
  // Current health of a partner
  PartnerHealth health = 1;
}

message ListPartnerHealthRequest {
  // Identifier of a partner
  string partner_id = 1;
  // Page size for the response
  int64 page_size = 2;
  // The cursor for next result
  string cursor = 3;
}

message ListPartnerHealthResponse {
  // The health of a partner
  repeated PartnerHealth healths = 1;
  // The cursor for paging
  string cursor = 2;
  // Whether there are more results
  bool has_more = 3;
  // Total number of healths for a partner
  int64 hits = 4;
}

message GetMultiPartnerHealthRequest {
  // List of partner identifiers
  repeated string partner_ids = 1;
}

message GetMultiPartnerHealthResponse {
  // List of partner healths
  repeated PartnerHealth healths = 1;
}

// Service to manage health of a partner
service PartnerHealths {

  rpc Set (SetPartnerHealthRequest) returns (google.protobuf.Empty) {
  };

  rpc Get (GetPartnerHealthRequest) returns (GetPartnerHealthResponse) {
  };

  rpc List (ListPartnerHealthRequest) returns (ListPartnerHealthResponse) {
  };

  rpc GetMulti (GetMultiPartnerHealthRequest) returns (GetMultiPartnerHealthResponse) {
  };
}

// Service to configure, and claim rewards for actions by partner in the platform
service Rewards {
    // If the Reward is not eligible by default, then each partner must be added individually
    rpc AddRewardToPartner (AddRewardToPartnerRequest) returns (google.protobuf.Empty) {};
    // Manually trigger a reward claim check, and start processing if eligible and conditions met
    rpc AttemptRewardClaim (AttemptRewardClaimRequest) returns (google.protobuf.Empty) {};
    // Get the Reward by ID, and partner ID
    rpc GetReward (GetRewardRequest) returns (GetRewardResponse) {};
    // Get the Reward by ID, and partner ID
    rpc IsRewardConditionMetForPartner (IsRewardConditionMetForPartnerRequest) returns (IsRewardConditionMetForPartnerResponse) {};
}

// Service to define Subscription Tiers for that Partners can be on
service SubscriptionTiers {
  // Get specific subscription by Id
  rpc GetSubscriptionTier(GetSubscriptionTierRequest) returns (GetSubscriptionTierResponse) {
  };
  // List all subscriptions
  rpc ListSubscriptionTiers(ListSubscriptionTiersRequest) returns (ListSubscriptionTiersResponse) {
  };
  // Datasource for visual comparison of subscription tiers
  rpc CompareSubscriptionTiers(CompareSubscriptionTiersRequest) returns (CompareSubscriptionTiersResponse) {
  };
}

// Service to set and track the onboarding use cases preformed by users.
service UserOnboarding {
  // Replace the status for the onboarding wizard
  rpc ReplaceWizardStatus(ReplaceWizardStatusRequest) returns (google.protobuf.Empty);
  // Return the status of the onboarding wizard associated with a user and a partner
  rpc GetWizardStatus(GetWizardStatusRequest) returns (GetWizardStatusResponse);
  // Returns the statuses of the wizard across the partner
  rpc ListWizardStatus(ListWizardStatusRequest) returns (ListWizardStatusResponse);
  // Replace the status for the onboarding wizard
  rpc DeleteWizardStatus(DeleteWizardStatusRequest) returns (google.protobuf.Empty);
}

service PartnerTermsOfService {
  rpc GetTermsOfService (GetTermsOfServiceRequest) returns (GetTermsOfServiceResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc CreateTermsOfService (CreateTermsOfServiceRequest) returns (google.protobuf.Empty);
}

service PartnerUserAgreementTermsOfService {
  rpc GetUserAgreementTermsOfService (GetUserAgreementTermsOfServiceRequest) returns (GetUserAgreementTermsOfServiceResponse) {
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
  rpc SetUserAgreementTermsOfService (SetUserAgreementTermsOfServiceRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "business-app",
    };
  };
}

// Deprecated: This is no longer to be used
service CustomerSurveyService {
  // Deprecated: This is no longer to be used
  rpc Respond (CustomerSurveyRespondRequest) returns (CustomerSurveyRespondResponse);
  // Deprecated: This is no longer to be used
  rpc AddFeedback (CustomerSurveyAddFeedbackRequest) returns (google.protobuf.Empty);
  // Deprecated: This is no longer to be used
  rpc ListResponses (CustomerSurveyListResponsesRequest) returns (CustomerSurveyListResponsesResponse);
}

service PreviewEmailService {
  // Creates an entry when the partner successfully sends a preview email
  rpc CreatePreviewEmail (CreatePreviewEmailRequest) returns (google.protobuf.Empty);
  // Returns a boolean of whether the partner is able to send preview emails or not
  rpc CanSendPreviewEmail (CanSendPreviewEmailRequest) returns (CanSendPreviewEmailResponse);
}

message CreatePreviewEmailRequest {
  // Identifier of a partner
  string partner_id = 1;
  // Identifier of the email template being sent
  string template_id = 2;
  // Email of the user sending the email
  string user_id = 3;
}

message CanSendPreviewEmailRequest {
  // Identifier of a partner
  string partner_id = 1;
}

message CanSendPreviewEmailResponse {
  // Whether the partner is allowed to send emails or not
  bool can_send_preview_emails = 1;
}

message GetBrandingV2Request {
  // ID of partner
  string partner_id = 1;
  // ID of market
  string market_id = 2;
}

message GetBrandingV2Response {
  BrandingV2 branding = 1;
}

message GetMultiBrandingV2Request {
  repeated GetBrandingV2Request requests = 1;
}

message GetMultiBrandingV2Response {
  repeated BrandingV2 branding = 1;
}

message UpsertMutation {
  message MarketOverridesMutation {
    repeated Field fields = 1;
  }
  oneof mutation {
    string favicon_url = 1;
    string shortcut_icon_url = 2;
    string logo_url = 3;
    UITheme ui_theme = 4;
    string primary_color = 5;
    // MarketOverridesMutation should only be added to Branding with a specific marketID. Has no benefit on the empty marketID.
    MarketOverridesMutation market_overrides = 6;
    string name = 7;
    string dark_mode_logo_url = 8;
    UITheme business_app_ui_theme = 9;
  }
}

message UpsertBrandingV2Request {
  // ID of the partner
  string partner_id = 1;
  // ID of the market. Can be empty string
  string market_id = 2;
  // A list of mutations to be able to individually specify what fields are being updated
  repeated UpsertMutation mutations = 3;
}

message ListBrandingV2Request {
  // ID of the partner
  string partner_id = 1;
  // Paging options (optional)
  PagedRequestOptions paging_options = 2;
}

message ListBrandingV2Response {
  // Brandings
  repeated BrandingV2 brandings = 1;
  // Paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message GetPartnerCenterNavigationRequest {
  string partner_id = 1;
}

message GetPartnerCenterNavigationResponse {
  repeated NavigationItem navigation = 1;
}

message ListMarketsForUserRequest {
  // id of the partner whose markets we want
  string partner_id = 1;
}

message ListMarketsForUserResponse {
  // List of markets
  repeated Market markets = 1;
}

// Service for interacting Partner that will maintain functionality that AA offered.
// But there is no guarantee that we will keep any of these endpoint long term as we work to
// migrate away from AA and restructure the functionality it contained.
service PartnerLegacy {
  // Create a Partner
  rpc CreatePartner (CreatePartnerRequest) returns (google.protobuf.Empty);
  // Polling endpoint to get the result of CreatePartner. This endpoint blocks for a set amount of time
  rpc GetCreatePartnerStatus (GetCreatePartnerStatusRequest) returns (GetCreatePartnerStatusResponse);
  // Check if the payment card the partner's added has any problems.
  rpc GetSignUpAddPaymentCardResult(GetSignUpAddPaymentCardResultRequest) returns (GetSignUpAddPaymentCardResultResponse);
  // Update the partner company profile values
  rpc UpdatePartnerCompanyProfile (UpdateCompanyProfileRequest) returns (google.protobuf.Empty);
  // Update partner
  rpc UpdatePartner (UpdatePartnerRequest) returns (google.protobuf.Empty);
  // Delete a partner
  rpc DeletePartner (DeletePartnerRequest) returns (google.protobuf.Empty);
  // Polling endpoint to get the result of UpdatePartner
  rpc GetUpdatePartnerStatus (GetUpdatePartnerStatusRequest) returns (GetUpdatePartnerStatusResponse);
  // GetPartnerCenterNavigation is the legacy navigation structure generation for partner center
  rpc GetPartnerCenterNavigation (GetPartnerCenterNavigationRequest) returns (GetPartnerCenterNavigationResponse);
  // ListMarketsForUser will return the markets that the user has access to on the partner
  rpc ListMarketsForUser (ListMarketsForUserRequest) returns (ListMarketsForUserResponse) {
    option (vendastatypes.access) = {
      scope: "market:read"
      scope: "market"
    };
  };
}

// Latest service to interact with the information a partner wants to display themselves with to clients (whitelabel data)
service BrandingV2Service {
  // Get the branding for a particular market or the partners fallback (empty market)
  // If getting the branding for a particular market, the result will be the merge of the partner branding and market specific branding
  rpc GetBranding (GetBrandingV2Request) returns (GetBrandingV2Response) {
    option (vendastatypes.access) = {
      scope: "business-app"
    };
  };
  // Get multiple partner's branding
  rpc GetMultiBranding (GetMultiBrandingV2Request) returns (GetMultiBrandingV2Response);
  // List the brandings for a partner
  rpc ListBranding (ListBrandingV2Request) returns (ListBrandingV2Response);
  // Update/Create a partner's branding
  rpc UpsertBranding (UpsertBrandingV2Request) returns (google.protobuf.Empty);
}

// Service to retrieve acquired data during signup process - used in conjunction of the partner-created event-broker
// after the partner is created by µservice partner, it emits a event broadcasting the id of the partner
// if other system needs to retrive some acquisition data after that, it should use this service
service AcquisitionDataService {
  rpc GetAcquisitionData (GetAcquisitionDataRequest) returns (GetAcquisitionDataResponse);
}

// Service to get Partner 14 Day Free Trail Status
// Please note this is unrelated to the PartnerTrial Service, that was an earlier implementation of trials
// This implementation of trial defers payment for the 14 days, but otherwise is indistinguishable from
// on being on a paid subscription tier.
service FreeTrialService {
  // GetFreeTrialStatus gets the current status of a partners free trial
  rpc GetFreeTrialStatus (GetFreeTrialStatusRequest) returns (GetFreeTrialStatusResponse);
  // GetFreeTrial gets partners free trial details
  rpc GetFreeTrial (GetFreeTrialRequest) returns (GetFreeTrialResponse);
  // UpdateFreeTrial submits which fields we want to update on a free trial model
  rpc UpdateFreeTrial (UpdateFreeTrialRequest) returns (google.protobuf.Empty);
}

message GetPartnerModelRequest {
  string partner_id = 1;
}

message GetPartnerModelResponse {
  PartnerModel partner = 1;
}

message GetMultiPartnerModelRequest {
  repeated string partner_ids = 1;
}

message GetMultiPartnerModelResponse {
  repeated PartnerModel partners = 1;
}

message UpdatePartnerModelMutation {
  message CountryState {
    string country_code = 1;
    string state_code = 2;
  }
  oneof mutation {
    bool premium_support = 1;
    bool standard_priority_support = 2;
    bool priority_chat_support = 3;
    string name = 4;
    string address_line_one = 5;
    string address_line_two = 6;
    string city = 7;
    CountryState country_and_state = 8;
    string postal_code = 9;
    string email = 10;
    string phone_number = 11;
    string website = 12;
  }
}

message UpdatePartnerModelRequest {
  // ID of the partner
  string partner_id = 1;
  // A list of mutations to be able to individually specify what fields are being updated
  repeated UpdatePartnerModelMutation mutations = 3;
}

message SearchPartnerModelsFilters {
  // Search term to filter out partners by partner_id and name
  string search_term = 1;
    // Partner's subscription tier
  string subscription_tier_id = 2;
}

message SearchPartnerModelsRequest {
  // The cursor for the next result
  string cursor = 1;
  // Page size for the response
  int64 page_size = 2;
  // Field mask for response
  vendastatypes.FieldMask field_mask = 3;
  // Search term to filter out partners by partner_id and name
  SearchPartnerModelsFilters filters = 4;
}

message SearchPartnerModelsResponse {
  // List of partners
  repeated PartnerModel partners = 1;
  // The cursor for paging
  string next_cursor = 2;
  // Whether there are more results
  bool has_more = 3;
}

service PartnerModelService {
  // GetPartnerModel returns the partner
  rpc GetPartnerModel(GetPartnerModelRequest) returns (GetPartnerModelResponse){
    option (vendastatypes.access) = {
      scope: "partner:read"
    };
  }
  // GetMultiPartnerModel returns a list of partners
  rpc GetMultiPartnerModel(GetMultiPartnerModelRequest) returns (GetMultiPartnerModelResponse){
    option (vendastatypes.access) = {
      scope: "partner:read"
    };
  }
  // UpdatePartnerModel updates the partner
  rpc UpdatePartnerModel(UpdatePartnerModelRequest) returns (google.protobuf.Empty);
  // SearchPartnerModels returns a list of partners matching the search criteria
  rpc SearchPartnerModels (SearchPartnerModelsRequest) returns (SearchPartnerModelsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
}

message GetSingleSignOnRequest {
  string user_id = 1;
}

message GetSingleSignOnResponse {
  // Canny SSO Token
  string token = 1;
  // Static ID of our Company in Canny
  string company_id = 2;
  // Location to view our roadmap (Canny's website)
  string roadmap_domain = 3;
}

service CannyService {
  rpc GetSingleSignOn(GetSingleSignOnRequest) returns (GetSingleSignOnResponse);
}

message TriggerEventRequest {
  // Metadata for the event, string key value pairs
  // Can be changed based on the automation event
  google.protobuf.Struct event_data = 1;
  // Type of event we want to trigger
  string event_type = 2;
  // Partner ID of the partner we want to trigger the event for
  string partner_id = 3;
}

service GrowthEventService {
  rpc TriggerEvent(TriggerEventRequest) returns (google.protobuf.Empty);
}
