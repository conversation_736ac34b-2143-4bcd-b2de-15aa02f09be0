# Partner API Changelog
## 1.213.0
- Add `SignUpAgent` to `PartnerSignup` service

## 1.212.0
- Add `show_crm_custom_objects` to `BusinessCenterConfiguration`

## 1.211.0
- Add `GetPartnerLeads` rpc to `Partner` service

## 1.210.1
- Change flag `should_cancel` to `cancel_all_products` in `ResetPartnerRequest` message

## 1.210.0
- Add `should_cancel` flag to the `ResetPartnerRequest` message

## 1.209.0
- Add `number_of_clients` to `PartnerAcquisitionData`

## 1.208.0
- Add `industry` to `PartnerAcquisitionData`

## 1.207.0
- Add `show_inbox_widget_footer_content` to `BusinessCenterConfiguration`
- Add `show_inbox_widget_footer_content` to `BusinessCenterMarketConfiguration`

## 1.206.0
- Add `show_lead_scoring` to `BusinessCenterConfiguration`

## 1.205.0
Fixing duplicate version

## 1.204.0
- Add `show_ai_assistant` to `BusinessCenterConfiguration`

## 1.204.0
- Add `use_case` to `PartnerAcquisitionData`

## 1.203.0
- Add `DeletePartner` rpc to `PartnerLegacy` service

## 1.202.0

- Add `show_leaderboard` to `BusinessCenterConfiguration`

### 1.201.1
- add `partner_id` to `GrowthEvent` service in `api.proto`

### 1.201.0
- add `GrowthEvent` service to `api.proto`

## 1.200.0
- add `disable_product_switcher` to `Configuration`

## 1.199.0
- remove `edit_profile_location` from `Configuration`
- remove `edit_user_profile_custom_link` from `Configuration`
- remove `edit_user_profile_name` from `Configuration`

## 1.198.0
- Add ExitLinkConfiguration to `Configuration`

## 1.197.0

- Add `edit_profile_location` to `Configuration`
- Add `edit_user_profile_custom_link` to `Configuration`
- Add `edit_user_profile_name` to `Configuration`

## 1.196.0

- Add `business_app_ui_theme` field to `BrandingV2`

## 1.195.1

- Improve documentation for `GetMarketConfiguration`

## 1.195.0

- Add `show_dynamic_lists` to `BusinessCenterConfiguration`

## 1.194.0

- Add `USER_PREFERENCE` value to `UITheme` enum in `BrandingV2`

## 1.193.0

- Add `ListMarketsForUser` rpc to `PartnerLegacy` service

## 1.192.0

- Add `show_crm_opportunities` to `BusinessCenterConfiguration`

## 1.191.0

- microsoft access token changed to microsoft id token

## 1.190.0

- Add `disable_business_nav` to `Configuration`

## 1.189.1

- Fixed response for `GetMicrosoftProfile` rpc

## 1.189.0

- Added `GetMicrosoftProfile` rpc, and `MicrosoftTokenCredential` option of `SignUpCredential`

## 1.188.2

- Added `dark_mode_logo_url` to `BrandingV2`

## 1.188.1

- Add `i18n_key` to `NavigationItem` and `NavigationItemChild`

## 1.188.0

- Add `crm_company_id` to `CompanyProfile`

## 1.187.0

- Add `visitor_id` to `IsEmailInUseRequest` rpc in `PartnerSignup` service

## 1.186.1

- Add `show_automations` to `BusinessCenterConfiguration` and `BusinessCenterMarketConfiguration`

## 1.186.0

- Add `show_automations` to `BusinessCenterConfiguration`

## 1.185.0

- Add `IsEmailDisposable` rpc to `PartnerSignup` service

## 1.184.0

- Remove `show_field_management` from `BusinessCenterConfiguration`

## 1.183.0

- Add `show_field_management` to `BusinessCenterConfiguration`

## 1.182.0

- Add subscription_tier_id to search partner models

## 1.181.0

- Add `ms_chat_widget_enabled` to `Configuration`

## 1.180.0

- Add `InitiateSubscriptionTierUpdate` rpc to Partner

## 1.179.0

- Add `business-app` scope to `GetUserAgreementTermsOfService`, `SetUserAgreementTermsOfService` and `GetTermsOfService`

## 1.178.0

- Add `business-app` scope to `GetMarketConfiguration`

## 1.177.0

- Removed `SendForgotPasswordEmail` RPC and `SendForgotPasswordEmailRequest`

## 1.176.0

- Add `business-app` scope to `GetConfiguration`, `BatchGetStatus`, and `GetBranding`

## 1.175.0

- Add `show_custom_forms` to `BusinessCenterConfiguration`

## 1.174.0

- Add `show_crm_tasks` to `BusinessCenterConfiguration`
-

## 1.173.0

- Add `show_crm_companies` to `BusinessCenterConfiguration`

# 1.172.0

- Deprecate the customer survey service and all of it's RPCs. This was never fully released and is being turned down.

# 1.171.0

- Remove useronboarding rpcs that is not used anymore.

# 1.170.0

- added `NavigationItemBadge` to `NavigationItem` to allow for badges to be added to the navigation items
  - currently the only supported badge is a "new" badge used to highlight newly released features

# 1.169.0

- Add `automations_per_month` to the Usage limits of subscription tiers.

# 1.168.0

- Add the new fields from 1.167 to the `BusinessCenterMarketConfiguration`

# 1.167.2

- `custom_guides_inline_title` is supposed to be boolean, not string

# 1.167.1

- Move the fields that were added to `BusinessCenterMarketConfiguration` in 1.167.0 to `BusinessCenterConfiguration`.
  - This is technically a breaking change. There should be no consumers of 1.167.0.

# 1.167.0

- Add several more fields from vconfig to the `BusinessCenterMarketConfiguration`

## 1.166.0

- Add `edit_user_profile_name` field to BusinessCenterMarketConfiguration

## 1.165.0

- Expose `expiry_start`, `expiry_finish` and `is_expired` on `FeatureFlag`

## 1.164.1

- Fix field numbers

## 1.164.0

- Add `UpdatePartnerAdmin` to `IAM` service

## 1.163.0

- Add `edit_user_profile_location` and `edit_user_profile_custom_link` fields to BusinessCenterMarketConfiguration

## 1.162.0

- Add `task_manager.proto` to `api.proto` in `partner.v1`
- Update config name as `tm_show_inbox` in `task_manager.proto`

## 1.161.1

- Fix a typo in `task_manager.proto`

## 1.161.0

- Add `task_manager.proto` to partner.v1 for task manager configurations

## 1.160.0

- Add `Expiry` to `FeatureFlag`, `CreateFeatureFlagRequest`, and
  `UpdateFeatureFlagRequest`.

## 1.159.0

- Add `SearchPartnerModels` to `PartnerModelService` service

## 1.158.0

- Add `owner_name` to Feature flags

## 1.157.0

- Add `GetMulti` to `PartnerModel` to allow fetching multiple partner models in one request

## 1.156.0

- Add `field_mask` to `SetSalesConfigRequest` to allowed filtering what fields are accepted on the request

## 1.155.0

- Add `st_show_inbox` to `SalesConfigInputs`

## 1.154.0

- Add `st_show_inbox` to `SalesConfiguration`

## 1.153.0

- Add `SUBSCRIPTION_TIER_UPDATE_TYPE_PAID_TO_PAID` to `SubscriptionTierUpdateType`

## 1.152.0

- Add `next_url` to `SignUpRequest`

## 1.151.0

- Add `roadmap_domain` to `GetSingleSignOnResponse`

## 1.150.0

- Add `CannyService` and `GetSingleSignOn` rpc

## 1.149.0

- Add `partner:read` scope to `GetPartnerAccountGroupMapping` endpoint

## 1.148.0

- Remove `SetupOnboardingAccountGroup` in `UserOnboarding`

## 1.147.0

- Add recaptcha token to `IsEmailInUseRequest`
  - Support both v2 and v3 tokens

## 1.146.0

- Add deprecation comment for `subscription_tier_id` on the `Configuration` message

## 1.145.0

- Add new `CompareSubscriptionTiers` to `SubscriptionTiers` server to provide an intent-driven data source for the
  Subscription Plan comparison table.

## 1.144.1

- Change NavigationItem to not refer to itself as python cannot handle that

## 1.144.0

- Add `GetPartnerCenterNavigation` to the `PartnerLegacy` service

## 1.143.0

- Remove `PricingPage` service and its `ListPricingPage` endpoint
  - Is unused and no longer needed

## 1.142.1

- Add `TIME_RANGE_UNSPECIFIED` to time range.

## 1.142.0

- Add `monthly_subscription_skus` to `SubscriptionUpdate` message

## 1.141.0

- Rename `allow_sending_notifications` to `notifications_enabled`

## 1.140.0

- Add missing field `allow_sending_notifications` to business app market config
- Add missing field `allow_sending_notifications` to business app config

## 1.139.0

- Add missing field `hide_create_account` to business app market config
- Add missing field `hide_create_account` to business app config.

## 1.138.0

- Add `business_app_name` to the business app market config, to allow for that field to be updated.

## 1.137.1

- Add missing field `multi_location_default_time_range` to business app market config

## 1.137.0

- Add missing field `multi_location_default_time_range` to business app config.

## 1.136.0

- add missing config fields to the merged whitelabel configuration:
  `show_customer_journey`
  `show_onboarding_video`
  `onboarding_video_url`
  `show_marketing_funnel`
  `show_custom_guides`
  `custom_guides_tags`
  `custom_guides_url`
  `send_onboarding_campaign`
  `onboarding_campaign_id`
  `show_recommendations_banner`
  `single_location_default_time_range`
  `show_google_search_console_cta`
  `is_business_profile_editable`
  `allow_smb_chat_with_partner`
  `show_word_press_sales_orders`
  `global_notification_expiry_time`
  `show_listing_score`

## 1.135.0

- Add missing field `show_listing_score` to business app config.

## 1.134.0

- Deprecate `start_on_tutorial_campaign` field on the `CreatePartnerRequest`
  - Field has no functionality behind it. If we want new partners to be added to a campaign, we can use automations

## 1.133.0

- Add the following missing fields to the business app config:
  `show_customer_journey`
  `show_onboarding_video`
  `onboarding_video_url`
  `show_marketing_funnel`
  `show_custom_guides`
  `custom_guides_tags`
  `custom_guides_url`
  `send_onboarding_campaign`
  `onboarding_campaign_id`
  `show_recommendations_banner`
  `single_location_default_time_range`
  `show_google_search_console_cta`
  `is_business_profile_editable`
  `allow_smb_chat_with_partner`
  `show_website_pro_sales_orders`

## 1.132.0

- Add `GetMarketConfiguration` and `UpdateMarketConfiguration` to be able to get and update Business App market configs for Partners.
- Deprecated `SetConfiguration` in favour of `UpdateMarketConfiguration`.

# 1.131.0

- Add `ListPartnerAddons` to `Addons` service

# 1.130.0

- Remove unused `UpsertFreeTrialStatus` and `UpsertCancelFreeTrialReason` endpoints

# 1.129.0

- Add generic `GetFreeTrial` endpoint to `FreeTrialService`

# 1.128.0

- Deprecated `show_my_listing_in_business_center` from `BusinessCenterConfiguration`

# 1.127.1

- Reorder definitions so python generation works

# 1.127.0

- Add `UpdateFreeTrial` and mutations `UpdateFreeTrialActionRequiredStatusMutation`, `UpdateCancelFreeTrialReasonMutation`

# 1.126.0

- Add `show_dashboard` and `show_inbox_message` to `BusinessCenterConfiguration`

# 1.125.0

- Remove `origin` from `PartnerModel`

# 1.124.0

- Remove `logo` from `PartnerModel`

# 1.123.1

- Remove `show_my_meetings` due to duplication

# 1.123.0

- Add `show_order_page`, `show_invoices`, `show_my_meetings`, `show_files`, `show_my_products`, `show_customers`
  and `show_recent_activity`to `BusinessCenterConfiguration`

# 1.122.0

- Add `fuzzy matching` to reputation configuration

# 1.121.0

- Add `partner:read` scope to `GetPartnerModel`

# 1.120.0

- Add `show_fulfillment` to `BusinessCenterConfiguration`

# 1.119.0

- Add `allow_free_trial` to `SubscriptionTier`

# 1.118.0

- Add `ListBranding` to `BrandingV2Service`

# 1.117.0

- Add `ResendPartnerAdminWelcomeEmail` rpc for resending the welcome email to partner admins

# 1.116.0

- Add scope to `ListUseCases`, `ListTasks` and `GetMultiUserTaskStatuses`

# 1.115.0

- Remove `Notes` service

# 1.114.0

- Add `Notes` service with CRUD operations

# 1.113.0

- Add `GetSignUpAddPaymentCardResult` to `PartnerLegacy` service.

# 1.112.0

- Add `allow_self_upgrade` to `Subscription_Tier`

# 1.111.0

- Add `monthly_fee` and `onboarding_fee` to `TierUsageLimits`

# 1.110.0

- Add `SUBSCRIPTION_TIER_UPDATE_TYPE_FREE_TO_FREE_TRIAL` to `SubscriptionTierUpdateType`

# 1.109.0

- Add `SUBSCRIPTION_TIER_UPDATE_TYPE_CANCEL_FREE_TRIAL_FAILED_PAYMENT` to `SubscriptionTierUpdateType`

# 1.108.0

- Combine country and state on `UpdatePartnerModelMutation`

# 1.107.0

- Add `UpsertFreeTrialStatus` to `FreeTrialService`

# 1.106.0

- Add `PartnerModel` crud service

# 1.105.0

- Move where brandingV2 api messages are defined

# 1.104.0

- Add `disable_business_nav` to `BusinessCenterConfiguration`

# 1.103.0

- Add `collected_signup_data_id` to `AcquisitionData`
- Add `collected_signup_data_id` to `PartnerAcquisitionData`

# 1.102.0

- Add subscription tier update type to the `UpdateSubscriptionTierRequest`

# 1.101.0

- Add `UpsertCancelFreeTrialReason` rpc

# 1.100.0

- Mark brandingV1 rpc and request messages as deprecated
- BrandingV2 is the new source of truth for partner branding info

# 1.99.2

- Reorder message declarations for python sdk

# 1.99.1

- Rename `FreeTrailService` to `FreeTrialService`

# 1.98.1

- Improve `FreeTrailService` description

# 1.98.0

- Add `FreeTrailService`

# 1.97.0

- Add `referrer_id` to `PartnerAcquisitionData`
- Add `AcquisitionDataService` rpc service

# 1.96.0

- Add `subscription_tier_id` and `stripe_token` to Create Partner and Sign up Requests protos

# 1.95.0

- Refactor `SelfSignUp`, `SelfSignUpWithGoogle`, `SelfSignUpWithLinkedIn`, `SelfSignUpExistingAccountGroup` rpc into a unique `SignUp` rpc with support for different credentials and operations

# 1.94.0

- Add `name` to BrandingV2 protos

# 1.93.0

- Add tags to `partnerInfo` in `PartnerLegacy`

# 1.92.0

- Add `SendForgotPasswordEmail` in `IAM`

# 1.91.0

- Add `SetupOnboardingAccountGroup` in `UserOnboarding`

# 1.90.0

- Add `BrandingV2` rpc service

# 1.89.0

- Use enum for support flags for `UpdatePartner` in `PartnerLegacy`

# 1.88.0

- add `DeleteWizardStatus` rpc to `UserOnboarding` service

# 1.87.0

- add `ListWizardStatus` rpc to `UserOnboarding` service

# 1.86.1

- remove `ACTION_CUSTOMIZE_TEXT` from Action enum

# 1.86.0

- add `ACTION_CUSTOMIZE_TEXT` to Action enum

# 1.85.0

- add:
  - `ReplaceWizardStatus` rpc
  - `GetWizardStatus` rpc

# 1.84.0

- add `GetUpdatePartnerStatus`to `PartnerLegacy`

# 1.83.1

- update `UpdatePartnerRequest` to include a `PartnerInfo` message

# 1.83.0

- add `UpdatePartner` rpc to `PartnerLegacy`

# 1.82.0

- add `LinkedInTokenCredential` option to `LoginRequest`
- add `SelfSignUpWithLinkedIn` rpc

# 1.81.0

- add recaptcha v3 token as option alongside recaptcha v2, in:
  - `SelfSignUpRequest`
  - `SelfSignUpWithGoogleRequest`
  - `SelfSignUpExistingAccountGroupRequest`

# 1.80.0

- add `GetLinkedInProfile` rpc

# 1.79.0

- add `user_credential` field to `SelfSignUpExistingAccountGroupRequest` message
- remove `user_email` and `user_password` from `SelfSignUpExistingAccountGroupRequest` message

# 1.78.0

- merge `GetRedirectURLForSelfSignupWithGoogle` operation into `Login`
  - add credentials(`EmailPasswordCredential` and `GoogleTokenCredential`) options to `Login`
  - remove `GetRedirectURLForSelfSignupWithGoogle`

# ~~2.0.0~~ 1.77.0

- add `GetGoogleProfile` rpc to `PartnerSignup` service
- remove `google_code` and `redirect_uri` to `SelfSignUpWithGoogleRequest`
- add back `google_token` to `SelfSignUpWithGoogleRequest`
- ps: temporarily this version was called 2.0.0, but since the feature was not delivered, it was agreed to follow a 1.x.x versioning.

# 1.76.0

- add `google_code` to `SelfSignUpWithGoogleRequest` message
- deprecate `google_token` from `SelfSignUpWithGoogleRequest` message
- add `SelfSignUpWithGoogleResponse` as return of `SelfSignUpWithGoogle`

# 1.75.0

- remove/rollback `GetGoogleProfile` from `PartnerSignup` service

# 1.74.0

- add `GetGoogleProfile` rpc to `PartnerSignup` service

# 1.73.0

- add `Login` rpc to `PartnerSignup` service

# 1.72.0

- add `SelfSignUpExistingAccountGroup` rpc to `PartnerSignup` service

# 1.71.0

- add `account_group_partner_id` field to `GetPartnerAccountGroupMappingRequest`

# 1.70.0

- add `GetRedirectURLForSelfSignupWithGoogle` rpc to `PartnerSignup` service

# 1.69.0

- add `GetSelfSignUpStatus` rpc to `PartnerSignup` service

# 1.68.0

- add `SelfSignUpWithGoogle` rpc to `PartnerSignup` service

# 1.67.0

- add `idempotent_key` to `SelfSignUpRequest`

# 1.66.0

- add `GetCreatePartnerResult` rpc to `PartnerLegacy` service

# 1.65.0

- add `BillingFrequency` to `UpdateSubscriptionTierRequest`

# 1.64.0

- Add `SelfSignUp` rpc to `PartnerSignup` service

# 1.63.1`

- `number_of_employees` is a string for `CreatePartner`

# 1.63.0

- Add `subdomain` and remove `password` from `CreatePartner`

# 1.62.0

- Mark `segmented_onboarding_configuration` as deprecated

# 1.61.0

- Add configuration rpcs to `UserOnboarding` service
  - `CreateUserOnboardingConfiguration`
  - `GetUserOnboardingConfiguration`
  - `DeleteUserOnboardingConfiguration`
  - `UpdateUserOnboardingConfiguration`

# 1.60.1

- Add missed field `number_of_employees` for `CreatePartner`

# 1.60.0

- Add `UpdateCompanyProfileRequest` rpc to the `PartnerLegacy` service

# 1.59.0

- Add `PartnerLegacy` service with `CreatePartner` rpc

# 1.58.0

- Add `CreatePartnerAccountGroup` rpc

# 1.57.0

- Add `show_invite_team` to `BusinessCenterConfiguration`

# 1.56.0

- Add `DeletePartnerAccountGroup`

# 1.55.1

- Add `ssc_whitelabeled` to `SalesConfiguration` message / typo in version 1.55.0

# 1.55.0

- Add `ssc_whilabeled` to `SalesConfiguration` message / typo in version 1.51.0

# 1.54.0

- Add `account_group_partner_id` to `GetPartnerAccountGroupMappingResponse` message

# 1.53.0

- Add `PreviewEmailService` service

# 1.52.0

- Add `SwapPartnerAccountGroup` and `UpdatePartnerAccountGroup` rpcs to `Partner` service

# 1.51.0

- Add `ssc_whilabelled` to `SalesConfiguration` message

# 1.50.0

- Add `GetPartnerAccountGroupMapping` rpc

# 1.49.0

- Add `GetMultiBranding` rpc to `Whitelabel` service

# 1.48.1

- Updating parameters in `CreateAnalyticsDashboard` rpc to `FeatureFlags` service

# 1.48.0

- Add `CreateAnalyticsDashboard` rpc to `FeatureFlags` service

# 1.47.0

- Add `show_my_listing_in_business_center` to `BusinessCenterConfiguration`

# 1.46.0

- Add `reply_email` to `EmailConfiguration`

# 1.45.0

- Add `SubscriptionUpdate` to `SubscriptionTier`

# 1.44.0

- Add RPC for `PartnerCanDoAction`

# 1.43.0

- add email to `CustomerSurvey` requests

# 1.42.0

- Add segmented onboarding to the `GetConfigurationResponse`.

# 1.41.0

- Add additional filters to the list features `ListFeatureFlagRequest`, and return total results with reply.

# 1.40.0

- Add `show_social_grade_for_smb_exec_report` to `BusinessCenterConfiguration`

# 1.39.0

- Add `user_id` to `GenerateAccountGroupSessionRequest`

# 1.38.0

- Add Reward Service

# 1.37.0

- Add `snapshot_report_name` to SnapshotConfiguration

# 1.36.0

- Add `show_get_started` to `BusinessCenterConfiguration`

# 1.35.0

- Add `show_review_grade_for_smb_exec_report` to `BusinessCenterConfiguration`

## 1.34.0

- Add `hide_smb_academy` to `BusinessCenterConfiguration` message

## 1.33.0

- Add "AddPartnerToWhitelist" rpc

## 1.32.0

- add "hide_quickbooks_connector" field to "BusinessCenterConfiguration" message

## 1.31.0

- add optional "partner_id" parameter to "GetSubscriptionTierRequest" and "ListSubscriptionTiersRequest"

## 1.31.0

- Add SEOAdvertisingDataProviderr to the snapshot configuration

## 1.30.0

- Add "PartnerSignp" service "IsEmailInUse" endpoint

## 1.29.0

- Add `referral_link` to `BusinessCenterConfiguration`

## 1.28.0

- Added Snapshot and Toolkits to the usage limits of subscription tiers

## 1.27.0

- Added global message and expiry date to business center configuration

## 1.26.0

- Add MailingConfiguration to Configuration

## 1.25.0

- Add GetMulti to PartnerHealth service

## 1.24.0

- Deprecate SubscriptionFailure in Configuration to promote PartnerHealths

## 1.23.0

- Add in_retention to PartnerHealth

## 1.22.0

- Add EmailConfiguration to Configuration

## 1.21.0

- Added new service for sales data

## 1.20.0

- Add PartnerHealths service

## 1.19.0

- Add subscription_failure to Configuration

## 1.18.0

- Add Theme to Branding

## 1.17.0

- Add FeatureFlags service

## 1.16.0

- Add adintel_is_enabled, ms_always_enabled to Configuration

## 1.15.0

- Add st_business_center_access to SalesConfiguration

## 1.14.0

- Add email, write_access, and delete_access to GenerateAccountGroupSessionRequest

## 1.13.0

- Add has_brands_enabled and has_product_marketplace to GetConfiguration response

## 1.12.0

- Add BusinessCenterConfiguration to GetConfiguration response

## 1.11.0

- Always return the partners name in the branding data

## 1.10.0

- Add hide_rm_demo to SalesConfiguration

## 1.10.0

- Add sales_configuration to Configuration

## 1.9.0

- Add social_profile_group_id and reputation_configuration to Configuration

## 1.8.1

- Add subscription_description and subscription_tagline to pricingpage response

## 1.8.0

- Add Partner Reset RPC

## 1.7.1

- Add pricing_blurb field to addons and services response

## 1.7.0

- Add ListPricingPageRequest to Pricing page list RPC

## 1.6.0

- Add Pricing page features service & RPCs

## 1.5.0

- Add GetActivation to Addon service
- Add Status to AddonActivation message

## 1.4.0

- Add partner id and addon id to DeactivateAddonRequest

## 1.3.0

- Add Addon service & RPCs

## 1.2.0

- Add page size and cursor to ListTrial request

## 1.1.0

- Bring back old fields in GetTrial response as deprecated to smooth the transition to new field

## 1.0.0

- Refactor GetTrialResponse to return Trial, add ListTrial RPCs

## 0.5.0

- Add subscription_level to configuration

## 0.4.0

- Add PartnerTrial Service with GetTrial, CreateTrial, and CancelTrial RPCs

## 0.3.0

- Add enabled_features to the returned partner configuration.

## 0.2.0

- Deleted unused partner proto
- Whitelabel Service with GetBranding RPC for partner market context, containing only the major branding data (theme, color, logos/icons, app names)

## 0.1.0

- Add CreatePartner rpc.
