syntax = "proto3";

package partner.v1;

option go_package = "github.com/vendasta/generated-protos-go/partner/v1;partner_v1";
option java_outer_classname = "PartnerSignupProto";
option java_package = "com.vendasta.partner.v1.generated";

import "partner/v1/partner_legacy.proto";

message IsEmailInUseRequest {
  // the email address we are checking against
  string email = 1;
  // Recaptcha token
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_v2_token = 2;
    // Recaptcha v3 token
    string recaptcha_v3_token = 3;
  }
  // visitor id to track the request
  string visitor_id = 4;
}

message IsEmailInUseResponse {
  // returns a list of subjects that are using the email. (empty array means its not in use)
  repeated string subjects = 1;
}

message IsEmailDisposableRequest {
  // the email address we are checking against
  string email = 1;
  // Recaptcha token
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_v2_token = 2;
    // Recaptcha v3 token
    string recaptcha_v3_token = 3;
  }
}

message IsEmailDisposableResponse {
  // returns true if the email is disposable
  bool is_disposable = 1;
}

// Self Sign Up Request captures all the same fields that the AA PartnerSelfSignupApi internal handler needs
message SelfSignUpRequest {
  option deprecated = true;
  // Key to ensure no repeat requests are processed
  string idempotent_key = 1;
  // Initial user created for partner
  PartnerUser initial_user = 2;
  // Website opt out flag controls whether website address should be required for sign up or not
  bool website_opt_out_flag = 3;
  // Billing currency 3 letter code
  string billing_currency_code = 4;
  // Company Profile (NAP Data)
  CompanyProfile company_profile = 5;
  // Acquisition Data
  PartnerAcquisitionData acquisition_data = 6;
  // Recaptcha token
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_response = 7;
    // Recaptcha v3 token
    string recaptcha_v3_token = 10;
  }
  // Terms of Service accepted
  TermOfService terms_of_service = 8;
  // Initial password for user in self sign up - should only be used for self sign up
  string user_password = 9;
}

// Self Sign Up With Google Request captures all the same fields that the AA PartnerGoogleSignupApi internal handler needs
message SelfSignUpWithGoogleRequest {
  option deprecated = true;
  // Key to ensure no repeat requests are processed
  string idempotent_key = 1;
  //google's token to fetch user information
  string google_token = 2;
  // vendasta information about the partner
  PartnerAcquisitionData acquisition_data = 4;
  // information about the partner
  CompanyProfile company_profile = 5;
  // Website opt out flag controls whether website address should be required for sign up or not
  bool website_opt_out_flag = 6;
  // Recaptcha token
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_response = 7;
    // Recaptcha v3 token
    string recaptcha_v3_token = 10;
  }
  // Billing currency 3 letter code (USD, CAD, AUD)
  string billing_currency_code = 8;
  // accepted terms of servce
  TermOfService terms_of_service = 9;
}

// Self Sign Up With LinkedIn Request captures all the same fields that the AA needs + linkedin access token
message SelfSignUpWithLinkedRequest {
  option deprecated = true;
  // Key to ensure no repeat requests are processed
  string idempotent_key = 1;
  //linkedin's token to fetch user information
  string linkedin_token = 2;
  // vendasta information about the partner
  PartnerAcquisitionData acquisition_data = 3;
  // information about the partner
  CompanyProfile company_profile = 4;
  // Website opt out flag controls whether website address should be required for sign up or not
  bool website_opt_out_flag = 5;
  // Recaptcha token
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_response = 6;
    // Recaptcha v3 token
    string recaptcha_v3_token = 7;
  }
  // Billing currency 3 letter code (USD, CAD, AUD)
  string billing_currency_code = 8;
  // accepted terms of servce
  TermOfService terms_of_service = 9;
}

// GetSelfSignUpStatusRequest is a container for check partner creation status
message GetSelfSignUpStatusRequest {
  // Unique key that was used in the SelfSignUpRequest
  string idempotent_key = 1;
}

// GetSelfSignUpStatusResponse is the response container for check partner creation status
message GetSelfSignUpStatusResponse {
  // Status of the create partner work
  Status status = 1;
  // ID of the partner if status is complete
  string partner_id = 2;
  // Error message if status is error
  string error_message = 3;
}

message UserCredential {
  // new user with information about first name, last name, and email
  PartnerUser user = 1;
  // password for new user
  string password = 2;
}

message EmailPasswordCredential{
  // email of the new partner
  string email = 1;
  // password of the new partner
  string password = 2;
}

message GoogleTokenCredential{
  // google token to form the redirect url
  string google_token = 1;
}

message LinkedInTokenCredential{
  // linkedin token to form the redirect url
  string linkedin_access_token = 1;
}

message MicrosoftTokenCredential {
  // microsoft token to form the redirect url
  string microsoft_id_token = 1;
}

// SelfSignUpExistingAccountGroupRequest
message SelfSignUpExistingAccountGroupRequest {
  option deprecated = true;
  // Key used to poll the status of the creation workflow
  string idempotent_key = 1;
  // Recaptcha response
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_response = 2;
    // Recaptcha v3 token
    string recaptcha_v3_token = 7;
  }
  // Terms of Service accepted
  TermOfService terms_of_service = 3;
  // Acquisition Data
  PartnerAcquisitionData acquisition_data = 4;
  //user information to be used for login
  oneof user_credential {
    GoogleTokenCredential google_token = 5;
    EmailPasswordCredential initial_user = 6;
  }
}

// Login Request for logging into Partner Center after using the self sign up form
message LoginRequest {
  // This operation supports logging in with email/password credential or
  // google/linkedin/microsoft token credential.
  oneof credential {
    EmailPasswordCredential email_password_credential = 1;
    GoogleTokenCredential token_credential = 2;
    LinkedInTokenCredential linkedin_credential = 5;
    MicrosoftTokenCredential microsoft_credential = 6;
  }
  // the next url after being logged in to Partner Center
  string next_url = 3;
  // Partner ID of new user logging in
  // If the partnerID is ommitted, this will trigger a fetch to database
  // to find the partnerID, when using with token credential, this will also trigger
  // a request to get the google profile to retrieve the user email, so ideally
  // the partnerID should be passed as parameter.
  string partner_id = 4;
}

// Login Response for logging in to Partner Center.
message LoginResponse {
  // the next url to redirect the user to for their new session
  string next_url = 1;
}

// Container for a Google Code, obtained during a signup with a google account
message GetGoogleProfileRequest {
  // this google code is provided by google in return to a signup attempt
  string google_code = 1;
  // the intermdiate access token is generated for a specific redirect url
  string redirect_url = 2;
}

// Container for a google profile obtained by calling https://oauth2.googleapis.com/tokeninfo
// the field list is choosen accordingly to https://developers.google.com/identity/sign-in/web/backend-auth
// in adition to a token provided by https://oauth2.googleapis.com/token
message GetGoogleProfileResponse {
  string email = 1;
  string email_verified = 2;
  string name = 3;
  string picture = 4;
  string given_name = 5;
  string family_name = 6;
  string locale = 7;
  string token = 8;
}

// Request to fetch a linkedin profile and an access token based on a authorization code
message GetLinkedinProfileRequest {
  // authorization_code returned by calling https://www.linkedin.com/oauth/v2/authorization
  string authorization_code = 1;
  // has to be the same used when requesting the authorization_code
  string redirect_url = 2;
}

// Container for a linkedin profile obtained by calling https://api.linkedin.com/v2/me
// in adition to a token provided by https://www.linkedin.com/oauth/v2/accessToken
//
// reference documentation:
//    * https://docs.microsoft.com/en-us/linkedin/consumer/integrations/self-serve/sign-in-with-linkedin
//    * https://docs.microsoft.com/en-us/linkedin/shared/authentication/authorization-code-flow
message GetLinkedinProfileResponse {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string profile_picture_urn = 4;
  string token = 5;
}

// Request to fetch a microsoft profile and an access token based on a authorization code
message GetMicrosoftProfileRequest {
  // authorization_code returned by calling https://learn.microsoft.com/en-us/entra/identity-platform/v2-protocols-oidc#send-the-sign-in-request
  string authorization_code = 1;
  // has to be the same used when requesting the authorization_code
  string redirect_url = 2;
}

// Container for a microsoft profile obtained by calling https://learn.microsoft.com/en-us/entra/identity-platform/userinfo#calling-the-api
// and the access token used for it
message GetMicrosoftProfileResponse {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string picture = 4;
  string id_token = 5;
}

message SignUpCredential {
  // Supported sign up credentials:
  // - google token
  // - user
  // - linkedin token
  // - microsoft id token
  oneof user_credential {
    GoogleTokenCredential google_token = 1;
    UserCredential user = 2;
    LinkedInTokenCredential linkedin_token = 3;
    MicrosoftTokenCredential microsoft_token = 4;
  }
}

message NewAccount {
  SignUpCredential credential = 1;
  CompanyProfile company_profile = 2;
}

message ExistingAccount {
  SignUpCredential credential = 1;
}

message SignUpRequest {
  // Key to ensure no repeat requests are processed
  string idempotent_key = 1;
  // Sign up supported types:
  oneof sign_up_type {
    // New Account (via self sign up, google, or linkedin)
    NewAccount new_account = 2;
    // Existing Account (via self sign up, google, or linkedin)
    ExistingAccount existing_account = 3;
  }
  // Acquisition Data
  PartnerAcquisitionData acquisition_data = 4;
  // Terms of Service accepted
  TermOfService terms_of_service = 5;
  // Recaptcha token
  oneof recaptcha_token {
    // Recaptcha v2 token
    string recaptcha_v2_token = 6;
    // Recaptcha v3 token
    string recaptcha_v3_token = 7;
  }
  // Billing currency 3 letter code
  string billing_currency_code = 8;
  // Website opt out flag controls whether website address should be required for sign up or not
  bool website_opt_out_flag = 9;
  // Subscription Tier ID defaults to free, if a paid tier requires a valid stripe token to setup payment
  string subscription_tier_id = 10;
  // The token to access stripes API used to setup payment card in the billing's merchant services.
  string stripe_token = 11;
  // Next URL where the user will be redirected to with auth at the end of the signup flow
  string next_url = 12;
}

message SignUpAgentRequest {
  // Key to ensure no repeat requests are processed
  string idempotent_key = 1;
  // new user with information about first name, last name, and email
  PartnerUser user = 2;
  // Info about the partner company
  CompanyProfile company_profile = 3;
  // Acquisition Data
  PartnerAcquisitionData acquisition_data = 4;
  // Billing currency 3 letter code
  string billing_currency_code = 5;
}
