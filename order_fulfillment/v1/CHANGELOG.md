## 4.34.0 - 2025-06-25
- Add scopes to `DeactivateAllItemsForBusiness` RPC

## 4.33.0 - 2025-03-10
- Add `DeactivateAllItemsForBusiness` RPC and request message to `OrderFulfillmentService`

## 4.32.0 - 2024-12-03
- add `ORDER_ITEM_STATUS_ACTIVATION_UNSCHEDULED` to `OrderItemStatus` message

## 4.31.0 - 2024-11-07
- Add `CreateOrderForSingleItem` rpc and request/response messages to create an order with a single item

## 4.30.0 - 2024-11-04
- Add `UnscheduleActivation` rpc to unschedule a `ScheduledProvisioningTask` of type "activation"

- ## 4.29.0 - 2024-08-29
- add `ORDER_ITEM_STATUS_ACTIVATION_SCHEDULED` to `OrderItemStatus` message

## 4.28.0 - 2024-08-20
- add `scheduled_activation_date` to `OrderItemParameters` message

## 4.27.0 - 2024-08-19
- Deprecated `partner_id` from `GetMultiActivationDatesRequest`

## 4.26.0 - 2024-08-16
- add `edition_id` to `ScheduledProvisioningTask` message

## 4.25.1 - 2024-08-13
- Make `ListScheduledActivationsForBusiness` paginated

## 4.25.0 - 2024-08-13
- Add new rpc `ListScheduledActivationsForBusiness`

## 4.24.0 - 2024-07-29
- Add new rpc `GetMultiActivationDates`

## 4.23.0 - 2024-06-18
- Add `market_name` field to `BusinessInfo`

## 4.22.0 - 2024-06-03
- Add `DeactivateImmediatelyRequest` request and `DeactivateImmediately` rpc to immediately deactivate an item

## 4.21.0 - 2024-05-21
- Add `scheduled_deactivation_date` field to `OrderItemParameters` rpc

## 4.20.0 - 2024-04-30
- Change response for `ScheduleDeactivation` rpc to empty

## 4.19.0 - 2024-04-23
- Add new rpc `UnscheduleDeactivation` to unschedule an `ScheduledProvisioningTask`

## 4.18.0 - 2024-04-23
- Add new rpc `GetMultiScheduledDeactivationsForBusiness` to get multiple scheduled deactivations as scheduled provisioning tasks for a list of activation IDs

## 4.17.0 - 2024-04-22
- Add new rpc `ScheduleDeactivation` for scheduling an item's deactivation

## 4.16.0 - 2024-02-21
- Add `FULFILLMENT_ORDER_STATUS_ON_HOLD` status to `FulfillmentOrderStatus`

## 4.15.0 - 2024-02-15
- Add `fulfillment_order_ids` to `SearchFulfillmentOrderRequest` message

## 4.14.0 - 2024-01-30
- Add `consumer` and `subscription_id` to OrderItem and OrderItemParameters messages

## 4.13.0 - 2024-01-11
- Add new validation error code to `ValidationErrorCodes` for partner not owner of package

## 4.12.0 - 2024-01-10
- Add new validation error code to `ValidationErrorCodes` for package ID not found

## 4.11.0 - 2023-12-20
- Add `FulfillmentOrderProjects` to `FulfillmentOrder`

## 4.10.0 - 2023-12-19
- Add `has_form` to `SearchFulfillmentOrderRequest`

## 4.9.0 - 2023-10-02
- Add `fulfillment_form_info` to `FulfillmentOrder`

## 4.8.0 - 2023-06-12
- Add `exclude_app_ids` and `exclude_reseller_ids` to `SearchFulfillmentOrderRequest`

## 4.7.0 - 2023-05-23
- Add `activation_ids` to `SearchFulfillmentOrderRequest`

## 4.6.0 - 2023-05-19
- Add `business_id` to `GetFulfillmentOrdersForSalesOrder` rpc

## 4.5.0 - 2023-05-10
- Hydrate Sales Order Form Submission for the SalesInfo if the appropriate ProjectionFilter option is turned on.

## 4.4.0 - 2023-04-12
- Deprecate `quantity` for FulfillmentFormSubmissions and related endpoints.

## 4.3.0 - 2023-04-12
- Add `include_pending_activation_info` to fulfillment order projection filter
- Add `pending_activation_ids` and `pending_activation_info` to fulfillment order

## 4.2.0 - 2023-03-22
- Add `projection_filter` to `GetFulfillmentOrdersForSalesOrder` rpc

## 4.1.0 - 2023-03-21
- Add `market_id` to `business_info` on fulfillment order

## 4.0.0 - 2023-02-21
- Multiple breaking changes and updates to fulfilment order and fulfillment form submission
- FulfillmentOrder:
  - Removed `form_config` field
- FulfillmentFormSubmission:
  - Removed `common_fields` field
  - Removed `Create` rpc
  - Renamed `UpdateFulfillmentFormSubmission` to `UpsertFulfillmentFormSubmission` to better reflect underlying behaviour
  - Added `form_config` field
  - Added `sales_order_id` field
  - Added `app_id` field
  - Added `quantity` field
  - Wrapped `custom_fields` in `form_answers` message to support multiple answer sets per form.
    - This is done to handle multi activatable addons in the same order.

## 3.0.0 - 2023-02-01
- Make breaking changes to add in new status enum structure for `FulfilllmentOrders`.
This should be fine this early in development as long as we coordinate rollout.
- Remove deprecated fields.

## 2.2.0 - 2023-01-24
- Add `FulfillmentOrderServiceV2` with `GetMultiBySalesOrderIds` rpc

## 2.1.0 - 2023-01-16
- Deprecate `CommonFormFields` in `FulfillmentFormSubmission` and its rpc's

## 2.0.0 - 2022-12-16
- Add `UNSET` status to `FulfillmentOrderStatus`, Pending is now 8

## 1.56.0 - 2022-12-05
- Add `ACTION_REQUIRED` and `DECLINED` enums to `FulfillmentOrderStatus`.

## 1.55.0 - 2022-11-09
- Add protos for `FulfillmentFormConfig` and add it to the `FulfillmentForm`.
- Add projection filter support for it.

## 1.54.0 - 2022-11-01
- Non-breaking changes, as we haven't implemented these yet:
  - Update `CreateFulfillmentFormSubmissionResponse` to return only `version`
  - Rename fields from `parent_order_id` to `fulfillment_order_id`

## 1.53.0- 2022-10-31
- Add protos for `FulfillmentFormSubmission` and its related endpoints.

## 1.52.0- 2022-10-25
- Add `reseller_currency` to `ChangeSpendRequestExternalData` and `ChangeRequestEventExternalData`

## 1.51.0- 2022-10-18
- Deprecate `external_form_url` and similar fields on Fulfillment OrdersConfig.

## 1.50.0 - 2022-10-11
- Add `IN_REVIEW` enum to `FulfillmentOrderStatus`.

## 1.49.1 - 2022-09-27
- Move `GetCurrentSpend` endpoint to api.proto

## 1.49.0 - 2022-09-26
- Add the protos for the new `GetCurrentSpend` endpoint 

## 1.48.0 - 2022-09-01
- Add paging to the `GetFulfillmentOrdersForSalesOrder` endpoint. This endpoint hasn't gone into production, so this isn't breaking.

## 1.47.0 - 2022-08-31
- Add the protos for the new `GetFulfillmentOrdersForSalesOrder` endpoint

## 1.46.0 - 2022-08-15
- Add `sales_info` to `FulfillmentOrder`
- Add `projection_filter` to `GetFulfillmentOrderRequest`

## 1.45.0 - 2022-08-12
- Add `HydratedFulfillmentOrder` messages and rpc for searching

## 1.44.0 - 2022-08-02
- Add `total_results` to `SearchFulfillmentOrderResponse`

## 1.43.0 - 2022-07-25
- Add `SearchFulfillmentOrder` to FulfillmentOrderService
- Add created, updated, deleted fields to `FulfillmentOrder`

## 1.42.0 - 2022-07-21
- Added `field_mask` to `UpdateFulfillmentOrderRequest` message

## 1.41.0 - 2022-07-20
- Added `status` to `UpdateFulfillmentOrderRequest` message

## 1.40.0 - 2022-07-18
- Added [Fulfillment Order] service

## 1.39.0
- Remove `fulfillment-order-config` service and messages. It was unimplemented, so is not a breaking change.
## 1.38.0
- Added `fulfillment-order-config` service and messages

## 1.37.0
- Add field `effective_date` to `ChangeSpendRequestEvents` 

## 1.36.0
- Add field `resolved_by` to `ChangeSpendRequestEvents` and field mask

## 1.35.0
- Add field `requesting_user` to `ChangeSpendRequestEvents`

## 1.34.0
- Add fields `Frequency` and `retail_amount` to `OrderItem` and `OrderItemParameters`

## 1.33.0
- Add new rpc `ListChangeSpendRequestHistory` for listing spend change request events

## 1.32.0
- Add `CHANGE_SPEND_REQUEST_STATUS_OVERRIDDEN` status to `ChangeSpendRequestStatus` rpc

## 1.31.0
- Add `reseller_id` to the `ChangeSpendRequest`

## 1.30.0
- Add `billing_frequency` to the `ChangeSpendRequest`

## 1.29.0
- update `ListChangeSpendRequests` endpoint to return the following additional information:
    - business_name
    - reseller_name
    - item_name
    - renewal_date
    - current_subscription_cost

## 1.28.0
- Add `billing_order_id` to the `ChangeSpendRequest`

## 1.27.0
- Changed name of `UpdateChangeSpendRequestStatus` to `ResolveChangeSpendRequest`

## 1.26.0
- Add new RPC `UpdateChangeSpendRequestStatus` for approving/rejecting requests to change spending amount

## 1.25.0
- Refactor Change Spend Requests apis

## 1.24.1
- Add an enum `ChangeSpendRequestStatus` to represent the status of `ChangeSpendRequest`s

## 1.24.0
- Add new RPC `ListChangeSpendRequests` for listing requests to change spending amount

## 1.23.0
- Add `Requester note` to `ChangeSpendRequest`

## 1.22.0
- Add `CustomPrice` to `OrderItem`

## 1.21.0
- Add new RPC `RequestSpendAmountChange` for sending a request to change spending amount.

## 1.20.0
- Add new RPC `GetMultiActivationErrorsForOrderItems` for getting order items activation error details.

## 1.19.0
- Add `CancelOrderItemWaitingForConsumerInformation` RPC

## 1.18.0
- Add a `WaitingForConsumerInformation` status for `OrderItem`s
- Add `RecordOrderFormSubmissionAndActivateOrderItem` RPC

## 1.17.0
- Add `PackageInstanceID` to `OrderItemParameters` and `OrderItem`

## 1.16.0
- Added new validation error code to `ValidationErrorCodes` for suspended apps

## 1.15.0
- Added new validation error code to `ValidationErrorCodes` for required features for partner and apps

## 1.14.0
- Add a `Canceled` status for `OrderItem`s

## 1.13.0
- Add `CancelEditionChange` RPC

## 1.12.0
- Add `charge_failure_code` string field to the `OrderItem` message.

## 1.11.0
- Add `ResolvePendingActivation` RPC

## 1.10.0
- Add `package_id` to the `OrderItem` and `OrderItemParams` to track which package/bundle an `OrderItem` comes from

## 1.9.0
- Add `reasons` to the`OrderItem` message for CreateOrder

## 1.8.0
- Add `DismissFailedOrderItem` RPC and associated request structure to be able to mark a failed order item as dismissed

## 1.7.0
- Add `GetOrderByIdempotencyKey` to be able to get an order by idempotency key
- Add `ListOrderItemsForOrder` to be able to list the order items from the order id

## 1.6.1
- Add `idempotency_key` to `CreateOrder` request, to allow multiple rapid submission of the same order without creating more than one order.

## 1.5.1
- Add comments on `ActivateProduct` and `ChangeEdition` V2 RPCs indicating they are for internal use only

## 1.5.0
- Deprecate `filter_by_statuses` field on `ListOrderItemsForBusinessRequest`
- Remove unused `OrderItemType` enum
- Add `VALIDATION_ERROR_CODES_CANNOT_CHANGE_EDITION_OF_TRIAL` and `VALIDATION_ERROR_CODES_APP_NOT_ACTIVE` entries to `ValidationErrorCodes` enum

## 1.4.0
- Add `EditionChangeInformation` and `ProductActivationInformation` messages containing information required for edition
changes and product activations, which also serve to indicate what the type of order item is
- Add `oneof` field to to `OrderItem` and `OrderItemParameters` messages for type specific information

## 1.3.0
- Add `ListOrderItemsForBusiness` RPC and associated request and response structs
- Add `OrderItemStatus` enum
    - Defines statuses an order item might be in
- Add `OrderItem` struct that represents an order item coming back from the server


## 1.2.0
- Add `ValidationErrorCodes` enum
    - Defines error codes for the reasons an order item could fail validation

## 1.1.0
- Add `order_source` to `Order`, add `edition_id` and `is_trial` to `OrderItemParameters`. Add `Blame` message.

## 1.0.0
- Defined API for `CreateOrder`

## 0.1.0
- Stub for Order Fulfillment service
