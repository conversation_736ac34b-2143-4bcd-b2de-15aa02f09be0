syntax = "proto3";

package orderfulfillment.v1;

option go_package = "github.com/vendasta/generated-protos-go/order_fulfillment/v1;orderfulfillment_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.orderfulfillment.v1.generated";

import "google/protobuf/empty.proto";
import "order_fulfillment/v1/blame.proto";
import "order_fulfillment/v1/order_item.proto";
import "order_fulfillment/v1/order.proto";
import "order_fulfillment/v1/change_spend_request.proto";
import "order_fulfillment/v1/frequency.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/annotations.proto";
import "google/protobuf/timestamp.proto";

// Parameters for an Order Item
message OrderItemParameters {
    // the business id for the order item
    string business_id = 1;
    // the item id for the order item
    string item_id = 2;
    // the submission form id for the order item
    string order_form_submission_id = 3;
    // the optional edition id for the order item
    string edition_id = 4;
    // the flag indicating whether the order item is for a trial or not
    bool is_trial = 5;
    // additional information based on order item type
    oneof additional_information_for_type {
        ProductActivationInformation product_activation_information = 13;
        EditionChangeInformation edition_change_information = 14;
    }
    // the optional package id of the package/bundle containing the order item
    string package_id = 6;
    // the optional custom price that will be propagated to the recurring subscription within billing
    int64 custom_price = 7;
    // the optional instance ID of the package/bundle containing the order item.
    // when a package is purchased in multiples, this ID will differentiate which items belong to which instance of the package
    string package_instance_id = 8;
    // The retail amount for the order item. This is in the smallest currency unit.
    int64 retail_amount = 9;
    // The frequency at which the retail price will be billed.
    Frequency retail_frequency = 10;
    // The subscription ID of the subscription within billing
    string subscription_id = 11;
    // The billing relationship the subscription is for
    Consumer consumer = 12;
    // The scheduled deactivation date assigned to an order item
    google.protobuf.Timestamp scheduled_deactivation_date = 15;
    // The scheduled activation date assigned to an order item
    google.protobuf.Timestamp scheduled_activation_date = 16;
}

// message to request the creation of an order
message CreateOrderRequest {
    // the id of the partner making the order
    string partner_id = 1;
    // the list of items being ordered
    repeated OrderItemParameters order_items = 2;
    // the metadata containing information about who initiated the request
    Blame order_source = 3;
    // Arbitrary but unique string which identifies this particular order
    string idempotency_key = 4;
}

// response message for the successful creation of an order
message CreateOrderResponse {
    // the id of the created order
    string order_id = 1;
}

// list the order items for a given business
message ListOrderItemsForBusinessRequest {
    // the business id to list order items for
    string business_id = 1;
    // the statuses to include in the list response [DEPRECATED - does nothing]
    repeated OrderItemStatus filter_by_statuses = 2 [deprecated=true];
    // field mask to use when filtering list response
    vendastatypes.FieldMask field_mask = 3;
    // cursor should be supplied from the last response to load the next results
    string cursor = 4;
    // the number of results to retrieve
    int64 page_size = 5;
}

// list the order items for a given business
message ListOrderItemsForOrderRequest {
    // the order id to list order items for
    string order_id = 1;
    // cursor should be supplied from the last response to load the next results
    string cursor = 2;
    // the number of results to retrieve
    int64 page_size = 3;
}

// paged list response of order items
message ListOrderItemsResponse {
    // the list of order items returned
    repeated OrderItem order_items = 1;
    // The cursor to send with the next request if there are more results to be loaded
    string next_cursor = 2;
    // Indicates whether or not there are more results to be loaded
    bool has_more = 3;
}

// Get the order, not by the OrderID but by the idempotency key. Technically a query, not a get but as the idempotency
// key is unique this will result in 0 or 1 orders. It is also a query against the primary data source so it will be up
// to date data. The fundamental difference between this and a get by order id is the speed of the response.
message GetOrderByIdempotencyKeyRequest {
    string idempotency_key = 1;
}

message GetOrderByIdempotencyKeyResponse {
    // The order
    Order order = 1;
}

// Request to mark a failed order item as dismissed
message DismissFailedOrderItemRequest {
    // parent order id of the order item
    string order_id = 1;
    // the business id for the order item
    string business_id = 2;
    // the item id for the order item
    string item_id = 3;
    // Unique identifier of the order item
    string order_item_id = 4;
}

// Request to resolve a pending activation
message ResolvePendingActivationRequest {
    // The business ID of the pending activation
    string business_id = 1;
    // The product ID of the pending activation
    string product_id = 2;
    // The ID of the pending activation
    string activation_id = 3;
    // True for approved, False for rejected
    bool approved = 4;
    // Reason for a rejection (Required only if approved is false)
    string rejected_reason = 5;
    // (OPTIONAL) The external account identifier of the successfully activated account
    string account_id = 6;
    // Activation descriptor is displayed in UI to differentiate multiple activations
    string activation_descriptor = 7;
    // (OPTIONAL) force indicator will allow the resulting workflow to execute even if it has previously ran to completion
    bool force = 8;
}

message CancelEditionChangeRequest {
    // Unique identifier of the order item
    string order_item_id = 1;
}

message CancelOrderItemWaitingForConsumerInformationRequest {
  // Unique identifier of the order item
  string order_item_id = 1;
}

message RecordOrderFormSubmissionAndActivateOrderItemRequest {
  // Unique identifier of the order item
  string order_item_id = 1;
  // Unique identifier of the order form submission
  string order_form_submission_id = 2;
}

enum FailureReason {
    FAILURE_REASON_UNKNOWN_ERROR = 0;
    FAILURE_REASON_VALIDATION_ERROR = 1;
    FAILURE_REASON_CHARGE_ERROR = 2;
    FAILURE_REASON_VENDOR_REJECTED_ERROR = 3;
    FAILURE_REASON_ACTIVATION_ERROR = 4;
}

message ActivationFailureDetails {
    // id that identifies an order item.
    string order_item_id = 1;
    // reason code for failure.
    FailureReason failure_reason = 2;
    // list of failure reason details.
    //      * there are activation failures that may return multiple failure details like validation
    //        failure which may return multiple error codes that corresponds to order form fields
    //        that are causing validation to fail.
    //      * details could be numeric values or strings.
    //      * for validation failure, details would be numeric values that corresponds to
    //        order-fulfillment's validation error codes.
    //      * for charge failures, details would be Stripe's error string/code if available.
    //        https://stripe.com/docs/error-codes
    //      * for vendor rejection failure, details would be a freeform text provided by vendors when
    //        they rejected the order item.
    //      * for activation failures, as of writting, more granular error details/codes are not
    //        available or provided. So only the FailureReason will be available.
    //      * details are not guaranteed to be available for all failure reasons as of writting.
    repeated string details = 3;
}

message GetMultiActivationErrorsForOrderItemsRequest {
    // list of order item ids we want to know about.
    repeated string order_item_ids = 1;
}

message GetMultiActivationErrorsForOrderItemsResponse {
    // details about the order item activation failure.
    repeated ActivationFailureDetails failure_details = 1;
}

message ChangeSpendRequestRequest {
  // account group id for this request
  string business_id = 1;
  // order item id, which could be app id or addon id
  string item_id = 2;
  // the activation id, which is specific for this change request
  string activation_id = 3;
  // new spend amount, which a partner requests from the vendor
  int64  requested_spend_amount = 4;
  // requester note for this change request
  string requester_note = 5;
}

message ListChangeSpendRequestsRequest {
  // the number of results to retrieve
  int64 page_size = 1;
  // cursor should be supplied from the last response to load the next results
  string cursor = 2;

  message ListChangeSpendRequestsFilterOptions {
    // List of items to return spend change requests for
    repeated string item_ids = 1;
    // List of businesses to return spend change requests for
    repeated string business_ids = 2;
    // List of which statuses a request should be in to be returned
    repeated ChangeSpendRequestStatus statuses = 3;
  }

  ListChangeSpendRequestsFilterOptions filter_options = 3;
}

message ListChangeSpendRequestsResponse {
  // The list of spend changes returned
  repeated ChangeSpendRequest change_spend_requests = 1;
  // The cursor to send with the next request if there are more results to be loaded
  string next_cursor = 2;
  // Indicates whether or not there are more results to be loaded
  bool has_more = 3;

}

message ResolveChangeSpendRequestRequest {
  // the unique identifier of the spend change request
  string change_request_id = 1;
  // account group id for this request
  string business_id = 2;
  // order item id, which could be app id or addon id
  string item_id = 3;
  // the activation id, which is specific for this change request
  string activation_id = 4;
  // the new status of the change spend request, typically approved or rejected
  ChangeSpendRequestStatus status = 5;
  // the updater's note which explains reasoning for their decision
  string status_update_note = 6;
}

message ListChangeSpendRequestHistoryRequest {
  // the number of results to retrieve
  int64 page_size = 1;
  // cursor should be supplied from the last response to load the next results
  string cursor = 2;

  message ListChangeSpendRequestsHistoryFilterOptions {
    // List of items to return spend change requests for
    repeated string item_ids = 1;
    // List of businesses to return spend change requests for
    repeated string business_ids = 2;
    // List of change request ids
    repeated string change_request_ids = 3;
    // List of which statuses a request should be in to be returned
    repeated ChangeSpendRequestStatus statuses = 4;
  }

  ListChangeSpendRequestsHistoryFilterOptions filter_options = 3;

  // field mask to use when filtering list response
  vendastatypes.FieldMask field_mask = 4;
}

message ListChangeSpendRequestHistoryResponse {
  // The cursor to send with the next request if there are more results to be loaded
  string next_cursor = 1;
  // Indicates whether or not there are more results to be loaded
  bool has_more = 2;

  message ChangeSpendRequestEvents {
    // order item id, which could be app id or addon id
    string item_id = 1;
    // account group id for this request
    string business_id = 2;
    // unique identifier for this change spend request
    string change_request_id = 3;
    // time stamp of the event and status change
    google.protobuf.Timestamp event_time = 4;
    // new status of the event
    ChangeSpendRequestStatus status = 5;
    // external spend change data
    ChangeRequestEventExternalData external_data = 6;
    // user requesting spend change, this will be the email of the user who made the request.
    string requesting_user = 7;
    // user requesting spend change, this will be the email of the user who resolved the request.
    string resolved_by = 8;
    // time stamp of the event and status change
    google.protobuf.Timestamp effective_date = 9;
  }
  // The list of spend change events
  repeated ChangeSpendRequestEvents change_spend_request_events = 3;
}

message GetCurrentSpendRequest {
  // partner id
  string partner_id = 1;
  // account group id for this request
  string business_id = 2;
  // item id, which could be app id or addon id
  string item_id = 4;
  // the unique ID that was assigned to the app/addon upon activation.
  string activation_id = 5;
}

message GetCurrentSpendResponse {
  // the current spend for the product
  int64 spend_amount = 1;
}

message ScheduledProvisioningTask{
  // activation id of the item
  string activation_id = 1;
  // business id of the account
  string business_id = 2;
  // id of the item
  string item_id = 3;
  // status of the scheduled task
  string scheduled_provisioning_task_status  = 4;
  // time that the item the scheduled task should execute
  google.protobuf.Timestamp scheduled_for = 5;
  // id of the edition
  string edition_id = 6;
}

message ScheduleDeactivationRequest {
  // activation id of the item
  string activation_id = 1;
  // business id of the account
  string business_id = 2;
  // id of the item to be deactivated
  string item_id = 3;
  // time that the item should get deactivated
  google.protobuf.Timestamp scheduled_for = 4;
}

message DeactivateImmediatelyRequest {
  // activation id of the item
  string activation_id = 1;
  // business id of the account
  string business_id = 2;
  // id of the item to be deactivated
  string item_id = 3;
}

message GetMultiScheduledDeactivationsForBusinessRequest {
  // list of activation IDs of the scheduled provisioning tasks to get
  repeated string activation_ids = 1;
  // Business ID to get activations for
  string business_id = 2;
}

message GetMultiScheduledDeactivationsForBusinessResponse {
  // list of scheduled provisioning tasks to return
  repeated  ScheduledProvisioningTask scheduled_deactivations = 1;
}

message ListScheduledActivationsForBusinessRequest {
  // Business ID to get activations for
  string business_id = 1;
  // The cursor at which to start the page.
  string cursor = 2;
  // The size of the page to fetch.
  int64 page_size = 3;
}

message ListScheduledActivationsForBusinessResponse {
  // list of scheduled provisioning tasks to return
  repeated  ScheduledProvisioningTask scheduled_activations = 1;
  // The next cursor for which to fetch the next page.
  string next_cursor = 2;
  // The flag indicating whether there is another page.
  bool has_more = 3;
}

message UnscheduleDeactivationRequest {
  // activation id of the item
  string activation_id = 1;
}

message ActivationDateIdentifiers {
  // activation id of the item
  string activation_id = 1;
  // item id of the item
  string item_id = 2;
}

message GetMultiActivationDatesRequest {
  // list of the activation and item IDs of the items to get dates for
  repeated ActivationDateIdentifiers activation_date_identifiers = 1;
  // Partner ID of the items to get the dates for
  string partner_id = 2 [deprecated = true];
  // Business ID of the items to get the dates for
  string business_id = 3;
}

message ActivationDates {
  // the date an item was activated
  google.protobuf.Timestamp activation_date = 1;
  // the next payment date for an item
  google.protobuf.Timestamp renewal_date = 2;
  // The date when the final payment of the subscription's commitment is due
  // (first day of final billing period on a commitement)
  google.protobuf.Timestamp commitment_date = 3;
  // the earliest date and item can be set to deactivate
  google.protobuf.Timestamp earliest_valid_scheduled_deactivation_date = 4;
  // the last day of final billing period of a commitment, when the service for the last payment officially expires
  google.protobuf.Timestamp end_of_commitment_period = 5;
}

message GetMultiActivationDatesResponse {
  repeated ActivationDates activation_dates = 1;
}

message UnscheduleActivationRequest {
  // activation id of the item
  string activation_id = 1;
}

message CreateOrderForSingleItemRequest {
  // the id of the partner making the order
  string partner_id = 1;
// the business id for the order item
  string business_id = 2;
  // the item id for the order item
  string item_id = 3;
  // the id of the edition to be processed
  string edition_id = 4;
  // the flag indicating whether the order item is for a trial or not
  bool is_trial = 5;
  // the submission form id for the order item
  string order_form_submission_id = 6;
  // the metadata containing information about who initiated the request
  Blame order_source = 7;

}

message CreateOrderForSingleItemResponse {
  // unique identifier of the created order
  string order_id = 1;
  // unique identifier of the single order item
  string order_item_id = 2;
  // pregenerated unique identifier for the activation
  string activation_id = 3;
}

message DeactivateAllItemsForBusinessRequest {
  // unique identifier of the business
  string business_id = 1;
}

service OrderFulfillmentService {
    // Create an order for a partner and a list of order items
    rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
    // List order items for a given business
    rpc ListOrderItemsForBusiness(ListOrderItemsForBusinessRequest) returns (ListOrderItemsResponse);
    // Retrieves an order from it's idempotency key
    rpc GetOrderByIdempotencyKey(GetOrderByIdempotencyKeyRequest) returns (GetOrderByIdempotencyKeyResponse);
    // List order items for a given order
    rpc ListOrderItemsForOrder(ListOrderItemsForOrderRequest) returns (ListOrderItemsResponse);
    // Mark a failed order item as dismissed
    rpc DismissFailedOrderItem(DismissFailedOrderItemRequest) returns (google.protobuf.Empty);
    // Resolve a pending activation. This is called by the vendor to approve or reject and activation. The endpoint
    // ensures Accounts and Order Fulfillment are kept in sync with respect to the status of the order item/activation
    rpc ResolvePendingActivation(ResolvePendingActivationRequest) returns (google.protobuf.Empty);
    // Cancel a pending edition change
    rpc CancelEditionChange(CancelEditionChangeRequest) returns (google.protobuf.Empty);
    // Cancel an order item waiting for consumer information
    // Only cancels order items with OrderItemStatus = 10
    rpc CancelOrderItemWaitingForConsumerInformation(CancelOrderItemWaitingForConsumerInformationRequest) returns (google.protobuf.Empty);
    // Record an order form submission for an OrderItem and start activation process for it
    rpc RecordOrderFormSubmissionAndActivateOrderItem(RecordOrderFormSubmissionAndActivateOrderItemRequest) returns (google.protobuf.Empty);
    // Gets activation errors for multiple order items.
    rpc GetMultiActivationErrorsForOrderItems(GetMultiActivationErrorsForOrderItemsRequest) returns (GetMultiActivationErrorsForOrderItemsResponse);
    // Sends the spend amount change request to the vendor
    rpc RequestSpendAmountChange(ChangeSpendRequestRequest) returns (google.protobuf.Empty);
    // Sends the spend amount change request to the vendor
    rpc ListChangeSpendRequests(ListChangeSpendRequestsRequest) returns (ListChangeSpendRequestsResponse);
    // Sends the approval/rejection of the spend amount change request from the vendor
    rpc ResolveChangeSpendRequest(ResolveChangeSpendRequestRequest) returns (google.protobuf.Empty);
    // Lists a spend change request history of spend change events
    rpc ListChangeSpendRequestHistory(ListChangeSpendRequestHistoryRequest) returns (ListChangeSpendRequestHistoryResponse);
    // Get current spend for a product/addon
    rpc GetCurrentSpend(GetCurrentSpendRequest) returns (GetCurrentSpendResponse);
    // Create a scheduled deactivation for an item
    rpc ScheduleDeactivation(ScheduleDeactivationRequest) returns (google.protobuf.Empty);
    // Gets scheduled deactivations for multiple Activation IDs
    rpc GetMultiScheduledDeactivationsForBusiness(GetMultiScheduledDeactivationsForBusinessRequest) returns (GetMultiScheduledDeactivationsForBusinessResponse);
    // Lists scheduled activations for a business
    rpc ListScheduledActivationsForBusiness(ListScheduledActivationsForBusinessRequest) returns (ListScheduledActivationsForBusinessResponse);
    // Unschedules a ScheduledProvisioningTask of type "deactivation"
    rpc UnscheduleDeactivation(UnscheduleDeactivationRequest) returns (google.protobuf.Empty);
    // Deactivate an item immediately
    rpc DeactivateImmediately(DeactivateImmediatelyRequest) returns (google.protobuf.Empty);
    // Deactivates all items for a business immediately
    rpc DeactivateAllItemsForBusiness(DeactivateAllItemsForBusinessRequest) returns (google.protobuf.Empty) {
      option (vendastatypes.access) = {
        scope: "business"
      };
    };
    // Get the activation dates for multiple items
    rpc GetMultiActivationDates(GetMultiActivationDatesRequest) returns (GetMultiActivationDatesResponse);
    // Unschedules a ScheduledProvisioningTask of type "activation"
    rpc UnscheduleActivation(UnscheduleActivationRequest) returns (google.protobuf.Empty);
    // Creates an order for a single order item
    // This is not intended for general use, and is locked down to specific service accounts
    rpc CreateOrderForSingleItem(CreateOrderForSingleItemRequest) returns (CreateOrderForSingleItemResponse);
}

