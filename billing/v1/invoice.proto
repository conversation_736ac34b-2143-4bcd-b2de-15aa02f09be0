syntax = "proto3";

package billing.v1;

option go_package = "github.com/vendasta/generated-protos-go/billing/v1;billing_v1";
option java_outer_classname = "InvoiceProto";
option java_package = "com.vendasta.billing.v1.generated";

import "billing/v1/applied_tax_rate.proto";
import "billing/v1/common.proto";
import "billing/v1/currency.proto";
import "billing/v1/field_mask.proto";
import "billing/v1/tax_rate.proto";
import "billing/v1/applied_bundle.proto";
import "billing/v1/applied_discount.proto";
import "billing/v1/payment_method.proto";
import "billing/v1/recurring_invoice.proto";

import "google/protobuf/timestamp.proto";

enum InvoiceStatus {
    DRAFT = 0;
    DUE = 1;
    PAID = 2;
    VOID = 3;
}

enum LastPaymentStatus {
    PAYMENT_UNSET = 0;
    PAYMENT_SUCCESS = 1;
    PAYMENT_PENDING = 2;
    PAYMENT_FAILED = 3;
}

message InvoiceItem {
    message RetailSubscriptionGroup {
        // identifiers of retail subscription group, if the invoice's line item belongs to one
        // The id of the retail subscription group
        string id = 1;
        // The name of the retail subscription group
        string name = 2;
    }
    // Random identifier of an invoice item
    string id = 1;
    // The identifier of what products/subscription this item refers to. Does not conform to the SKU standard
    string sku = 2;
    // The amount (in cents)
    int64 amount = 3;
    // The number of items purchased
    float quantity = 4;
    // The name or human readable description of what is being purchased
    string description = 5;
    // calculated total which includes discounts and taxes
    int64 total = 6;
    // The tax rates which apply to the line item
    repeated AppliedTaxRate applied_taxes = 7;
    // The default tax rates to apply to the line item
    repeated TaxRate tax_rates = 8;
    // the bundle this item belongs to
    string bundle_id = 9;
    // the discounts that have been applied to this invoice item
    repeated AppliedDiscount discounts  = 10;
    // Amount * Quantity
    int64 subtotal = 11;
    // the instance of the bundle this item belongs to
    string bundle_instance_id = 12;
    // The start of the service period associated with the line item
    google.protobuf.Timestamp service_period_start = 13;
    // The end of the service period associated with the line item
    google.protobuf.Timestamp service_period_end = 14;
    // The subscription items's retail subscription group, if item belongs to one
    RetailSubscriptionGroup retail_subscription_group = 15;
}

message Invoice {
    message CustomerAddress{
        // The street address of the customer
        string address = 1;
        // The city of the customer
        string city = 2;
        // The state of the customer
        string state = 3;
        // The zip code of the customer
        string zip = 4;
        // The country of the customer
        string country = 5;
        // The second street address of the customer
        string address2 = 6;
    }
    // The id of the merchant
    string merchant_id = 1;
    // Identifier for the invoice
    string id = 2;
    // The reference number assigned to the invoice for use in the dashboard
    string number = 3;
    // Identifier of the customer who the invoice is for within Vendasta (AG-1234567890)
    string customer_id = 4;
    // Status of the invoice
    InvoiceStatus status = 5;
    // Amount in cents of the invoice before taxes and discounts
    int64 subtotal = 6;
    // Amount in cents of the invoice
    int64 total = 7;
    // Amount in cents that has been paid on the invoice
    int64 amount_paid = 8;
    // Date when invoice is issued.
    google.protobuf.Timestamp issued = 9;
    // Due date of the invoice
    google.protobuf.Timestamp due = 10;
    // Created date of the invoice
    google.protobuf.Timestamp created = 11;
    // Last updated date of the invoice
    google.protobuf.Timestamp updated = 12;
    // The line items of the invoice
    repeated InvoiceItem items = 13;
    // The currency that this invoice is in
    Currency currency = 14;
    // Paid date of the invoice
    google.protobuf.Timestamp paid = 15;
    // The (optional) order that this invoice is based on. Refers to a sales_orders.v1 Order
    string order_id = 16;
    // The amount of tax on this invoice. This is the sum of all the tax amounts on this invoice.
    int64 amount_tax = 17;
    // The aggregate amounts calculated per tax rate for all line items.
    repeated AppliedTaxRate applied_taxes = 18;
    // The default tax rates to apply to the invoice
    repeated TaxRate default_tax_rates = 19;
    // The bundles that have been added to the invoice
    repeated AppliedBundle applied_bundles = 20;
    // The total amount of all discounts
    int64 amount_discount  = 21;
    // The name of the customer
    string customer_name = 22;
    // Indicates what triggered the invoice generation
    string origin = 23;
    // Indicates how we will attempt to collect the invoice (attempt to automatically charge the invoice or send the invoice to the customer)
    string collection_method = 24;
    // The identifier of the contact who will receive invoice notification
    string contact_id = 25;
    // An arbitrary note which appears on the invoice
    string memo = 26;
    // Voided date of the invoice
    google.protobuf.Timestamp voided = 27;
    // The identifier of the default payment method for the invoice. It must belong to the customer associated with the invoice.
    string default_payment_method = 28;
    // the address of the customer
    CustomerAddress customer_address = 29;
    // the identifier of the customer that is in other systems (Non AG-1234567890)
    string external_customer_identifier = 30;
    // The internal notes on an invoice
    repeated Note notes = 31;
    // The user ids of any additional contacts who will receive invoice notifications
    repeated string additional_contact_ids = 32;
    // Date when the invoice is set to be posted.
    google.protobuf.Timestamp auto_advance = 33;
    // payment_method_types define how a Customer can pay for the Invoice (defaults to card)
    repeated PaymentMethodType payment_method_types = 34;
    // The status of the last payment attempt
    LastPaymentStatus last_payment_status = 35;
    // The amount owing on the invoice
    int64 amount_owing = 36;
    // The credit notes applied to the invoice
    repeated AppliedCreditNote applied_credit_notes = 37;
}

message AppliedCreditNote {
    // The credit note id
    string credit_note_id  = 1;
    // The credit amount applied in cents
    int64 amount = 2;
    // The type of credit note
    CreditNoteType credit_note_type = 3;
    // The credit note number
    string credit_note_number = 4;
}

message AddLineItemRequest {
    // The id of the merchant
    string merchant_id = 1;
    // Identifier for the invoice
    string invoice_id = 2;
    // The identifier of what products/subscription this item refers to. Does not conform to the SKU standard
    string sku = 3;
    // The amount (in cents)
    int64 amount = 4;
    // The number of items purchased
    float quantity = 5;
    // The name or human readable description of what is being purchased
    string description = 6;
    // The default tax rates to apply to the invoice
    repeated string tax_rates = 7;
    // the discounts to apply to this invoice item
    repeated AppliedDiscount discounts  = 8;
    // The start of the service period associated with the line item
    google.protobuf.Timestamp service_period_start = 9;
    // The end of the service period associated with the line item
    google.protobuf.Timestamp service_period_end = 10;
}

message AddLineItemResponse {
    // The invoice
    Invoice invoice = 1;
}

// Origin of the invoice
enum Origin {
  UNSET = 0;
  MANUAL = 1;
  SALES_ORDER = 2;
  RENEWAL = 3;
  TEMPLATE = 4;
}

message CreateInvoiceRequest {
    string merchant_id = 1;
    string customer_id = 2;
    // Optional: Use to override the default invoice currency
    string currency_code = 3;
    // Optional: Use when creating an invoice from a sales order
    string order_id = 4;
    // Optional: The line items of the invoice
    repeated InvoiceItem items = 5;
    // Optional: The identifier of the default payment method for the invoice. It must belong to the customer associated with the invoice.
    string default_payment_method = 6;
    // Optional: The identifier of the contact who will receive invoice notification
    string contact_id = 7;
    // Optional: Indicates how we will attempt to collect the invoice
    CollectionMethod collection_method = 8;
    // Optional: The origin of the invoice. Defaults to 'manual' if not provided.
    Origin origin = 9;
    // Optional: The issued date of the invoice. Will remain blank if not provided.
    google.protobuf.Timestamp issued = 10;
}

message ChargeInvoiceRequest {
    // The unique identifier of the merchant
    string merchant_id = 1;
    // The unique identifier of the customer
    // Deprecated: No longer in use, the customer ID on the corresponding invoice will be used instead
    string customer_id = 2 [deprecated=true];
    // The unique identifier of the invoice
    string invoice_id = 3;
    // (Optional) The unique identifier of the payment method
    // If not provided, the default payment method associated with the invoice will be used. If no default payment method is set on the invoice, the customer default payment method will be used.
    string payment_method_id = 4;
}

message CreateInvoiceResponse {
    string invoice_id = 1;
}

message DeleteInvoiceRequest {
    string merchant_id = 1;
    string invoice_id = 2;
}

message DeleteLineItemRequest {
    string merchant_id = 1;
    string invoice_id = 2;
    string id = 3;
}

message DeleteLineItemResponse {
    // The invoice
    Invoice invoice = 1;
}

message GetInvoiceRequest {
    // The merchant the invoice is for
    string merchant_id = 1;
    // The invoice id
    string invoice_id = 2;
}

message GetInvoiceResponse {
    // The invoice
    Invoice invoice = 1;
}

message GetInvoiceWithCalculatedTaxesRequest {
    // The merchant the invoice is for
    string merchant_id = 1;
    // The invoice id
    string invoice_id = 2;
}

message GetInvoiceWithCalculatedTaxesResponse {
    // The invoice with calculated taxes
    Invoice invoice = 1;
}

message ListInvoicesRequest {
  message Filters {
    message DateFilter {
      enum DateField {
        // Filter invoice by their created date
        CREATED = 0;
        // Filter invoice by their issued date
        ISSUED = 1;
      }
      DateField date_field = 1;
      google.protobuf.Timestamp date_lte = 2;
      google.protobuf.Timestamp date_gte = 3;
    }
    // The merchant the invoice is for
    string merchant_id = 1;
    // The customer the invoice was created for
    string customer_id = 2;
    // Invoice statuses
    repeated InvoiceStatus statuses = 3;
    // Invoices that are passed the due date and have not been paid
    bool past_due = 4;
    // Number will filter to a specific invoice number
    string number = 5;
    // Filter invoices based on dates
    DateFilter date_filter = 6;
    // Filter invoices based on their origin (source)
    repeated string origins = 7;
    // Filter invoices based on their collection method
    repeated string collection_methods = 8;
    // Filter invoices based on their last payment status
    repeated LastPaymentStatus last_payment_statuses = 9;
  }
  // The filters for listing invoices
  Filters filters = 1;
  // Options for how to page the response for this request
  PagedRequestOptions paging_options = 2;
}

message ListInvoicesResponse {
    // The invoices
    repeated Invoice invoices = 1;
    // Metadata about the paging
    PagedResponseMetadata paging_metadata = 2;
}

message PayInvoiceRequest {
    // The merchant the invoice is for
    string merchant_id = 1;
    // The invoice id
    string invoice_id = 2;
    // An optional reason for this update
    string reason = 3;
}

message SendInvoiceRequest {
    // The merchant the invoice is for
    string merchant_id = 1;
    // The invoice id
    string invoice_id = 2;
    // The email address to send the invoice to
    repeated string email_addresses = 3;
    // The language to send the invoice in
    string language_code = 4;
}

message UpdateInvoiceRequest {
    Invoice invoice = 1;
    // The fields to update (no field mask means update all fields)
    FieldMask field_mask = 2;
}

message UpdateInvoiceResponse {
    Invoice invoice = 1;
}

message UpdateLineItemRequest {
    // The id of the merchant
    string merchant_id = 1;
    // Identifier of the invoice
    string invoice_id = 2;
    // The unique identifier of the line item
    string id = 3;
    // The identifier of what products/subscription this item refers to. Does not conform to the SKU standard
    string sku = 4;
    // The amount (in cents)
    int64 amount = 5;
    // The number of items purchased
    float quantity = 6;
    // The name or human readable description of what is being purchased
    string description = 7;
    // The tax rates to apply to the invoice line item
    repeated string tax_rates = 8;
    // the discounts to apply to this invoice item
    repeated AppliedDiscount discounts  = 9;
    // The start of the service period associated with the line item
    google.protobuf.Timestamp service_period_start = 10;
    // The end of the service period associated with the line item
    google.protobuf.Timestamp service_period_end = 11;
    // The field mask to apply to the invoice line item. Only fields provided in the field mask will be updated. If a field mask is not present, the operation applies to all fields (as if a field mask of all fields has been specified).
    // Nested field updates are not supported.
    //
    // Note: The only updatable fields are those included in the UpdateLineItemRequest message
    FieldMask field_mask = 12;
}

message UpdateLineItemResponse {
    // The invoice
    Invoice invoice = 1;
}

message AddBundleRequest {
    string invoice_id = 1;
    string merchant_id = 2;
    string bundle_id = 3;
}

message AddBundleResponse {
    // The invoice
    Invoice invoice = 1;
}

message RemoveBundleRequest {
    string invoice_id = 1;
    string merchant_id = 2;
    string bundle_id = 3;
    string bundle_instance_id = 4;
}

message RemoveBundleResponse {
    // The invoice
    Invoice invoice = 1;
}

message UpdateDefaultTaxRatesRequest {
    // Identifier of the invoice
    string invoice_id = 1;
    // The id of the merchant
    string merchant_id = 2;
    // Identifiers of the tax rates to apply to the invoice
    repeated string tax_rate_ids = 3;
}

message UpdateDefaultTaxRatesResponse {
    // The invoice
    Invoice invoice = 1;
}

message VoidInvoiceRequest {
    // The merchant the invoice is for
    string merchant_id = 1;
    // The invoice id
    string invoice_id = 2;
    // An optional reason for this update
    string reason = 3;
}

message DuplicateInvoiceRequest{
    // The merchant the invoice is for
    string merchant_id = 1;
    // The invoice id
    string invoice_id = 2;
}

message DuplicateInvoiceResponse {
    // The invoice id
    string invoice_id = 1;
}

message UpdateBundleRequest {
    // The ID of the merchant
    string merchant_id = 1;
    // Identifier of the invoice
    string invoice_id = 2;
    // The ID of the bundle to update
    string bundle_id = 3;
    // Whether to hide the items in the bundle or not
    bool hide_bundle_items = 4;
    // The ID of the instance of the bundle to update
    string bundle_instance_id = 5;
}

message UpdateBundleResponse {
    // The invoice
    Invoice invoice = 1;
}

message PaymentLinkItem {
    // The amount (in cents)
    int64 amount = 1;
    // The number of items purchased
    float quantity = 2;
    // The name or human readable description of what is being purchased
    string description = 3;
}

message CreatePaymentLinkRequest {
    // The merchant ID can be the Partner ID or the Account Group ID
    string merchant_id = 1;
    // Customer ID
    string customer_id = 2;
    // Currency Code
    string currency_code = 3;
    // Line items
    repeated PaymentLinkItem line_items = 4;
    // Due date
    google.protobuf.Timestamp due = 5;
}

message CreatePaymentLinkResponse {
    // Invoice ID for payment link
    string invoice_id = 1;
}

message GetUpcomingInvoicePreviewRequest {
  string merchant_id = 1;
  string customer_id = 2;
  // (optional) The date that the invoice will be generated on
  // If not provided, the next invoice date will be calculated
  google.protobuf.Timestamp invoice_date = 3;
}

message GetUpcomingInvoicePreviewResponse {
  // The invoice to preview
  // The following fields are not populated:
  // id, number, amount_paid, issued, paid, origin, voided, customer_address, external_customer_identifier
  Invoice invoice = 1;
}
