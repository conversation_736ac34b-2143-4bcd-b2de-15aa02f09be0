syntax = "proto3";

package billing.v1;

option go_package = "github.com/vendasta/generated-protos-go/billing/v1;billing_v1";
option java_outer_classname = "BillableItemProto";
option java_package = "com.vendasta.billing.v1.generated";

import "billing/v1/common.proto";
import "billing/v1/field_mask.proto";
import "billing/v1/frequency.proto";
import "billing/v1/product_pricing.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
import "billing/v1/subscription.proto";

enum SubscriptionStatus{
  SUBSCRIPTION_STATUS_UNSET = 0;
  SUBSCRIPTION_STATUS_ACTIVE = 1;
  SUBSCRIPTION_STATUS_EXPIRED = 2;
}

enum Source {
  SOURCE_UNSET = 0;
  SOURCE_SYSTEM = 1;
  SOURCE_USER = 2;
}

// A proto representation of a billable item (also referred to as a subscription).
message BillableItem {
  message CustomerAddress{
    // The street address of the customer
    string address = 1;
    // The city of the customer
    string city = 2;
    // The state of the customer
    string state = 3;
    // The zip code of the customer
    string zip = 4;
    // The country of the customer
    string country = 5;
  }
  message RetailSubscriptionGroup {
    // identifiers of retail subscription group, if the billable item belongs to one
    // The id of the retail subscription group
    string id = 1;
    // The name of the retail subscription group
    string name = 2;
  }
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system
  string customer_id = 3;
  // Unique id for this instance of the sku.
  string order_id = 4;
  // The time at which the billable item will expire.
  google.protobuf.Timestamp expiry = 5;
  // The time when the billable item starts wholesale billing.
  google.protobuf.Timestamp billing_start = 6;
  // The time the billable item was originally made
  google.protobuf.Timestamp created = 7;
  // The time the billable item was most recently modified
  google.protobuf.Timestamp updated = 8;
  // The wholesale frequency this product is billed
  Frequency frequency = 9;
  // The renewal date of the billable item where it is an anchor point value that may be set by users or calculated by the system
  google.protobuf.Timestamp next_renewal = 10;
  // The name of the customer
  string customer_name = 11;
  // the name of the product
  string product_name = 12;
  // The address of the customer
  CustomerAddress customer_address = 13;
  // The custom wholesale unit price of the subscription
  int64 custom_unit_price = 14;
  // Deprecated: use `frequency` - the frequency field for both retail and wholesale BillableItems
  Frequency retail_frequency = 15 [deprecated = true];
  // Deprecated: use `next_renewal` - the next_renewal field for both retail and wholesale BillableItems
  google.protobuf.Timestamp next_retail_renewal = 16 [deprecated = true];
  // Deprecated: use `price` to determine the pricing
  int64 retail_unit_price = 17 [deprecated = true];
  // The current state of the subscription
  SubscriptionStatus status = 18;
  // The product pricing information
  ProductPricing price = 19;
  // A unique identifier for the subscription
  string subscription_id = 20;
  // The time the billable item was deleted
  google.protobuf.Timestamp deleted = 21;
  // Who was responsible for the creation of the billable item
  Source source = 22;
  // Specifies whether the subscription should result in charges (ie. through invoices) to the customer
  // If unspecified, it will result in charges to the customer
  google.protobuf.BoolValue auto_billable = 23;
  // The description of the subscription. This is how the subscription will appear on invoices and is synonymous with a display name
  string description = 24;
  // The billing relationship the subscription is being created for
  Consumer consumer = 25;
  // The anchor date for determining when a renewal is recorded or prepared to be charged.
  google.protobuf.Timestamp billing_cycle_anchor = 26;
  // The start of the next service period
  google.protobuf.Timestamp next_service_period_start = 27;
  // The end of the next service period
  google.protobuf.Timestamp next_service_period_end = 28;
  // The next day an invoice will be generated for the subscription
  google.protobuf.Timestamp next_invoice_date = 29;
  // The retail subscription group, if item belongs to one
  RetailSubscriptionGroup retail_subscription_group = 30;
}

// An Item to bill for
message Item {
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 1;
  // The reference to the owner in the external system.
  string customer_id = 2;
  // Unique ID for this instance of the sku.
  string order_id = 3;
  // (Optional) the date at which billing should occur. This date can be in the past.
  google.protobuf.Timestamp billing_start = 4;
}

// SubscriptionProjectionFilter controls which sets of extended subscription data is returned on subscription responses
message SubscriptionProjectionFilter {
  // Controls if product price is returned
  bool price = 1;
  // Controls if deleted subscriptions are returned
  bool include_deleted = 2;
}

// Bill for the specified item. This could be instantly, or just add to next invoice.
message BillItemRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system.
  string customer_id = 3;
  // Unique ID for this instance of the sku.
  string order_id = 4;
  // (Optional) The date at which billing should occur. This date can be in the past. Also the date which renewals are driven off of.
  google.protobuf.Timestamp billing_start = 5;
  // (Optional) The date to prorate the billable item to. If billing_start is Jan 1 and prorated_to is Jan 15, the price should reflect the period from Jan 15 - Jan 31 but will renew for a full month on Feb 1 (assuming monthly frequency).
  google.protobuf.Timestamp prorated_to = 6;
}

// Bill for the specified items. This could be instantly, or just add to next invoice.
message BillMultipleItemsRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // The items to bill for (in a single charge for instant)
  repeated Item items = 2;
}

message BillMultipleItemsResponse {
  // The id of the temporary record that was created to group the items together
  string id = 1;
}

message CanBillItemRequest {
  // The unique identifier of the merchant
  string merchant_id = 1;
  // Unique id representing a product
  string sku = 2;
}

// Delete a billable item
message DeleteBillableItemRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system
  string customer_id = 3;
  // Unique id for this instance of the sku.
  string order_id = 4;
}

// Update a billable item's expiry date
message ExpireBillableItemRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system
  string customer_id = 3;
  // Unique id for this instance of the sku.
  string order_id = 4;
  // The date when the billable item is expired.
  google.protobuf.Timestamp expiry = 5;
  // Optional: The subscription id of the billable item.
  // This can be used as an alternative to the combination of the primary key fields
  // (merchant_id, sku, customer_id, and order_id) to identify the billable item.
  string subscription_id = 6;
}

// Clear a billable item's expiry date
message UnexpireBillableItemRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system
  string customer_id = 3;
  // Unique id for this instance of the sku.
  string order_id = 4;
  // Optional: The subscription id of the billable item.
  // This can be used as an alternative to the combination of the primary key fields
  // (merchant_id, sku, customer_id, and order_id) to identify the billable item.
  string subscription_id = 5;
}

// Get an existing billable item
message GetBillableItemRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system
  string customer_id = 3;
  // Unique id for this instance of the sku.
  string order_id = 4;
  // Extended data to include as part of the response
  SubscriptionProjectionFilter projection_filter = 5;
}

// Response that has the billable item
message GetBillableItemResponse {
  // The Billable Item
  BillableItem item = 1;
}

// request structure for getting billable items.
message GetMultiSubscriptionsRequest {
  message Key {
    // The id of the merchant selling the product.
    string merchant_id = 1;
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 2;
    // The reference to the owner in the external system
    string customer_id = 3;
    // Unique id for this instance of the sku.
    string order_id = 4;
  }

  // List of subscription keys. Can use subscription_ids instead.
  repeated Key subscription_keys = 1;
  // Extended data to include as part of the response
  SubscriptionProjectionFilter projection_filter = 2;
  // Optional: list of subscription IDs. Can be used instead of subscription_keys.
  repeated string subscription_ids = 3;
}

// response structure for the requested billable items.
message GetMultiSubscriptionsResponse {
  // list of subscriptions
  repeated BillableItem subscriptions = 1;
}

// List existing billable item
message ListBillableItemsRequest {
  message Filters {
    // Unique ids representing products (Stock Keeping Unit).
    repeated string skus = 1;
    // The id of the merchant selling the product.
    string merchant_id = 2;
    // The reference to the owner in the external system
    string customer_id = 3;
  }
  // The filters for listing the billable items.
  Filters filters = 1;
  // Options for how to page the response for this request
  PagedRequestOptions paging_options = 2;
  // search term for pre and post fix matching on customer names
  string search_term = 3;
}

// Response that has the billable items
message ListBillableItemsResponse {
  // The Billable Item
  repeated BillableItem items = 1;
  // Metadata about the paging
  PagedResponseMetadata paging_metadata = 2;
}

// List active and inactive subscriptions
message ListSubscriptionsRequest {
  reserved 3;

  message Filters {
    // Unique ids representing products (Stock Keeping Unit).
    repeated string skus = 1;
    // The id of the merchant selling the product.
    string merchant_id = 2;
    // The reference to the owner in the external system
    string customer_id = 3;
    // search term for pre and post fix matching on customer names
    string search_term = 4;
    // Status for whether the product is activated or deactivated
    SubscriptionStatus status = 5;
    // Filter to subscriptions greater than or equal to the given timestamp
    google.protobuf.Timestamp expired_date_gte = 6;
    // Filter to subscriptions less than or equal to the given timestamp
    google.protobuf.Timestamp expired_date_lte = 7;
    // Frequencies that the subscriptions are billed at
    repeated Frequency frequencies = 8;
  }

  // The filters for listing the billable items.
  Filters filters = 1;
  // Options for how to page the response for this request
  PagedRequestOptions paging_options = 2;
  // Extended data to include as part of the response
  SubscriptionProjectionFilter projection_filter = 4;
}

message ExportSubscriptionsRequest {
  //The filters for listing the billable items.
  ListSubscriptionsRequest.Filters filters = 1;
  // The consumer of the subscriptions
  Consumer consumer = 2;
}

// Response that has subscriptions
message ListSubscriptionsResponse {
  // The Billable Item
  repeated BillableItem subscriptions = 1;
  // Metadata about the paging
  PagedResponseMetadata paging_metadata = 2;
}

// UsageRequest creates a usage for a metered product
message CreateUsageRequest {
  // The id of merchant
  string merchant_id = 1;
  // The id of the customer incurring usage charges
  string customer_id = 2;
  // Unique id for this instance of the sku
  string order_id = 3;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 4;
  // Quantity of usage
  int64 quantity = 5;
  // The timestamp at which the usage occurred
  google.protobuf.Timestamp usage_date = 7;
}

message UpdateSubscriptionRequest {
  // The subscription that includes updatable fields
  message Subscription {
    // The unique identifier of the subscription. This is used only for identification and is not editable
    string subscription_id = 1;
    // The description of the subscription. This is how the subscription will appear on invoices and is synonymous with a display name
    string description = 2;
    // Acts as an anchor date for determining when a renewal is recorded or prepared to be charged.
    // It is called an anchor date because it does not reflect the actual date, but is used to compute future dates.
    google.protobuf.Timestamp billing_cycle_anchor = 3;
  }

  Subscription subscription = 1;
  // The field mask to apply to the subscription. Only fields provided in the field mask will be updated. At least one field is required.
  // Nested field updates are not supported.
  //
  // Note: The only updatable fields are those included in the UpdateSubscriptionRequest.Subscription message
  FieldMask field_mask = 2;
}

message UpdateSubscriptionResponse {}

