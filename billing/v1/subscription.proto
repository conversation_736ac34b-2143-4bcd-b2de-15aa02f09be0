syntax = "proto3";

package billing.v1;

option go_package = "github.com/vendasta/generated-protos-go/billing/v1;billing_v1";
option java_outer_classname = "SubscriptionProto";
option java_package = "com.vendasta.billing.v1.generated";

import "billing/v1/frequency.proto";
import "billing/v1/subscribe_validation.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

// Renewal State is used when creating a new subscription (billable item).
enum RenewalState {
    // The subscription will be created in a recurring state upon successfully being billed. The next renewal date will be automatically set based upon the start_date.
    RENEWAL_STATE_IMMEDIATE = 0;
    // The subscription will be created in a non recurring state upon successfully being billed. The next renewal date will be set at a later time.
    RENEWAL_STATE_DELAYED = 1;
}

// Consumer represents the billing relationship the subscription is being created for
enum Consumer {
    // A wholesale consumer represents a billing relationship with Vendasta
    CONSUMER_WHOLESALE = 0;
    // A retail consumer represents a billing relationship with a merchant
    CONSUMER_RETAIL = 1;
}

// an alignment subscription is specified in a create subscription request to align the new subscription to an existing subscription
message AlignmentSubscription {
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 1;
    // The reference to the owner in the external system
    string customer_id = 2;
    // Unique id for this instance of the sku.
    string order_id = 3;
}

// A subscription connects a customer to products/services. It determines what your customer has signed up for and how often and if they're charged recurringly for it.
message Subscription {
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 1;
    // The reference to the owner in the external system.
    string customer_id = 2;
    // Unique ID for this instance of the sku.
    string order_id = 3;
    // (Optional) the date at which billing should occur. This date can be in the past
    google.protobuf.Timestamp billing_start = 4;
    // The state that the subscription is getting created in
    RenewalState renewal_state = 5;
    // DEPRECATED for using in subscription creation: use `align_with` instead
    // (Optional) The date to prorate the subscription to. If billing_start is Jan 1 and prorated_to is Jan 15, the price should reflect the period from Jan 15 - Jan 31 but will renew for a full month on Feb 1 (assuming monthly frequency).
    google.protobuf.Timestamp prorated_to = 6;
    // (Optional) the bundle that applies to calculating retail pricing
    string retail_bundle_id = 7;
    // indicates whether the subscription exists for resale by the merchant
    bool for_resale = 8;
    // The wholesale custom unit price to be set on the subscription
    int64 custom_unit_price = 9;
    // Used on subscription create to align the anniversary date of a new subscription with an existing subscription
    // triggers prorated billing on the new subscription
    AlignmentSubscription align_with = 10;
    // An ID that associated the subscription with others purchased as part of the same bundle. This is important when purchasing multiples of a bundle as part of the same transaction,
    // as it allows the system to determine which subscriptions are part of which instance of the bundle.
    // This can be any string, as long as it is unique within the set of subscriptions created within the same RPC call.
    string bundle_instance_id = 11;
    // the frequency at which the wholesale subscription will be billed
    Frequency frequency = 12;
    // The retail amount for the subscription. This is in the smallest currency unit.
    // DEPRECATED: this field is not used by the billing system. Prices must be set using PricingService.UpsertRetail
    int64 retail_amount = 14 [deprecated = true];
    // The frequency at which the retail price will be billed.
    Frequency retail_frequency = 15;
    // The billing relationship the subscription is being created for
    Consumer consumer = 16;
    // (Optional) The desired renewal start date. This should be used when the subscription is being created with a specific renewal start date in mind.
    google.protobuf.Timestamp renewal_start = 17;
    // (Optional) The desired expiry date. This should be used when the subscription is being created with a specific end date in mind.
    google.protobuf.Timestamp expiry = 18;
    // Specifies whether the subscription should result in charges (ie. through invoices) to the customer
    google.protobuf.BoolValue auto_billable = 19;
    // The identifier of the subscription
    // This id is meant to match with an existing SubscriptionPreset so fields can be hydrated from that preset
    // If there is no matching SubscriptionPreset, this id will be ignored and a new id will be auto-generated
    string subscription_id = 20;
    // Acts as an anchor date for determining when a renewal is recorded or prepared to be charged.
    // It is called an anchor date because it does not reflect the actual date, but is used to compute future dates.
    google.protobuf.Timestamp billing_cycle_anchor = 21;
    // (Optional) The identifier of the retail subscription group that this subscription belongs to.
    string retail_subscription_group_id = 22;
}

message CanCreateSubscriptionsRequest {
    message SubscriptionItem {
        // The SKU of the product.
        string sku = 1;
        // The frequency the product is billed at.
        Frequency frequency = 2;
        // The order quantity of this product.
        int64 quantity = 3;
    }

    // The unique identifier of the merchant
    string merchant_id = 1;
    // a map of product skus and the quantity requested for that sku
    // This is deprecated, use items instead.
    map<string, int64> skus = 2 [deprecated=true];
    // A list of the product information.
    repeated SubscriptionItem items = 3;
}

message ValidateResponse {
    string sku = 1;
    SubscribeValidation code = 2;
}

message CanCreateSubscriptionsResponse {
    repeated ValidateResponse validation_response = 1;
}

// Creates a new subscription that starts at the specified start time. This could be billed instantly, or billed on the next invoice
message CreateSubscriptionRequest {
    // The id of the merchant selling the product.
    string merchant_id = 1;
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 2;
    // The reference to the owner in the external system.
    string customer_id = 3;
    // Unique ID for this instance of the sku.
    string order_id = 4;
    // (Optional) The date at which billing should start. This date can be in the past. Also the date which immediate renewals are driven off of.
    // NOTE: if align_with is provided it will overwrite the value of billing_start
    google.protobuf.Timestamp billing_start = 5;
    // (Optional) The date to prorate the billable item to. If billing_start is Jan 1 and prorated_to is Jan 15, the price should reflect the period from Jan 15 - Jan 31 but will renew for a full month on Feb 1 (assuming monthly frequency).
    // NOTE: if align_with is provided it will overwrite the value of prorated_to
    google.protobuf.Timestamp prorated_to = 6;
    // The state that the subscription is getting created in. If immediate the subscription will reoccur. If delayed the subscription won't reoccur until the renewal start date has been set.
    RenewalState renewal_state = 8;
    // (Optional) the bundle that applies to calculating retail pricing
    string retail_bundle_id = 9;
    // indicates whether the subscription exists for resale by the merchant
    bool for_resale = 10;
    // The wholesale custom unit price to be set on the subscription
    int64 custom_unit_price = 11;
    // (Optional) Used on subscription create to align the anniversary date of a new subscription with an existing subscription
    // triggers prorated billing on the new subscription
    AlignmentSubscription align_with = 12;
    // the frequency at which the wholesale subscription will be billed
    Frequency frequency = 13;
    // The retail amount for the subscription. This is in the smallest currency unit.
    // DEPRECATED: this field is not used by the billing system. Prices must be set using PricingService.UpsertRetail
    int64 retail_amount = 14 [deprecated = true];
    // The frequency at which the retail price will be billed.
    Frequency retail_frequency = 15;
    // The billing relationship the subscription is being created for
    Consumer consumer = 16;
    // (Optional) The desired renewal start date. This should be used when the subscription is being created with a specific renewal start date in mind.
    google.protobuf.Timestamp renewal_start = 17;
    // (Optional) The desired expiry date. This should be used when the subscription is being created with a specific end date in mind.
    google.protobuf.Timestamp expiry = 18;
    // Specifies whether the subscription should result in charges (ie. through invoices) to the customer
    google.protobuf.BoolValue auto_billable = 19;
    // The identifier of the subscription
    // This id is meant to match with an existing SubscriptionPreset so fields can be hydrated from that preset
    // If there is no matching SubscriptionPreset, this id will be ignored and a new id will be auto-generated
    string subscription_id = 20;
    // Acts as an anchor date for determining when a renewal is recorded or prepared to be charged.
    // It is called an anchor date because it does not reflect the actual date, but is used to compute future dates.
    google.protobuf.Timestamp billing_cycle_anchor = 21;
    // (Optional) The identifier of the retail subscription group that this subscription belongs to.
    string retail_subscription_group_id = 22;
}

message CreateSubscriptionResponse {
    // The identifier of the subscription
    string subscription_id = 1;
}

// Create a subscription for the specified items. This could be billed instantly, or billed on the next invoice
message CreateSubscriptionsRequest {
    // The id of the merchant selling the product.
    string merchant_id = 1;
    // The items to bill for (in a single charge for instant)
    repeated Subscription subscriptions = 2;
}

message CreateSubscriptionsResponse {
    // The id of the temporary record that was created to group the items together
    string id = 1;
}


// Reverts all billing for the specified subscription
message ReverseSubscriptionRequest {
    // The id of the merchant selling the product.
    string merchant_id = 1;
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 2;
    // The reference to the owner in the external system.
    string customer_id = 3;
    // Unique ID for this instance of the sku.
    string order_id = 4;
}

// SetRenewalStartRequest specifies which subscription to set the renewal start date for, and optionally the date to set it to
message SetRenewalStartRequest {
    // The id of the merchant selling the product.
    string merchant_id = 1;
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 2;
    // The reference to the owner in the external system.
    string customer_id = 3;
    // Unique ID for this instance of the sku.
    string order_id = 4;
    // (Optional) the date to start renewing on. Will default to the current time if not supplied
    google.protobuf.Timestamp renewal_start = 5;
}

// SetAutoBillableRequest specifies which subscription to set the auto billable flag for
message SetAutoBillableRequest {
  // The unique identifier for the subscription
  string subscription_id = 1;
  // Specifies whether the subscription should result in charges (ie. through invoices) to the customer
  google.protobuf.BoolValue auto_billable = 2;
}

// ChangeRenewalDateRequest will update the next renewal_start date of an existing billable item. This will not handle proration calculations.
message ChangeRenewalDateRequest {
    // the unique identifier of the merchant
    string merchant_id = 1;
    // the unique identifier of the customer
    string customer_id = 2;
    // the unique identifier representing a product (Stock Keeping Unit).
    string sku = 3;
    // the unique identifier for this instance of the sku.
    string order_id = 4;
    // the desired next renewal date
    google.protobuf.Timestamp  next_renewal_date = 5;
}

// ChangeFrequencyRequest will update the frequency of an existing billable item
message ChangeFrequencyRequest {
    // The id of the merchant selling the product.
    string merchant_id = 1;
    // Unique id representing a product (Stock Keeping Unit).
    string sku = 2;
    // The reference to the owner in the external system.
    string customer_id = 3;
    // Unique ID for this instance of the sku.
    string order_id = 4;
    // the desired frequency
    Frequency frequency = 5;
}

// ChangeCustomUnitPriceRequest will update the custom unit price of an existing billable item
message ChangeCustomUnitPriceRequest {
  // The id of the merchant selling the product.
  string merchant_id = 1;
  // Unique id representing a product (Stock Keeping Unit).
  string sku = 2;
  // The reference to the owner in the external system.
  string customer_id = 3;
  // Unique ID for this instance of the sku.
  string order_id = 4;
  // The custom unit price to be set on the subscription
  int64 custom_unit_price = 5;
}
