## 2.96.0
- Remove `updated_by_user_id` field from `UpsertAppReportingCodeOverrideRequest` message

## 2.95.0
- Add `AppReportingCodeOverride` message and `AppReportingCodeOverrideType` enum for managing app reporting code overrides
- Add `AppReportingCodeOverrides` service with `Get` and `Upsert` RPCs

## 2.94.0
- Add `item_type` to `ItemDependencyNode` message

## 2.93.0
- Add `GetItemDependencyTree` rpc to `Items` service and associated messages

## 2.92.0
- Add `store_id` to `ListDependenciesRequest`

## 2.90.0
- Add scopes to `GetMultiPricing`, and `ListActivatableItems`

## 2.90.0
- Add `GetMultiDependentItems` rpc to `Items` service

## 2.89.0
- Add `business-app` scope to `GetUpgradeDialogContent` rpc

## 2.88.0
- Add `business-app` scope to `ListAppSettings` rpc

## 2.87.0
- Add `GetMultiAuxiliaryData` to get specific fields of a single app

## 2.86.0
- Add `customer_id` to `Discount` message in `AppPrice` message in `GetMultiPricingResponse`

## 2.85.0
- Add `business-app` scope to `GetAppSettings`

## 2.84.0
- Change `edition_id` to `edition_requirement` on the `ListDependenciesRequiredItem` message

## 2.83.0
- Rename `ListDependenciesSelectedItems` to `ItemIdentifier`
- Remove `item_type` from `ItemIdentifier` 
- Rename `selected_items` to `items` in `ListDependenciesItemsRequest`

## 2.82.0
- Remove `item_name`, `edition_name`, and `icon_url` from Add `ListDependenciesRequiredItem`
  - As this api isn't in use yet, this is not a breaking change

## 2.81.0
- Add `ListDependencies` rpc to `Items` service

## 2.80.0
- Add `uses_editions` field to `EditionInformation`

## 2.79.0
- Add `disable_fallback` field to `GetAppSettingsRequest`

## 2.78.0
- Add `PRICE_DISPLAY_OVERRIDE_STARTING_AT` option to `PriceDisplayOverride` enum

## 2.77.0
- Add `requested_items` field to `GetMultiPricingRequest` and mark `requested_apps` as deprecated

- ## 2.76.0
- Add `child_apps` to the `App`

## 2.75.0
- Add `item_types` field to the `ListActivatableItemsFilters` and mark `item_type` field as deprecated
- Add `ITEM_TYPE_PACKAGE` to `ItemType` enum 

## 2.74.0
- Allow `ListOwnedItemsFilterOptions` to specify an App Type to filter by

## 2.73.0
- Mark `app_ids` as deprecated in `GetMultiPricingRequest` message
- Add `item_ids` as optional field to the `GetMultiPricingRequest` to preserve backwards compatibility for `app_ids` 

## 2.72.0
- Add `business_id` to the `GetMultiPricingRequest` message
## 2.71.0
- Add `has_order_form` to the `ListActivatableItemsFilters` message

## 2.70.0
- Add `required_item_id` to the `ListActivatableItemsFilters` message

## 2.69.0
- Add `item_type` to the `ListActivatableItemsFilters` message

## 2.68.0
- Add `store_id` as an optional field to the `ListActivatableItemsRequest` message

## 2.67.0
- Add `country` to the `ListActivatableItemsFilters`.

## 2.66.0
- Add the `include_suspended` flag to the `ListActivatableItemsFilters`.

## 2.65.0
- Add `total_results` field to `ListActivatableItemsResponse`

## 2.64.0
- Rename `seller_name` to `name` in `RequiredItem` message

## 2.63.1
- Fix typo in field `uses_editions` in `ActivatableItemSummary` message

## 2.63.0
- Add `use_editions` to the `ActivatableItemSummary` message

## 2.62.0
- BREAKING CHANGE: Replace `owner_id` field with `owner_partner_id` on `ListActivatableItemsFilters`

## 2.61.0
- Modify `ListActivatableItemsSortOptions`to specify the sort field and sort direction (ascending/descending)

## 2.60.0
- Add `ListActivatableItems` RPC to the `Items` service.

## 2.59.0
- Add the `Items` service.
- Add the `ListOwnedItems` RPC to the `Items` service.

## 2.58.0
- Deprecate `external_form_url` and similar fields on Fulfillment OrdersConfig.

## 2.57.0
- Add `uses_fulfillment_form` to `UpdateFulfillmentOrderConfig` API and top level FulfillmentOrderConfig.

## 2.56.0
- Add `OrderForm` to FOConfig.
- Add `ProjectionFilter` support to the `GetFulfillmentOrderConfig` API.

## 2.55.0
- Add `AppLevelAdminDashboard` and related structs

## 2.54.0
- Remove unnecessary Create and Delete APIs for `FulfillmentOrderConfigService`. This is technically BREAKING, but we haven't used these APIs yet so this is fine.

## 2.53.0
- Add `FulfillmentOrderConfigService` and related structs

## 2.52.0
- Add `Fee` struct and `fees` field to `GetMultiPricingRequest` RPC response

## 2.51.0
- Add `GetPurchaseLink` RPC

## 2.50.0
- Add `GetMultiAuxiliaryDataFieldSchema` for product auxiliary data field schemas

## 2.49.0
- Add scope to `UpsertAuxiliaryData`

## 2.48.0
- Add scope to `ListAuxiliaryData`

## 2.47.0
- Fix `filter_options` on `GetMultiPricingRequest` as not repeated

## 2.46.0
- Add `filter_options` and `requested_apps` to `GetMultiPricingRequest`
- Add `instance_price` to `FrequencyPrice` for `GetMultiPricingResponse`

## 2.45.0
- Add `ArchiveAuxiliaryDataFieldSchema` and `UnarchiveAuxiliaryDataFieldSchema` rpcs
- Add `only_archived` as part of `filters` for `ListAuxiliaryDataFieldSchemaRequest`
- Add `archived` to `AuxiliaryDataFieldSchema`
- 
## 2.44.0
- Add `AppRestrictions` field to `ActivationInformation`

## 2.43.0
- Add `Discount` field to GetMultiPricing RPC

## 2.42.0
- Add `GetMultiPricing` RPC

#2.40.0
- Add services `ProductAuxiliaryFieldData` and `ProductAuxiliaryFieldSchema`

#2.40.0
- Add `app_type` to `App`

#2.39.0
- Add `hide_upgrade_cta` to `EditionChange`

#2.38.0
- Add `country_restrictions` to `ActivationInformation`

#2.37.1
- Version bump to fix generated-protos-go issues with v2 submodules

#2.37.0
- Add minimal price structure (setup fee / uses custom pricing) to `App`

#2.36.0
- Add new endpoint for suggesting a product `SuggestProduct`. This will be primarily used by PCC in discover product page when partners/resellers want to suggest a product to marketplace.

#2.35.0
- Add `suspended` field to `ActivationInformation` message

#2.34.0
- Add new `categories` service

#2.33.0
- Add `videos` to `EndUserMarketingInformation`

#2.32.0
- Add `need_help` to `EndUserMarketingInformation`

# 2.31.0 
- Add `external_identifiers` to `App`

# 2.30.0
- Add `required_business_data` to `ActivationInformation`

# 2.29.0
- Add `price_reference_package_id` to `EditionChange`

# 2.28.0
- Add new `EditionUpgradeAction` for `EDITION_UPGRADE_ACTION_CUSTOM_ORDER`
- Add list of `custom_upgrade_packages` to `EditionChange`

#2.27.0
- Add field `data` in `ScopedTag` message
- Add `ListByMultiTag` rpc to AppData service

# 2.26.0
- Add `TagApps` rpc to AppData service
- Add `UntagApps` rpc to AppData service

## 2.25.0
- Add `ListPartnerEnabledApps` rpc to partner service

## 2.24.0
- Add new endpoint for listing app IDs by tags.

## 2.23.0
- Add `ListValuesByMultiKind` rpc

## 2.22.0
- Add new AppData service for tag information

## 2.21.0
- Add `allow_white_labeling` to `BasicIntegration` message

## 2.20.0
- Add `enabled` field to `Branding` message

## 2.19.0
- Add `Branding` to `AppSettings` to allow whitelabel
- Add `Branding` to `ReplaceAppSettingsRequest`

## 2.18.0
- Add `Trial` to `ReplaceAppSettingsRequest` message

## 2.17.0
- Add `Trial` message to `AppSettings` to allow configuration of trial behaviour

## 2.16.0
- Add `EditionChange` to `AppSettings` to allow specific actions to be selected for edition upgrades.

## 2.15.0
- Add edition information to the app response

## 2.14.0
- Add rpc `ListAppSettings`

## 2.13.0
- Add rpc `GetAppSettings`

## 2.12.0
- Add `AppSettings` proto and `ReplaceAppSettings` rpc

## 2.11.0
- Add separate_order_forms to `ActivationInformation`

## 2.10.0
- Add include_not_enabled to `GetMultiAppRequest`
- Add tagline to `Details` on `RequiredParent`

## 2.9.0
- Add `TrialConfiguration` message to `App` message.

## 2.8.0
- Add activation_specific_url_enabled to `ActivationInformation`

## 2.7.0
- Remove the `order_form` field from base app
- Add `ActivationInformation` that includes order_form_enabled and allow_multiple_activations_enabled

## 2.6.0
- Add base order form message with enabled field inside of it

## 2.5.1
- Update parent details to return an app key instead of just an app id

## 2.5.0
- Add `market_id` to the request object for the get multi. Market ID is required
  as whitelabelling for our 0&0 applies at the market level.

## 2.4.0
- Switch the name for `AppService` to `Partner`.
- Update the names of the marketing information so that it is clear which user types they are suppose to inform

## 2.3.0
- Add the app type definition to the general settings struct.

## 2.2.0
- Add SSO Integration to the v2 marketplace app definition

## 2.1.0
- Add lmi categories to the v2 marketplace app definition

## 2.0.0
- Add v2 marketplace app service with `GetMulti` call.
