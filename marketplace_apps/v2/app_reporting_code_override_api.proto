syntax = "proto3";

package marketplaceapps.v2;

option go_package = "github.com/vendasta/generated-protos-go/marketplace_apps/v2;marketplaceapps_v2";
option java_outer_classname = "AppReportingCodeOverrideApiProto";
option java_package = "com.vendasta.marketplaceapps.v2.generated";

import "marketplace_apps/v2/app_reporting_code_override.proto";

message GetAppReportingCodeOverrideRequest {
    // The ID of the app to get the reporting code override for
    string app_id = 1;
}

message GetAppReportingCodeOverrideResponse {
    // The requested app reporting code override
    AppReportingCodeOverride app_reporting_code_override = 1;
}

message UpsertAppReportingCodeOverrideRequest {
    // The ID of the app to upsert the reporting code override for
    string app_id = 1;
    // The type of reporting code override to upsert
    AppReportingCodeOverrideType reporting_code_type = 2;
}

message UpsertAppReportingCodeOverrideResponse {
    // The upserted app reporting code override
    AppReportingCodeOverride app_reporting_code_override = 1;
}

// Service for managing app reporting code overrides
service AppReportingCodeOverrides {
    // Get the reporting code override for an app
    rpc Get(GetAppReportingCodeOverrideRequest) returns (GetAppReportingCodeOverrideResponse);
    
    // Upsert the reporting code override for an app
    rpc Upsert(UpsertAppReportingCodeOverrideRequest) returns (UpsertAppReportingCodeOverrideResponse);
} 