syntax = "proto3";

package marketplaceapps.v2;

option go_package = "github.com/vendasta/generated-protos-go/marketplace_apps/v2;marketplaceapps_v2";
option java_outer_classname = "AppReportingCodeOverrideProto";
option java_package = "com.vendasta.marketplaceapps.v2.generated";

import "google/protobuf/timestamp.proto";

// App reporting code override types for tracking different categories of applications
enum AppReportingCodeOverrideType {
  APP_REPORTING_CODE_OVERRIDE_TYPE_INVALID = 0;
  APP_REPORTING_CODE_OVERRIDE_TYPE_VENDASTA_CORE_APPS = 1;
  APP_REPORTING_CODE_OVERRIDE_TYPE_VENDASTA_DIGITAL_ADS_APPS = 2;
  APP_REPORTING_CODE_OVERRIDE_TYPE_VENDASTA_WEBSITES_APPS = 3;
  APP_REPORTING_CODE_OVERRIDE_TYPE_MANAGEMENT_FEE_DIGITAL_ADS_APPS = 4;
}

// App reporting code override object for tracking app classification overrides
message AppReportingCodeOverride {
  // The ID of the app this reporting code is associated with
  string app_id = 1;
  // The type of reporting code override
  AppReportingCodeOverrideType reporting_code_type = 2;
  // When the reporting code was assigned
  google.protobuf.Timestamp created = 3;
  // When the reporting code was last modified
  google.protobuf.Timestamp updated = 4;
  // The user ID of who last updated the reporting code
  string updated_by = 5;
} 