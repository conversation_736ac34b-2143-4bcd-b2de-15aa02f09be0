## Changelog

## 0.19.0
- Add fields to create salescoach config

## 0.18.2
- Add more fields to SalesCoachConfig

## 0.17.0
- Add user config crud to `SalesCoachConfigService`

## 0.16.0
- Add Kixie as a conversation source

## 0.15.0
- Add `MeetingBotService` and `ScheduleBot` rpc

## 0.14.0
- Add `SalesCoachConfigService` and CRUD endpoints

## 0.13.0
- Add `teams` and `zoom` as conversation sources

## 0.12.0
- Add transcript entries

## 0.11.0
- add `GetRecordingURL` rpc to get the recording URL for a conversation

## 0.10.0
- add duration to Conversation

## 0.9.0
- Add the `CreateConversation` rpc for testing

## 0.8.1
- Fix typo in `GetConversation` RPC

## 0.8.0
- Remove `ProcessedTranscript` and `RawTranscript` messages and rework apis to be for `Conversations`

## 0.0.7
- Add 'EvaluationReason' enum and repeated 'reasons' field to CreateEvaluationRequest for detailed feedback options

## 0.0.6
- Change `conference_id` to `external_id` to match the model

## 0.0.5
- Add `conference_id` and `recording_url` fields to both `RawTranscript` and `ProcessedTranscript` messages

### 0.4.0
- Add `raw_transcript_id` field to `ProcessedTranscript` message

### 0.3.1
- remove `GenerateNonceTokenForOauthFlow` rpc

### 0.2.0
- Add `GenerateNonceTokenForOauthFlow` rpc

### 0.1.0
- Remove Namespace from CreateEvaluationRequest

### 0.0.5
- Add 'CreateEvaluation' rpc

### 0.0.4
- Add search term to list processed transcripts

### 0.0.3
- Add sort to list processed transcripts

## 0.0.2
- Add filters to list processed transcripts

## 0.0.1
- Initial import of apis
