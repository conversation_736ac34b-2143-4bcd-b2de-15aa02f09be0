syntax = "proto3";

package meeting_analysis.v1;

option go_package = "github.com/vendasta/generated-protos-go/meeting_analysis/v1;meeting_analysis_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.meetinganalysis.v1.generated";

import "google/protobuf/empty.proto";
import "vendasta_types/annotations.proto";
import "vendasta_types/paging.proto";
import "meeting_analysis/v1/conversation.proto";
import "meeting_analysis/v1/salescoachconfig.proto";
import "meeting_analysis/v1/evaluation.proto";
import "vendasta_types/field_mask.proto";

enum SortDirection {
  SORT_DIRECTION_UNSET = 0;
  SORT_DIRECTION_ASCENDING = 1;
  SORT_DIRECTION_DESCENDING = 2;
}

message ListConversationsRequest {
  message Filters {
    string namespace = 1;
    ConversationSource source = 2;
    repeated string keywords = 3;
    int64 sentiment = 4;
    int64 sales_score = 5;
  }
  message Sort {
    ConversationSortField field = 1;
    SortDirection direction = 2;
  }
  Filters filters = 1;
  vendastatypes.PagedRequestOptions paging_options = 2;
  Sort sort = 3;
  string search_term = 4;
}

message ListConversationsResponse {
  repeated Conversation conversations = 1;
  vendastatypes.PagedResponseMetadata metadata = 2;
}

message GetConversationRequest {
  string id = 1;
}

message GetConversationResponse {
  Conversation conversation = 1;
}


message CreateEvaluationRequest {
  string conversation_id = 1;
  EvaluationFeedback feedback = 2;
  string comment = 3;
  repeated EvaluationReason reasons = 4;
}

message CreateConversationRequest {
  Conversation conversation = 1;
}

message CreateConversationResponse {
  string id = 1;
}

message GetRecordingURLRequest {
  string conversation_id = 1;
}

message GetRecordingURLResponse {
  string recording_url = 1;
}

service MeetingAnalysis {
  // CreateConversation is a utility api used by developers to ease testing. You won't be able to use this in production.
  rpc CreateConversation(CreateConversationRequest) returns (CreateConversationResponse) {
    option (vendastatypes.access) = {
      scope: "meeting-conversation"
    };
  }

  rpc ListConversations(ListConversationsRequest) returns (ListConversationsResponse) {
    option (vendastatypes.access) = {
      scope: "meeting-conversation"
      scope: "meeting-conversation:read"
    };
  }

  rpc GetConversation(GetConversationRequest) returns (GetConversationResponse) {
    option (vendastatypes.access) = {
      scope: "meeting-conversation"
      scope: "meeting-conversation:read"
    };
  }

  rpc CreateEvaluation(CreateEvaluationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "meeting-conversation"
    };
  }

  // GetRecording gets the URL to the recording of the meeting for a conversation.
  rpc GetRecordingURL(GetRecordingURLRequest) returns (GetRecordingURLResponse) {
    option (vendastatypes.access) = {
      scope: "meeting-conversation"
      scope: "meeting-conversation:read"
    };
  }
}

message GetSalesCoachConfigRequest{
  oneof lookup_ids {
    string id = 1;
    string namespace = 2;
  }
}

message GetSalesCoachConfigResponse{
  SalesCoachConfig sales_coach_config = 1;
}

message CreateSalesCoachConfigRequest {
  string namespace = 1;
  Context context = 2;
  bool join_meetings = 3;
  string notetaker_name = 4;
  bool show_recording_consent_messages = 5;
}

message CreateSalesConfigResponse {
  string id = 1;
}

message UpdateSalesCoachConfigRequest {
  oneof lookup_ids {
    string id = 1;
    string namespace = 2;
  }
  bool join_meetings = 3;
  string notetaker_name = 5;
  bool show_recording_consent_messages = 6;

  vendastatypes.FieldMask field_mask = 4;
}

message DeleteSalesCoachConfigRequest{
  oneof lookup_ids {
    string id = 1;
    string namespace = 2;
  }
}

message GetSalesCoachUserConfigRequest{
  string namespace = 1;
  string user_id = 2;
}

message GetSalesCoachUserConfigResponse{
  SalesCoachUserConfig sales_coach_user_config = 1;
}

message CreateSalesCoachUserConfigRequest {
  string namespace = 1;
  string user_id = 2;
  bool join_meetings = 3;
}

message UpdateSalesCoachUserConfigRequest {
  string namespace = 1;
  string user_id = 2;
  bool join_meetings = 3;

  vendastatypes.FieldMask field_mask = 4;
}

message DeleteSalesCoachUserConfigRequest{
  string namespace = 1;
  string user_id = 2;
}

service SalesCoachConfigService {
  // Get returns the config for the given id or namespace
  rpc Get(GetSalesCoachConfigRequest) returns (GetSalesCoachConfigResponse) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
      scope: "sales-coach-config:read"
    };
  };
  // Create creates the config for the given namespace
  rpc Create(CreateSalesCoachConfigRequest) returns (CreateSalesConfigResponse) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
    };
  };
  // Update updates the config for the given namespace
  rpc Update(UpdateSalesCoachConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
    };
  };
  // Delete deletes the config for the given namespace or id
  rpc Delete(DeleteSalesCoachConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
    };
  };

  // Get returns the config for the given id or namespace
  rpc GetUserConfig(GetSalesCoachUserConfigRequest) returns (GetSalesCoachUserConfigResponse) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
      scope: "sales-coach-config:read"
    };
  };
  // Create creates the config for the given namespace
  rpc CreateUserConfig(CreateSalesCoachUserConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
    };
  };
  // Update updates the config for the given namespace
  rpc UpdateUserConfig(UpdateSalesCoachUserConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
    };
  };
  // Delete deletes the config for the given namespace or id
  rpc DeleteUserConfig(DeleteSalesCoachUserConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "sales-coach-config"
    };
  };
}

message ScheduleBotRequest {
  string namespace = 1;
  string meeting_url = 2;
}

service MeetingBotService {
  rpc ScheduleBot(ScheduleBotRequest) returns (google.protobuf.Empty);
}
