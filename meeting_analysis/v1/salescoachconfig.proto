syntax = "proto3";

package meeting_analysis.v1;

option go_package = "github.com/vendasta/generated-protos-go/meeting_analysis/v1;meeting_analysis_v1";
option java_outer_classname = "SalesCoachConfigProto";
option java_package = "com.vendasta.meetinganalysis.v1.generated";

import "google/protobuf/timestamp.proto";

// Context indicates if the entity is for Partners or SMBs
enum Context {
  SALES_COACH_CONFIG_CONTEXT_UNSET = 0;
  SALES_COACH_CONFIG_CONTEXT_PARTNER = 1;
  SALES_COACH_CONFIG_CONTEXT_SMB = 2;
}

message SalesCoachConfig {
  string id = 1;
  string namespace = 2;
  Context context = 3;
  bool join_meetings = 4;

  string notetaker_name = 8;
  bool show_recording_consent_messages = 9;

  google.protobuf.Timestamp created = 5;
  google.protobuf.Timestamp updated = 6;
  google.protobuf.Timestamp deleted = 7;
}

message SalesCoachUserConfig {
  string namespace = 1;
  string user_id = 2;
  bool join_meetings = 3;

  google.protobuf.Timestamp created = 4;
  google.protobuf.Timestamp updated = 5;
  google.protobuf.Timestamp deleted = 6;
}
