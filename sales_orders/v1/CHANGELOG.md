# Change Log

This file includes v0 and v1 change logs for package `vendastapis/sales_orders/v1`.
All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

--------------------------------------------------------------------------------
## v1

## 1.125.0 - 2025-07-02
- add `business_id` to `GetSalesOrderWithCalculatedTaxesRequest` message

## 1.124.0 - 2025-07-02
- add `GetSalesOrderWithCalculatedTaxes` rpc to `SalesOrders` service

## 1.123.0 - 2025-05-22
- add `CanOrderBeWholesaleBilled` rpc to `SalesOrders` service

## 1.122.0 - 2025-05-14
- replace `can_admin_skip_approval_workflow` field with `use_approval_workflow_for_admins` for `WorkflowStepOptions` message

## 1.121.0 - 2025-05-13
- add `can_admin_skip_approval_workflow` field to `WorkflowStepOptions` message

### 1.120.0 - 2025-03-13
- add `line_item_errors` field to `ValidateLineItemsResponse`

### 1.119.0 - 2025-02-19
- add `ChargeOrder` to `SalesOrders` service

### 1.118.0 - 2025-02-19
- add `UpdateCustomerRecipientUserId` to `SalesOrders` service

### 1.117.0 - 2025-02-19
- add `UpdatePaymentMethodToken` to `SalesOrders` service

### 1.116.1 - 2025-02-12
- remove the `customer_recipient_user_id` field from `SendExistingOrderToCustomerForApprovalRequest` since it already has `user_id` field

### 1.116.0 - 2025-02-12
- add `customer_recipient_user_id` field to `SendExistingOrderToCustomerForApprovalRequest`

### 1.115.0 - 2025-02-11
- add `customer_recipient` field to `Order` message
- add `include_customer_recipient` field to `SalesOrderProjectionFilter`

### 1.114.0 - 2025-02-10
- add `can_charge_smb_on_order_submission` field to `WorkflowStepOptions` for `Config`

### 1.113.0 - 2025-01-27
- add `user_role` field of `UserRole` enum type to `StatusHistoryItem` 

### 1.112.0 - 2025-01-13
- Add `user_info` field to `StatusHistoryItem` and projection filter to `GetSalesOrderRequest`

### 1.111.0 - 2024-12-30
- Add `UpdateCustomerAttachments` RPC `SalesOrders` service

### 1.110.0 - 2024-12-30
- Add `customer_attachments` to Order message and CreateSalesOrderRequest

## 1.109.0 - 2024-12-09
- Add `SCHEDULED` AND `UNSCHEDULED` options to `ActivationStatus` message

## 1.108.0 - 2024-11-19
- Add `ValidateLineItems` rpc to `SalesOrders` service

## 1.107.0 - 2024-10-16
- Deprecate lease order to automations

## 1.106.1 - 2024-10-16
- [Sales Orders]: Add description to `CustomApproveActions` in the sales order config

## 1.106.0 - 2024-09-10
- [Sales Orders]: Add `CustomApproveActions` to the sales order config

## 1.105.0 - 2024-08-27
- Add the `CancelOrder` endpoint to the `SalesOrders` service.

## 1.104.0 - 2024-08-13
- add scope to `UpdateDocument`

## 1.103.0 - 2024-08-13
- add scopes to all endpoints in `Document` service
- add scopes to all endpoints in `SalesOrders` service

## 1.102.0 - 2024-08-13
- add `proposal` scope to `ListDocuments` and `SearchDocuments`

## 1.101.0 - 2024-08-12
- add `proposal:read` scope to `ListDocuments` and `SearchDocuments`

## 1.100.0 - 2024-07-02
- add `UpdateEnforceContractTerm` to the `SalesOrders` service

## 1.99.0 - 2024-06-07
- return `tax_rate_ids` and `initial_amount` in the `TaxOptions` message 

## 1.98.0 - 2024-06-04
- add `enforce_contract_term` field to `CreateSalesOrderRequest` message

## 1.97.0 - 2024-06-03
- add `enforce_contract_term` field to `Order` message

## 1.96.0 - 2024-03-22
- update docstring for `use_customer_price` boolean across various RPCs for creating orders to remove specificity of how the pricing is fetched (as it was since updated to fetch market level)

## 1.95.0 - 2024-02-13
- add `order_is_smb_payable` field to `Order`, `CreateSalesOrderRequest`, and `SendForCustomerApprovalRequest` messages

## 1.94.0 - 2024-02-12
- add `allow_collect_payment_from_customer` field to `WorkflowStepOptions` message

## 1.93.0 - 2024-02-04
- add `disable_tagging` to `SalespersonOptions` message

## 1.92.0 - 2024-01-04
- add `has_fulfillment` field to Order model

## 1.91.0 - 2023-12-01
- add `is_edition_change` field to ProductActivation model on Order message 

## 1.90.0 - 2023-11-21
- add documentation of expected behaviours for `use_customer_price` field on various RPC requests where it's used:
  - `CreateSalesOrder`, `SendForCustomerApproval`, `CreateDraftSalesOrder`, `CreateAndActivateOrder`, `CreateLeasedToAutomationsSalesOrder`

## 1.89.0 - 2023-11-13
- add `use_customer_price` field to the following rpc requests:
  - `CreateSalesOrder`, `SendForCustomerApproval`, `CreateDraftSalesOrder`, `CreateAndActivateOrder`, `CreateLeasedToAutomationsSalesOrder`

## 1.88.0 - 2023-10-30
- Add phone_number to `User` message

## 1.87.0 - 2023-10-11
- Remove `CustomPriceMapping` from `sales-orders`, use `sales` struct of same name for field

## 1.86.0 - 2023-09-22
- Add `CustomField` message
- Add `custom_prices` to `Package` message

## 1.85.0
- Add `purchase` scope to `GetMulitSalesOrder`

## 1.84.0 - 2023-07-11
- Add `fulfillment_status` to `Order` message

## 1.83.0 - 2023-05-11
- Add `UpdateSalesOrderMarket` to `SalesOrders` service

## 1.82.0 - 2023-04-27
- Add `PreviewOrderActivations` to `Customer` service

## 1.81.0 - 2022-03-22
- Add GetMultiSalesOrder RPC

## 1.80.0 - 2023-03-17
- revert 1.79.0 as that's not the direction we wanted to go in

## 1.79.0 - 2023-03-17
- Add dont_allow_admins_to_create_orders to the sales order config

## 1.78.0 - 2022-12-20
- Add scopes to list orders

## 1.77.0 - 2022-12-20
- Remove more wororders rpcs

## 1.76.0 - 2022-12-03
- Remove workorders rpcs
- Add delete endpoint for sales orders

## 1.75.0 - 2022-11-14
- [Sales Orders]: Add UpdateSalesperson RPC

## 1.74.0 - 2022-10-24
- [Proposal Builder]: Add `orientation` to document

## 1.73.1 - 2022-09-26
- [Sales Orders]: Rename sales-order origin `API` to `IMPORT`

## 1.73.0 - 2022-09-26
- [Sales Orders]: Add new sales-order origin: `API`

## 1.72.0 - 2022-09-22
- [Sales Orders]: Add `CustomSalespersonActions` to the sales order config

## 1.71.1 - 2022-09-21
- [Proposal Builder]: Add scopes to `CreateDocument` and `GetDocument`

## 1.71.0 - 2022-08-11
- [Sales Orders]: Add `include_archived_orders` to `ListSalesOrderRequest`

## 1.70.0 - 2022-08-11
- [Sales Orders]: Add `CreateArchivedOrder` to `SalesOrderDevelopment` service

## 1.69.0 - 2022-07-08
- [Proposal Builder]: Add `organization_user_ids` to document search filter

## 1.68.0 - 2022-06-21
- [Proposal Builder]: Add `recipient_contact_id` to document

## 1.67.0 - 2022-05-02
- Add `GetMultiUserAuxiliaryDataFieldSchema` rpc

## 1.66.0 - 2022-04-26
- [Proposal Builder]: Add `currency_code` to document

## 1.65.0 - 2022-04-26
- [Sales Orders]: Add `order` scope to `UpsertAuxiliaryData` 

## 1.64.0 - 2022-04-19
- [Proposal Builder]: Add `snapshot_id` to document

## 1.63.0 - 2022-04-11
- [Proposal Builder]: Add `pdf_path` to document

## 1.62.0 - 2022-04-11
- [Sales Order]: Create `GenerateDocumentPDF` rpc

## 1.61.0 - 2022-04-11
- [Proposal Builder]: Add SearchDocuments RPC

## 1.60.0 - 2022-04-08
- [Sales Order]: Change `AddWorkOrderCommentRequest` to not need partnerID. This is technically a BREAKING CHANGE,
but we haven't started using this API yet so it should be fine.

## 1.59.0 - 2022-04-04
- [Sales Order]: Change `ApproveSalesOrder` to use `purchase` scope instead of `order`

## 1.58.0 - 2022-04-04
- [Sales Order]: Add `purchase` scope to `CreateAndActivateOrder`

## 1.57.0 - 2022-04-01
- [Work Orders]: Added pid and agid to `LoadWorkOrderCommentsRequest`

## 1.56.0 - 2022-03-31
- [Sales Orders]: Add `order` & `order:read` scope to `ListAuxiliaryData`

## 1.55.0 - 2022-03-30
- [Sales Orders]: Add `order` scope to `ActivateProducts`, `ScheduleActivation`

## 1.54.0 - 2022-03-24
- [Sales Orders]: Add `order:read` scope to `GetSalesOrder` and `GetSalesOrderByIdempotencyKey`
- [Sales Orders]: Add `order` scope to `ApproveSalesOrder`, `DeclineSalesOrder`, `ArchiveSalesOrder`, `SubmitDraftSalesOrder` and `SubmitDraftForCustomerApproval`

## 1.53.0 - 2022-03-24
- [Sales Orders]: Add `order` scope to `UpdateTags`

## 1.52.0 - 2022-03-24
- [Work Orders]: Add `AddComment(AddWorkOrderCommentRequest)`, `LoadComments(LoadWorkOrderCommentsRequest)`

## 1.51.0 - 2022-03-23
- [Work Orders]: Create `GetWorkOrderUrl` rpc

## 1.50.0 - 2022-03-17
- [Sales Orders]: Add `ArchiveAuxiliaryDataFieldSchema` and `UnarchiveAuxiliaryDataFieldSchema` rpcs
- [Sales Orders]: Add `only_archived` as part of `filters` for `ListAuxiliaryDataFieldSchemaRequest`
- [Sales Orders]: Add `archived` to `AuxiliaryDataFieldSchema`
## 1.49.1 - 2022-02-22
- [Work Orders]: Add missing properties to `GetWorkOrderProduct` and `CheckEnabledProducts`

## 1.49.0 - 2022-02-17
- [Work Orders]: Add `GetWorkOrderProduct`, `AddWorkOrderProduct` and `DeleteWorkOrderProduct` RPCs

## 1.48.0 - 2022-02-17
- [Work Orders]: Add `WorkOrders` service
- [Work Orders]: Add `CheckEnabledProducts` rpc to `WorkOrders`

## 1.47.0 - 2022-01-18
- [Sales Orders]: Add `UpdateProductAnswer` and `GetProductAnswer` RPC with request and response messages
- [Sales Orders]: Add rpc's `GetWorkOrderRecipientSession` and `GetShareableWorkOrderUrl`

## 1.46.0 - 2022-01-31
- [Proposal Builder]: Add UnarchiveDocument RPC

## 1.45.0 - 2022-01-03
- [Sales Orders]: Add `comment` property to `DocumentEventContextReviewedByInternalUser`
- [Sales Orders]: Remove `requester_user_id` property from `MarkDocumentAsReviewedRequest`

## 1.44.0 - 2022-01-03
- [Sales Orders]: Add RPC `GetOrderRecipientDetails`

## 1.43.0 - 2021-12-28
- [Sales Orders]: Add `requester_user_id` and `comment` property to `MarkDocumentAsReviewedRequest`

## 1.42.0 - 2021-12-09
- [Sales Orders]: Add `SalesOrdersAuxiliaryFieldData` and `SalesOrdersAuxiliaryFieldSchema` services
- [Sales Orders]: Replace `UpsertSalesOrderAuxiliaryDataRequest` in `UpsertAuxiliaryData`
- [Sales Orders]: Replace `ListSalesOderAuxiliaryDataRequest` in `ListAuxiliaryData`
- [Sales Orders]: Add message `SalesOrderAuxiliaryDataObjectID`

### 1.41.0 - 2021-12-08
- [Sales Orders]: Adds declined_reason/IDs to `LeaseData` for `LeaseSalesOrderToAutomations`

### 1.40.0 - 2021-12-08
- [Proposal Builder]: Adds context messages `DocumentEventContextReviewedByInternalUser` and `DocumentEventContextUnlockDocument` 

### 1.39.0 - 2021-12-08
- [Proposal Builder]: Adds `MarkDocumentAsReviewed` and `UnlockDocument` RPCs

### 1.38.0 - 2021-11-24
- [Proposal Builder]: Rename `send_for_internal_review` to `send_for_internal_review_context`

### 1.37.0 - 2021-11-24
- [Proposal Builder]: Add  context message `DocumentEventContextSendForInternalReview`

### 1.36.0 - 2021-11-24
- [Proposal Builder]: Add `DOCUMENT_EVENT_TYPE_COMMENT_FROM_INTERNAL_USER` and associated context message `DocumentEventContextCommentFromInternalUser`

### 1.35.0 - 2021-11-24
- [Sales Orders]: Add `lease_data` property to `LeaseSalesOrderToAutomationsRequest` to contain information to be passed on to automations, with an initial property for `action_type`

### 1.34.0 - 2021-11-23
- [Proposal Builder]: Add `SendDocumentForInternalReview` RPC

### 1.33.0 - 2021-11-23
- [Proposal Builder]: Add field `DOCUMENT_STATUS_PENDING_REVIEW` to document status and `DOCUMENT_EVENT_TYPE_INTERNAL_REVIEW_REQUESTED` to document events

### 1.32.0 - 2021-11-18
- [Proposal Builder]: Replace `includeArchived` field to reserved

### 1.31.0 - 2021-11-16
- [Proposal Builder]: Add Filters to ListDocumentEventsRequest

### 1.30.0 - 2021-11-15
- [Proposal Builder]: Add field `DocumentStatus statuses` to ListDocumentsRequest.Filters

### 1.29.0 - 2021-11-15
- [Proposal Builder]: Add DocumentEventContextFeedbackFromRecipient

### 1.28.0 - 2021-11-03
- [Sales Orders]: OrderIds filter to ListSalesOrder RPC and deprecate single OrderId filter

### 1.27.0 - 2021-10-26
- [Proposal Builder]: Add field `DocumentEventContext context` to DocumentEvent
- [Proposal Builder]: Rename ListDocumentEvents.event -> ListDocumentEvents.events

### 1.26.0 - 2021-10-18
- [Proposal Builder]: Add thumbnail_path for documents

### 1.25.0 - 2021-10-15
- [Sales Orders]: Add CreateLeasedToAutomationsSalesOrder RPC
- [Sales Orders]: Add fields to LeaseSalesOrderToAutomations RPC
- [Sales Orders]: Add leased_to_automations to Order

### 1.24.0 - 2021-10-14
- [Sales Orders]: Add LeaseSalesOrderToAutomations RPC

### 1.23.0 - 2021-10-13
- [Proposal Builder]: Add view_count for documents

### 1.22.0 - 2021-10-12
- [Proposal Builder]: Add ListDocumentEvents RPC

### 1.21.0 - 2021-09-28
- [Sales Orders]: Add GetSalesOrderByIdempotencyKey RPC

### 1.20.0 - 2021-09-28
- [Sales Orders]: Add idempotency_key to CreateSalesOrderRequest and Order

### 1.19.0 - 2021-09-21
- [Proposal Builder]: Add deleted status for documents

### 1.18.0 - 2021-09-16
- [Proposal Builder]: Add include_archived in the ListDocumentsRequest

### 1.17.1 - 2021-09-15
- [Proposal Builder]: 1.17.0 + DocumentMetadata.Archived

### 1.17.0 - 2021-09-14
- [Proposal Builder]: Add MarkDocumentAsViewed RPC

### 1.16.0 - 2021-09-13
- [Proposal Builder]: Add ArchiveDocument RPC 

### 1.15.0 - 2021-09-07
- [Proposal Builder]: Move GetOrderRecipientSession to the public server (breaking, hadn't been exposed)

### 1.14.0 - 2021-09-07
- [Proposal Builder]: Add `status` (DocumentStatus) to the Document proto

### 1.13.0 - 2021-09-04
- [Proposal Builder]: Add DeclineSalesOrder and GetConfig to the CustomerSalesOrders service

### 1.12.0 - 2021-09-03
- [Proposal Builder]:
  Breaking changes:
- Removed DocumentID from the GetOrderRecipientSessionRequest proto

### 1.11.0 - 2021-09-02
- [Proposal Builder]: Add DocumentEventType and DocumentStatus enums


### 1.10.0 - 2021-08-26
- [Proposal Builder]: Add sender_user_id to GetProposalEmailPreviewRequest

### 1.9.0 - 2021-08-24
- [Proposal Builder]: Add GetOrderRecipientSession RPC

### 1.8.0 - 2021-08-19
- [Proposal Builder]: Add CustomEmailDetails  and add it to SendProposalRecipientEmailRequest and GetProposalEmailPreviewRequest
Breaking changes:
- SendProposalRecipientEmail and GetProposalEmailPreview now contain a nested ProposalEmailDetails structure

### 1.7.0 - 2021-08-17
- [Proposal Builder]: Add `market_id` to Document and ListDocumentsRequest.Filters

### 1.6.0 - 2021-07-28
- [Proposal Builder]: Add GetMultiDocuments

### 1.5.0 - 2021-07-15
- Add `activation_id` to `ProductActivations`

### 1.4.0 - 2021-06-15
- [Proposal Builder]: Add GetDocumentLineItems and UpsertDocumentLineItems RPCs

### 1.3.0 - 2021-05-26
- [Proposal Builder]: Add recipient_organization_id to ListDocuments filters

### 1.2.0 - 2021-05-26
- [Proposal Builder]: 
    Breaking Changes: 
        sender_organization_id -> recipient_organization_id
        sender_organization_type -> recipient_organization_type
        OrganizationType invalid default value of 0 added.

### 1.1.0 - 2021-04-30
- [Proposal Builder] Add organization_id and creator_user_id to the document.


### 1.0.0 - 2021-04-23
- [Proposal Builder] Initial release.
- [Proposal Builder] Add RPCs `CreateDocument`, `UpdateDocument`, `GetDocument`, `ListDocuments` and `DeleteDocument` to the `Documents` RPC server
