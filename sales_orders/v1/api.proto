syntax = "proto3";

package salesorders.v1;

option go_package = "github.com/vendasta/generated-protos-go/sales_orders/v1;salesorders_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.salesorders.v1.generated";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "sales_orders/v1/sales_orders.proto";
import "sales_orders/v1/sales_orders_public.proto";
import "sales_orders/v1/config.proto";
import "sales_orders/v1/notifications.proto";
import "sales_orders/v1/auxiliary_data.proto";
import "sales/v1/workflow.proto";
import "vendasta_types/field_mask.proto";
import "vendasta_types/annotations.proto";
import "auxiliary_data/v1/fields.proto";
import "sales_orders/v1/validation_error_codes.proto";

// Containing information to create a new order
message CreateSalesOrderRequest {
  // ID of the salesperson who should attend to the order
  string salesperson_id = 1;
  // ID of the partner that the order belongs to
  string partner_id = 2;
  // ID of the business that the order is for
  string business_id = 3;
  // ID of the market that the order belongs to
  string market_id = 4;
  // Opportunity id(s) that the order is fulfilling
  repeated string opportunity_ids = 5;
  // Time submitted by a salesperson that indicates when an administrator should activate the products on the order
  google.protobuf.Timestamp requested_activation = 6;
  // Additional notes written by a salesperson for admin only
  string notes = 7;
  // Data for the package(s) being activated upon approval of this order
  repeated Package packages = 8 [deprecated=true];
  // A list of additional unique information pertaining to an individual product
  repeated CustomField custom_fields = 9;
  // A list of additional information that is shared across multiple products
  repeated CommonField common_fields = 10;
  // Data for the product(s) being activated upon approval of this order
  repeated Product products = 11 [deprecated=true];
  // Where the order was created
  Origin origin = 12;
  // Data for the attachments on the order
  repeated Attachment attachments = 13;
  // Extra fields are the fields that a partner (or market) admin has added to their orders by default to be filled
  repeated Field extra_fields = 14;
  // Lineitems replace Products and Packages
  repeated sales.v1.LineItem line_items = 15;
  // Value representing the duration of a contract.
  sales.v1.Duration contract_duration = 16;
  // Additional notes written for customer by a salesperson
  string customer_notes = 17;
  // ID of the quote associated with the order if the order was created from a quote
  string quote_id = 18;
  // Tags on the order
  repeated string tags = 19;
  // Optional key used to prevent request duplication and allows safety when retrying requests
  string idempotency_key = 20;
  // Indicates whether or not to automatically resolve the line items' retail pricing.
  // The request's line items are expected to have nil revenue if this is set to true.
  // For more information and error scenarios, see Confluence docs:
  // https://vendasta.jira.com/wiki/spaces/PSYC/pages/2211971323/New+use+customer+price+on+sales-orders+Create+APIs
  bool use_customer_price = 21;
  // Indicates whether the order should be charged to the SMB or not
  bool order_is_smb_payable = 22;
  // Indicates whether to enforce the contract term
  bool enforce_contract_term = 23;
  // Data for the customer attachments on the order
  repeated Attachment customer_attachments = 24;
}

// Response of creating an order
message CreateSalesOrderResponse {
  // Unique identifier of an order
  string order_id = 1;
}

// Containing information to get an order
message GetSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // projection filter to decide which additional fields to include
  SalesOrderProjectionFilter projection_filter = 3;
}

message SalesOrderProjectionFilter {
  // Indicates whether info and role should be included about the user in the status history
  bool include_user_info_in_status_history = 1;
  // Indicates whether customer recipient data should be included
  bool include_customer_recipient = 2;
}

// Containing information to get multiple orders
message GetMultiSalesOrderRequest {
  // Unique identifier of an order, can be in the form of either ORD-123 or AG-123:ORD-123
  repeated string order_ids = 1;
}

// Containing information to get an order by an idempotency key
message GetSalesOrderByIdempotencyKeyRequest {
  // The idempotency key for the order
  string idempotency_key = 1;
}

// Response of getting an order
message GetSalesOrderResponse {
  // Sales order
  Order order = 1;
}

message GetMultiSalesOrderResponse {
  repeated Order orders = 1;
}

message GetSalesOrderWithCalculatedTaxesRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

// Response of getting an order
message GetSalesOrderWithCalculatedTaxesResponse {
  // Sales order
  Order order = 1;
}


// Request to get a list of orders for a partner
message ListSalesOrderRequest {
  // Begin and end of date range
  message DateRangeFilter {
    google.protobuf.Timestamp begin_range = 1;
    google.protobuf.Timestamp end_range = 2;
  }
  // Filters to list sales orders based on given filter criterias
  message Filters {
    // DEPRECATED: Singular market filter. Use market_ids instead. If both are supplied, market_ids will be used instead
    string market_id = 1 [deprecated=true];
    // DEPRECATED: Singular salesperson filter.  Use salesperson_ids instead.  If both are supplied, salesperson_ids
    // will be used instead
    string salesperson_id = 2 [deprecated=true];
    // Created date range filter
    DateRangeFilter created = 3;
    // Requested activation date filter
    DateRangeFilter requested_activation = 4;
    // Multiple status filter
    repeated Status statuses = 5;
    // Singular business filter
    string business_id = 6;
    // Singular product filter
    string product_id = 7;
    // Singular product edition filter
    string product_edition_id = 8;
    // Multi market request
    repeated string market_ids = 9;
    // Tags required to be on the order
    repeated string tags = 10;
    // Will filter for orders that don't have tags if true.
    bool untagged = 11;
    // Singular package filter
    string package_id = 12;
    // Expiry date filter
    DateRangeFilter expiry_date = 13;
    // Multiple salesperson filter
    repeated string salesperson_ids = 14;
    // DEPRECATED: Singular order identifier to filter for. Use order_ids instead. If both are supplied, order_ids will be used instead
    string order_id = 15 [deprecated=true];
    // Multiple order identifiers to filter for
    repeated string order_ids = 16;
    // Will include archived orders if true
    bool include_archived_orders = 17;
  }
  // The field to sort a result set on
  enum SortField {
    REQUESTED_ACTIVATION = 0;
    CREATED = 1;
    STATUS = 2;
    EXPIRY_DATE = 3;
  }
  // The directions that a result set can be sorted in
  enum SortDirection {
    ASCENDING = 0;
    DESCENDING = 1;
  }
  // Options for controlling the order of query results
  message SortOption {
    // A direction to sort results in
    SortDirection direction = 1;
    // Field to sort on
    SortField field = 2;
  }
  // partner id for partner making request
  string partner_id = 1;
  // Filters for the request
  Filters filters = 2;
  // Sort options for the request
  SortOption sort_option = 3;
  // current cursor position
  string cursor = 4;
  // size of the page to list
  // 0 assumes the default page size
  int64 page_size = 5;
}

// Request to get a list of orders for a customer
message ListCustomerSalesOrderRequest {
  // Begin and end of date range
  message DateRangeFilter {
    google.protobuf.Timestamp begin_range = 1;
    google.protobuf.Timestamp end_range = 2;
  }
  // Filters to list sales orders based on given filter criterias
  message Filters {
    // Multiple status filter
    repeated Status statuses = 1;
    // Created date range filter
    DateRangeFilter created = 2;
    // Requested activation date filter
    DateRangeFilter requested_activation = 3;
    // Expiry date filter
    DateRangeFilter expiry_date = 4;
    // Singular business filter
    string business_id = 5;
  }
  // The field to sort a result set on
  enum SortField {
    REQUESTED_ACTIVATION = 0;
    CREATED = 1;
    STATUS = 2;
    EXPIRY_DATE = 3;
  }
  // The directions that a result set can be sorted in
  enum SortDirection {
    ASCENDING = 0;
    DESCENDING = 1;
  }
  // Options for controlling the order of query results
  message SortOption {
    // A direction to sort results in
    SortDirection direction = 1;
    // Field to sort on
    SortField field = 2;
  }
  // partner id for partner making request
  string partner_id = 1;
  // Filters for the request
  Filters filters = 2;
  // Sort options for the request
  SortOption sort_option = 3;
  // current cursor position
  string cursor = 4;
  // size of the page to list
  // 0 assumes the default page size
  int64 page_size = 5;
}

message ListTagsRequest {
  // Filters to list tags based on given filter criterias
  message Filters {
    // The market_ids to include when filtering for tags, if empty will filter for all markets
    repeated string market_ids = 1;
  }
  // partner id for partner making request
  string partner_id = 1;
  // Filters for the request
  Filters filters = 2;
}

message ListTagsResponse {
  repeated string tags = 1;
}

// Response to get a list of orders for a partner
message ListSalesOrderResponse {
  // list of the requested sales order data
  repeated Order orders = 1;
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 2;
  // Whether or not more results exist
  bool has_more = 3;
  // The number of total results there are
  int64 total_results = 4;
}

// Request for approving a order
message ApproveSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

// ArchiveSalesOrderRequest is use to archive an order
message ArchiveSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

// ArchiveSalesOrderResponse is returned when an order is archived
message ArchiveSalesOrderResponse {
  // Sales order
  Order order = 1;
}

// DeleteSalesOrderRequest is use to delete an order
message DeleteSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}


// CheckSalesOrderExistsRequest is used to check for the existance of active sales orders
message CheckSalesOrderExistsRequest {
  // The partner the order is being made under
  string partner_id = 1;
  // The business the order is being made for
  string business_id = 2;
  // A product ID that must be included in the order if provided here.
  string product_id = 3;
  // A product edition ID that must be included in the order if provided here.
  string product_edition_id = 4;
  // The statuses the sales order check if for. If empty, check for sales order with any status.
  repeated Status statuses = 5;
}

// CheckSalesOrderExistsResponse describes if an order exists
message CheckSalesOrderExistsResponse {
  // boolean that will only be true if an order matched the criteria given in the request
  bool exists = 1;
}

// Response of approving an order
message ApproveSalesOrderResponse {
  // Sales order
  Order order = 1;
}

// Request for declining a order
message DeclineSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Notes about why the order is declined
  string declined_reason = 3;
  // The selected declined reasons
  repeated string declined_reason_ids = 4;
}

// Response of declining an order
message DeclineSalesOrderResponse {
  // Sales order
  Order order = 1;
}

// Request for activating products on the order
message ActivateProductsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // A list of additional unique information pertaining to an individual product
  repeated CustomField custom_fields = 3;
  // A list of additional information that is shared across multiple products
  repeated CommonField common_fields = 4;
  // A list of extra information
  repeated Field extra_fields = 5;
}

// Response of activating products on the order
message ActivateProductsResponse {
  // Sales order
  Order order = 1;
}

message ScheduleActivationRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // A list of additional unique information pertaining to an individual product
  repeated CustomField custom_fields = 3;
  // A list of additional information that is shared across multiple products
  repeated CommonField common_fields = 4;
  // A list of extra information
  repeated Field extra_fields = 5;
}

// Request for sending orders with payment information that the Partner must approve
message CreateForPartnerApprovalRequest {
  // The order to create
  Order order = 1;
}

// Response when sending orders with payment information that the Partner must approve
message CreateForPartnerApprovalResponse {
  // Unique identifier of an order
  string order_id = 1;
}

message CreateWithPaymentIntentRequest {
  // The order to create
  Order order = 1;
}

message CreateWithPaymentIntentResponse {
  // Unique identifier of the created order
  string order_id = 1;
}

message SendForCustomerApprovalRequest {
  //The order to create
  Order order = 1;
  // The user to send the order to
  string user_id = 2;
  // The date and time at which this offer expires and can no longer be approved by the customer
  google.protobuf.Timestamp offer_expiry = 3;
  // Indicates whether or not to automatically resolve the line items' retail pricing.
  // The request's line items are expected to have nil revenue if this is set to true.
  // For more information and error scenarios, see Confluence docs:
  // https://vendasta.jira.com/wiki/spaces/PSYC/pages/2211971323/New+use+customer+price+on+sales-orders+Create+APIs
  bool use_customer_price = 4;
}

message SendForCustomerApprovalResponse {
  // Unique identifier of an order
  string order_id = 1;
}

message SendExistingOrderToCustomerForApprovalRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The user to send the order to
  string user_id = 3;
  // The date and time at which this offer expires and can no longer be approved by the customer
  google.protobuf.Timestamp offer_expiry = 4;
  // Indicates whether the order should be charged to the SMB or not
  bool order_is_smb_payable = 5;
}

message ChargeOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message ChargeOrderResponse {
  // Sales order
  Order order = 1;
}

message CreateDraftSalesOrderRequest {
  //The order to create
  Order order = 1;
  // Indicates whether or not to automatically resolve the line items' retail pricing.
  // The request's line items are expected to have nil revenue if this is set to true.
  // For more information and error scenarios, see Confluence docs:
  // https://vendasta.jira.com/wiki/spaces/PSYC/pages/2211971323/New+use+customer+price+on+sales-orders+Create+APIs
  bool use_customer_price = 2;
}

message CreateDraftSalesOrderResponse {
  // Unique identifier of an order
  string order_id = 1;
}

message CustomerCreateSalesOrderRequest {
  //The order to create
  Order order = 1;
}

message CustomerCreateSalesOrderResponse {
  // Unique identifier of an order
  string order_id = 1;
}

// Request for the customer to approve an order
message CustomerApproveSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The terms the customer was presented
  repeated Agreement agreements = 3;
  // (Optional) An ID used to associate a payment method to the order, which if provided will set the order status to awaiting payment
  string payment_method_token = 4;
}

// Response of a customer approving an order
message CustomerApproveSalesOrderResponse {
  // Sales order
  Order order = 1;
}

message GetUsersRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message GetUsersResponse {
  map<string, User> users = 1;
}

message UpdateConfigRequest {
  Config config = 1;
  // The fields to change on the config
  vendastatypes.FieldMask field_mask = 2;
}

message GetConfigRequest {
  string partner_id = 1;
  string market_id = 2;
}

message GetConfigResponse {
  Config config = 1;
}

message DeleteConfigRequest {
  string partner_id = 1;
  string market_id = 2;
}

// Request to update a sales order
message UpdateAnswersRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Custom field answers for the order
  repeated CustomField custom_fields = 3;
  // Common field answers for the order
  repeated CommonField common_fields = 4;
  // Extra fields are the fields that a partner (or market) admin has added to their orders by default to be filled
  repeated Field extra_fields = 5;
}

// Response of a customer approving an order
message UpdateAnswersResponse {
  // Sales order
  Order order = 1;
}

message UpdateTagsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // List of tags for an order
  repeated string tags = 3;
}

message UpdateTagsResponse {
  Order order = 1;
}

message UpdateSalespersonRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // List of tags for an order
  string salesperson_id = 3;
}

message UpdateSalespersonResponse {
  Order order = 1;
}

message UpdateNotesRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Notes on the order
  string notes = 3;
}

message UpdateCustomerNotesRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Customer notes on the order
  string customer_notes = 3;
}

message UpdateAttachmentsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Attachments on the order
  repeated Attachment attachments = 3;
}

message UpdateCustomerAttachmentsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Attachments on the order
  repeated Attachment attachments = 3;
}

// The payload for updating the requested activation of an order
message UpdateRequestedActivationRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The new requested activation date
  google.protobuf.Timestamp requested_activation = 3;
}

// The payload for updating the contract duration of an order
message UpdateContractDurationRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Value representing the duration of a contract.
  sales.v1.Duration contract_duration = 3;
}

// The payload for updating the enforce_contract_term field on an order
message UpdateEnforceContractTermRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Value representing the field to enforce the contract term of an order
  bool enforce_contract_term = 3;
}

// The payload for updating the Revenue of the LineItems of an order
message UpdateCurrentRevenueRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The LineItems to be updated
  repeated sales.v1.LineItem line_items = 3;
}

// The payload for updating the LineItems of an order
message UpdateLineItemsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The LineItems to be updated
  repeated sales.v1.LineItem line_items = 3;
}

// The payload for updating the PaymentMethodToken of an order
message UpdatePaymentMethodTokenRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The payment method token to associate with the order
  string payment_method_token = 3;
}

// The payload for updating the CustomerRecipientUserId of an order
message UpdateCustomerRecipientUserIdRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The user ID of the customer who receives the order
  string customer_recipient_user_id = 3;
}

// Request to submit a sales order draft
message SubmitDraftRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Custom field answers for the order
  repeated CustomField custom_fields = 3;
  // Common field answers for the order
  repeated CommonField common_fields = 4;
  // Extra fields are the fields that a partner (or market) admin has added to their orders by default to be filled
  repeated Field extra_fields = 5;
}

// Request to submit a sales order draft
message SubmitDraftForApprovalRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The user to send the order to
  string user_id = 3;
  // Custom field answers for the order
  repeated CustomField custom_fields = 4;
  // Common field answers for the order
  repeated CommonField common_fields = 5;
  // Extra fields are the fields that a partner (or market) admin has added to their orders by default to be filled
  repeated Field extra_fields = 6;
  // The date and time at which this offer expires and can no longer be approved by the customer
  google.protobuf.Timestamp offer_expiry = 7;

}

message SubmitDraftResponse {
  string order_id = 1;
}

message SubmitDraftForApprovalResponse {
  string order_id = 1;
}

// Request for ignoring a product activation error
message IgnoreProductActivationErrorRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The unique identifier of the product activation
  string product_activation_unique_id = 3;
}

// Request for ignoring all product activation errors on an order
message IgnoreAllProductActivationErrorsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

// Request to cancel the order
message CancelOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Notes from the partner administrator cancelling the order
  string notes = 3;
}

// Request for submitting a request to cancel the order
message RequestCancellationRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Notes for the partner administrator from the salesperson this will be appended to the notes on the order
  string notes = 3;
}

// Request for approving a requested cancellation
message ApproveCancellationRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

// Request for declining a request to cancel the order
message DeclineCancellationRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Notes explaining why the cancellation was declined
  string notes = 3;
}

// Request for comparing current activations with activations that will be done for order
message PreviewOrderActivationsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

// Request for comparing requested activations for the order with what's currently active
message PreviewOrderActivationsResponse {
  // List of product activations for order with activations marking products that are already active
  repeated ProductActivation product_activations = 1;
}

// Request for converting a sales order to draft status
message ConvertToDraftRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message GetStatusCountsRequest {
  // Filters to list sales orders based on given filter criterias
  message Filters {
    // Filter for orders created for businesses in specific markets
    repeated string market_ids = 1;
    // Singular salesperson filter
    string salesperson_id = 2;
  }

  string partner_id = 1;
  Filters filters = 2;
}

message GetStatusCountsResponse {
  // Counts is a map from the status to the number of orders that are currently in that status
  // the key is the value of the order.Status values but proto doesn't allow enums as the key type so it's ints
  map<int64, int64> counts = 1;
}

// Request for creating an invoice from this sales order
message CreateInvoiceRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message CreateInvoiceResponse {
  // The ID of the invoice that was created. This id refers to a billing_v1.Invoice
  string invoice_id = 1;
}

// Request for creating an order directly into activation state
message CreateAndActivateOrderRequest {
  //The order to create
  Order order = 1;
  // Indicates whether or not to automatically resolve the line items' retail pricing.
  // The request's line items are expected to have nil revenue if this is set to true.
  // For more information and error scenarios, see Confluence docs:
  // https://vendasta.jira.com/wiki/spaces/PSYC/pages/2211971323/New+use+customer+price+on+sales-orders+Create+APIs
  bool use_customer_price = 2;
}

// Response for creating an order directly into activation state
message CreateAndActivateOrderResponse {
  // Unique identifier of an order
  string order_id = 1;
}

message InitiateOrderExportRequest {
  // Begin and end of date range
  message DateRangeFilter {
    google.protobuf.Timestamp begin_range = 1;
    google.protobuf.Timestamp end_range = 2;
  }
  // Filters to export sales orders based on given filter criterias
  message Filters {
    // Multiple market request
    repeated string market_ids = 1;
    // Multiple salesperson filter
    repeated string salesperson_ids = 2;
    // Multiple sales team filter
    repeated string sales_team_ids = 3;
    // Created date range filter
    DateRangeFilter created = 4;
    // Requested activation date filter
    DateRangeFilter requested_activation = 5;
    // Expiry date filter
    DateRangeFilter expiry_date = 6;
    // Multiple status filter
    repeated Status statuses = 7;
    // Tags required to be on the order
    repeated string tags = 8;
    // Will filter for orders that don't have tags if true
    bool untagged = 9;
    // Any one of these packages must be included in the order
    // The package id is defined by the marketplace for a grouping of products sold together
    repeated string package_ids = 10;
    // Multiple specific business filter
    repeated string business_ids = 11;
    // Singular order identifier to filter for
    string order_id = 12;
  }
  // The field to sort a result set on
  enum SortField {
    REQUESTED_ACTIVATION = 0;
    CREATED = 1;
    STATUS = 2;
    EXPIRY_DATE = 3;
  }
  // The directions that a result set can be sorted in
  enum SortDirection {
    ASCENDING = 0;
    DESCENDING = 1;
  }
  // Options for how the data should be formatted in the exported file
  enum DataFormatOption {
    // specifies to produces one line per order with columns suitable for a Partner Admin.
    ORDERS_PER_ROW = 0;
    // specifies to produces one line per product in the matching orders with columns suitable for a Partner Admin. Order level values will be duplicated on each row.
    PRODUCTS_PER_ROW = 1;
    // specifies to produces one line per order with columns suitable for a Salesperson (SSC).
    ORDERS_PER_ROW_SALES = 2;
    // specifies to produces one line per product in the matching orders with columns suitable for a Salesperson (SSC). Order level values will be duplicated on each row.
    PRODUCTS_PER_ROW_SALES = 3;
    // specifies to produces one line per order with columns suitable for the Buyer (Business Center).
    ORDERS_PER_ROW_BUYER = 4;
    // specifies to produces one line per product in the matching orders with columns suitable for the Buyer (Business Center). Order level values will be duplicated on each row.
    PRODUCTS_PER_ROW_BUYER = 5;
  }
  // Options for controlling the order of query results
  message SortOption {
    // A direction to sort results in
    SortDirection direction = 1;
    // Field to sort on
    SortField field = 2;
  }
  // partner id for partner making request
  string partner_id = 1;
  // Filters for the request
  Filters filters = 2;
  // Sort options for the request
  SortOption sort_option = 3;
  // Try again URL is a link for the Failure Notification for retrying the export
  string try_again_url = 4;
  // The selected option for how the data should be formatted in the exported file
  DataFormatOption selected_data_format = 5;
}

// Defines the response body from InitiateOrderExport
message InitiateOrderExportResponse {
  // The unique identifier for the file generation that can be used to check its status
  string export_id = 1;
}

// Request for getting order items for vendors data
message ListOrdersRequest {
  // Begin and end of date range
  message DateRangeFilter {
    google.protobuf.Timestamp begin_range = 1;
    google.protobuf.Timestamp end_range = 2;
  }
  message Filters {
    // The unique ids of resellers
    repeated string reseller_ids = 1;
    // The activation statuses to filter for
    repeated ActivationStatus activation_statuses = 2;
    // The fulfillment statuses to filter for
    repeated string fulfillment_statuses = 3;
    // The app ids to filter for
    repeated string app_ids = 4;
    // Created date range filter
    DateRangeFilter created = 5;
    // Search term across account name, product name, project name
    string search_term = 6;
    // Indicator of whether or not to exclude children (addons) results from the search
    bool exclude_children = 7;
    // Indicator to treat any provided app ids as excluded
    bool exclude_app_ids = 8;
    // Indicator to treat any provided reseller ids as excluded
    bool exclude_reseller_ids = 9;
  }
  // The partner id of the vendor
  string partner_id = 1;
  // current cursor position
  string cursor = 2;
  // The filters for listing orders
  Filters filters = 3;
}

message Pricing {
  // The amount that was set for items using custom pricing
  int64 amount = 1;
  // The set up fee amount that was paid by the reseller (partner)
  int64 setup_fee = 2;
  // The currency used by the reseller (partner)
  string currency_code = 3;
  // The frequency that the amount will be charged
  string frequency = 4;
}

message FulfillmentStatus {
  // The status of the fulfillment project, which is custom for each partner
  string status = 1;
  // The due date of the fulfillment project
  google.protobuf.Timestamp due_date = 2;
  // The assignee of the fulfillment project
  string assignee = 3;
  // The identifier of the fulfillment project
  string project_id = 4;
  // The project name
  string project_name = 5;
}

message VendorOrder {
  // The unique identifier of the order
  string order_id = 1;
  // The unique identifier of the order form for the particular order item
  string order_form_submission_id = 2;
  // The unique identifier of the reseller (partner) who placed the order
  string reseller_id = 3;
  // The name of the reseller (partner) who placed the order
  string reseller_name = 4;
  // The unique identifier of the business the order was for
  string business_id = 5;
  // The unique identifier of the product / order item
  string app_id = 6;
  // The name of the product / order item
  string app_name = 7;
  // The business information that the order was for
  Business business = 8;
  // Time the order was placed by the reseller (partner)
  google.protobuf.Timestamp created = 9;
  // Pricing information for the order item
  Pricing pricing = 10;
  // Activation status is the current activation state of the product
  ActivationStatus activation_status = 11;
  // Fulfillment status is the current fulfillment state of the product
  FulfillmentStatus fulfillment_status = 12;
  // The reason for a rejected order
  string rejected_reason = 13;
}

// Response for getting orders for vendors
message ListOrdersResponse {
  repeated VendorOrder orders = 1;
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 2;
  // Whether or not more results exist
  bool has_more = 3;
  // An int64 indicating the total number of orders returned by the query.
  int64 total_results = 4;
}

// Data about the order to be used by the automations service
message LeaseData {
  option deprecated = true;
  // Type of action that was taken on the order (eg. approve, decline)
  string action_type = 1;
  // For action_type decline, notes about why the order is declined
  string declined_reason = 2;
  // The selected declined reasons
  repeated string declined_reason_ids = 3;
}

// Request for leasing a sales order to automations
message LeaseSalesOrderToAutomationsRequest {
  option deprecated = true;
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // Custom field answers for the order
  repeated CustomField custom_fields = 3;
  // Common field answers for the order
  repeated CommonField common_fields = 4;
  // Extra fields are the fields that a partner (or market) admin has added to their orders by default to be filled
  repeated Field extra_fields = 5;
  // Data about the order to be used by the automations service
  LeaseData lease_data = 6;
}

// Response for leasing a sales order to automations
message LeaseSalesOrderToAutomationsResponse {
  option deprecated = true;
  // Sales order
  Order order = 1;
}

message CreateLeasedToAutomationsSalesOrderRequest {
  option deprecated = true;
  //The order to create
  Order order = 1;
  // Indicates whether or not to automatically resolve the line items' retail pricing.
  //  // The request's line items are expected to have nil revenue if this is set to true.
  //  // For more information and error scenarios, see Confluence docs:
  //  // https://vendasta.jira.com/wiki/spaces/PSYC/pages/2211971323/New+use+customer+price+on+sales-orders+Create+APIs
  bool use_customer_price = 2;
}

message CreateLeasedToAutomationsSalesOrderResponse {
  option deprecated = true;
  // Unique identifier of an order
  string order_id = 1;
}

message DuplicateSalesOrderRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message DuplicateSalesOrderResponse {
  // Unique identifier of an order
  string order_id = 1;
}

message UpdateSalesOrderMarketRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The market to update the order to
  string market_id = 3;
}

// Request for checking if partner can bill the wholesale for the given order
message CanOrderBeWholesaleBilledRequest {
  // The business the order belongs to
  string business_id = 1;
  // The order to validate
  string order_id = 2;
}

message  CanOrderBeWholesaleBilledResponse {
  // Indicates whether or not the partner can bill the wholesale for the order
  bool can_order_be_wholesale_billed = 1;
}


// The service to interact with sales orders
service SalesOrders {
  // Create data for a sales order
  rpc CreateSalesOrder(CreateSalesOrderRequest) returns (CreateSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Api for customer to create a sales order. [deprecated], use the one from the CustomerSalesOrders service instead
  rpc CustomerCreateSalesOrder(CustomerCreateSalesOrderRequest) returns (CustomerCreateSalesOrderResponse){
    option deprecated = true;
  };
  // Duplicate an order an existing order. The new order created will be set to "DRAFT" status
  rpc DuplicateSalesOrder(DuplicateSalesOrderRequest) returns (DuplicateSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update the market of the sales order
  rpc UpdateSalesOrderMarket(UpdateSalesOrderMarketRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };

  // Get data for a sales order
  rpc GetSalesOrder(GetSalesOrderRequest) returns (GetSalesOrderResponse) {
    option (vendastatypes.access) = {
        scope: "order"
        scope: "order:read"
    };
  };
  // GetMultiSalesOrder will fetch multiple orders at the same time.
  // It supports ids in the format of `ORD-123` or `AG-1234567:ORD-123`
  // If the order can not be found or the caller does not have access a nil placeholder will be returned in the position corresponding to the id's location in the request.
  // This RPC will not return a not found or permission denied error. It may return a unauthenticated error.
  rpc GetMultiSalesOrder(GetMultiSalesOrderRequest) returns (GetMultiSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
      scope: "purchase" // This is required because of a legacy issue with automations
    };
  };
  // Get data for a sales order by idempotency key
  rpc GetSalesOrderByIdempotencyKey(GetSalesOrderByIdempotencyKeyRequest) returns (GetSalesOrderResponse) {
    option (vendastatypes.access) = {
        scope: "order"
        scope: "order:read"
    };
  };
  // Get data for a sales order with calculated taxes
  rpc GetSalesOrderWithCalculatedTaxes(GetSalesOrderWithCalculatedTaxesRequest) returns (GetSalesOrderWithCalculatedTaxesResponse) {
    option (vendastatypes.access) = {
        scope: "order"
        scope: "order:read"
    };
  };
  // List sales orders
  rpc ListSalesOrder(ListSalesOrderRequest) returns (ListSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // An admin approving a sales order
  rpc ApproveSalesOrder(ApproveSalesOrderRequest) returns (ApproveSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "purchase"
    };
  };
  // A Customer approving a sales order. [deprecated] use the one from the CustomerSalesOrders service instead
  rpc CustomerApproveSalesOrder(CustomerApproveSalesOrderRequest) returns (CustomerApproveSalesOrderResponse){
    option deprecated = true;
  };
  // Decline a sales order
  rpc DeclineSalesOrder(DeclineSalesOrderRequest) returns (DeclineSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Activate products in a sales order
  rpc ActivateProducts(ActivateProductsRequest) returns (ActivateProductsResponse) {
    option (vendastatypes.access) = {
      scope: "purchase"
    };
  };
  // Schedule activation for products in a sales order
  rpc ScheduleActivation(ScheduleActivationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Archive a sales order
  rpc ArchiveSalesOrder(ArchiveSalesOrderRequest) returns (ArchiveSalesOrderResponse)  {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Delete a sales order
  rpc DeleteSalesOrder(DeleteSalesOrderRequest) returns (google.protobuf.Empty)  {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Check if an order exists for the given criteria
  rpc CheckSalesOrderExists(CheckSalesOrderExistsRequest) returns (CheckSalesOrderExistsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // SendForCustomerApproval creates an order and sends it to the SMB for approval
  rpc SendForCustomerApproval(SendForCustomerApprovalRequest) returns (SendForCustomerApprovalResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // SendExistingOrderToCustomerForApproval will send an existing order in the awaiting customer approval status to a user
  rpc SendExistingOrderToCustomerForApproval(SendExistingOrderToCustomerForApprovalRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  rpc ChargeOrder(ChargeOrderRequest) returns (ChargeOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // GetUsers gets the users that have taken action on a given order
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // Get an Order Config
  rpc GetConfig(GetConfigRequest) returns (GetConfigResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // Update an Order Config
  rpc UpdateConfig(UpdateConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Delete an Order Config
  rpc DeleteConfig(DeleteConfigRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update Answers for an Order
  rpc UpdateAnswers(UpdateAnswersRequest) returns (UpdateAnswersResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update Tags for an Order
  rpc UpdateTags(UpdateTagsRequest) returns (UpdateTagsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update the salesperson of an order
  rpc UpdateSalesperson(UpdateSalespersonRequest) returns (UpdateSalespersonResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update Notes for an Order
  rpc UpdateNotes(UpdateNotesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // UpdateRequestedActivation will update the requested_activation of an order
  rpc UpdateRequestedActivation(UpdateRequestedActivationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // UpdateContractDuration will update the contract_duration of an order
  rpc UpdateContractDuration(UpdateContractDurationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // UpdateEnforceContractTerm will update the enforce_contract_term field on an order
  rpc UpdateEnforceContractTerm(UpdateEnforceContractTermRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // UpdateCurrentRevenue will update the revenue of all the LineItems of the order [deprecated] use UpdateLineItems instead
  rpc UpdateCurrentRevenue(UpdateCurrentRevenueRequest) returns (google.protobuf.Empty){
    option deprecated = true;
  };
  // UpdateLineItems will update the LineItems of the order
  rpc UpdateLineItems(UpdateLineItemsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // UpdatePaymentMethodToken will update the payment method token for an order
  rpc UpdatePaymentMethodToken(UpdatePaymentMethodTokenRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // UpdateCustomerRecipientUserId will update the customer recipient user ID for an order
  rpc UpdateCustomerRecipientUserId(UpdateCustomerRecipientUserIdRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // ListTags returns the unique tags for the filters provided
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // CreateDraftSalesOrder creates an order in a draft state
  rpc CreateDraftSalesOrder(CreateDraftSalesOrderRequest) returns (CreateDraftSalesOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // SubmitDraftSalesOrder updates answers for a last time and changes the status to Submitted
  rpc SubmitDraftSalesOrder(SubmitDraftRequest) returns (SubmitDraftResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // SubmitDraftForCustomerApproval updates answers for a last time and changes the status to SubmittedForCustomerApproval
  rpc SubmitDraftForCustomerApproval(SubmitDraftForApprovalRequest) returns (SubmitDraftForApprovalResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // IgnoreProductActivationResponse marks a product activation as ignore errors
  rpc IgnoreProductActivationError(IgnoreProductActivationErrorRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // IgnoreAllProductActivationErrors marks all product activation as ignore errors for an order
  rpc IgnoreAllProductActivationErrors(IgnoreAllProductActivationErrorsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Cancelling the order will put the order in a cancelled status
  rpc CancelOrder(CancelOrderRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Requesting a cancellation will submit the order to the administrator for cancellation
  rpc RequestCancellation(RequestCancellationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Declining a cancellation request will return the order to its previous status
  rpc DeclineCancellation(DeclineCancellationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // PreviewOrderActivations will return the difference between what is already activated for an account and what is going to be activated in an order
  rpc PreviewOrderActivations(PreviewOrderActivationsRequest) returns (PreviewOrderActivationsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // ApproveCancellation will set an order into the cancelled status
  rpc ApproveCancellation(ApproveCancellationRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // ConvertToDraft will convert a sales order to the draft status
  rpc ConvertToDraft(ConvertToDraftRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // GetStatusCounts returns the counts of the number of orders currently in each status based on the filters
  rpc GetStatusCounts(GetStatusCountsRequest) returns (GetStatusCountsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // CreateInvoice creates a billing invoice with the orders lineItems and returns the id
  rpc CreateInvoice(CreateInvoiceRequest) returns (CreateInvoiceResponse) {
    option (vendastatypes.access) = {
      scope: "purchase"
    };
  };
  // CreateAndActivateOrder creates an order that will go directly into activation
  rpc CreateAndActivateOrder(CreateAndActivateOrderRequest) returns (CreateAndActivateOrderResponse) {
    option (vendastatypes.access) = {
      scope: "purchase"
    };
  };
  // Update customer notes for an order
  rpc UpdateCustomerNotes(UpdateCustomerNotesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update attachments for an order
  rpc UpdateAttachments(UpdateAttachmentsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Update customer attachments for an order
  rpc UpdateCustomerAttachments(UpdateCustomerAttachmentsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Lease a sales order to automations so that an automation can act on it
  // DEPRECATED: Leasing orders no longer supported
  rpc LeaseSalesOrderToAutomations(LeaseSalesOrderToAutomationsRequest) returns (LeaseSalesOrderToAutomationsResponse) {
    option deprecated = true;
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Create a new sales order that is leased to automations so that an automation can act on it
  rpc CreateLeasedToAutomationsSalesOrder(CreateLeasedToAutomationsSalesOrderRequest) returns (CreateLeasedToAutomationsSalesOrderResponse) {
    option deprecated = true;
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Given an opaque access token identifying an external user, return details about that external user
  rpc GetOrderRecipientDetails(GetOrderRecipientDetailsRequest) returns (GetOrderRecipientDetailsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  // Given an order, determine if the order and its line items are in a valid state
  rpc ValidateLineItems(ValidateLineItemsRequest) returns (ValidateLineItemsResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
  // Given an order, determine if the partner can bill the wholesale for the order
  rpc CanOrderBeWholesaleBilled(CanOrderBeWholesaleBilledRequest) returns (CanOrderBeWholesaleBilledResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
}

message ValidateLineItemsRequest {
  // The business the order belongs to
  string business_id = 1;
  // The order to validate
  string order_id = 2;
}

message ValidateLineItemsResponse {
  // The validation errors for the order
  repeated ValidationErrorCodes error_codes = 1;
  // Map of line item index to validation errors
  map<string, ValidationErrorList> line_item_errors = 2;
}

message CreateArchivedOrderRequest {
  Order order = 1;
}

message CreateArchivedOrderResponse {
  string order_id = 1;
}

// Service for doing development work with sales orders
service SalesOrdersDevelopment {
  rpc CreateArchivedOrder(CreateArchivedOrderRequest) returns (CreateArchivedOrderResponse) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
}

service SalesOrdersAuxiliaryFieldData {
  rpc ListAuxiliaryData(ListSalesOderAuxiliaryDataRequest) returns (auxiliarydata.v1.ListAuxiliaryDataResponse) {
    option (vendastatypes.access) = {
      scope: "order"
      scope: "order:read"
    };
  };
  rpc UpsertAuxiliaryData(UpsertSalesOrderAuxiliaryDataRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "order"
    };
  };
}

service SalesOrdersAuxiliaryFieldSchema {
  rpc CreateAuxiliaryDataFieldSchema(auxiliarydata.v1.CreateAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc GetAuxiliaryDataFieldSchema(auxiliarydata.v1.GetAuxiliaryDataFieldSchemaRequest) returns (auxiliarydata.v1.GetAuxiliaryDataFieldSchemaResponse);
  rpc ListAuxiliaryDataFieldSchema(auxiliarydata.v1.ListAuxiliaryDataFieldSchemaRequest) returns (auxiliarydata.v1.ListAuxiliaryDataFieldSchemaResponse);
  rpc UpdateAuxiliaryDataFieldSchema(auxiliarydata.v1.UpdateAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc ArchiveAuxiliaryDataFieldSchema(auxiliarydata.v1.ArchiveAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc UnarchiveAuxiliaryDataFieldSchema(auxiliarydata.v1.UnarchiveAuxiliaryDataFieldSchemaRequest) returns (google.protobuf.Empty);
  rpc GetMultiAuxiliaryDataFieldSchema(auxiliarydata.v1.GetMultiAuxiliaryDataFieldSchemaRequest) returns (auxiliarydata.v1.GetMultiAuxiliaryDataFieldSchemaResponse);
}

// The Actions that a customer can take regarding orders
service CustomerSalesOrders {
  // Api for customer to create a sales order
  rpc CreateSalesOrder(CustomerCreateSalesOrderRequest) returns (CustomerCreateSalesOrderResponse);
  // A Customer approving a sales order
  rpc ApproveSalesOrder(CustomerApproveSalesOrderRequest) returns (CustomerApproveSalesOrderResponse);
  // Update Answers for an Order
  rpc UpdateAnswers(UpdateAnswersRequest) returns (UpdateAnswersResponse);
  // Get data for a sales order
  rpc GetSalesOrder(GetSalesOrderRequest) returns (GetSalesOrderResponse);
  // List sales orders
  rpc ListSalesOrder(ListCustomerSalesOrderRequest) returns (ListSalesOrderResponse);
  // GetUsers gets the users for the agreements of the order
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse);
  // Decline a sales order
  rpc DeclineSalesOrder (DeclineSalesOrderRequest) returns (DeclineResponse);
  // Get configuration details related to a partner's sales orders (Terms and Conditions, Tax Options, Administration Common Form Section)
  rpc GetConfig(CustomerGetConfigurationRequest) returns (GetConfigurationResponse);
  // InitiateOrderExport creates a downloadable CSV of ALL orders for a business
  rpc InitiateOrderExport(InitiateOrderExportRequest) returns (InitiateOrderExportResponse);
  // A Customer wants to create an order with payment information that the Partner must look at and approve
  rpc CreateForPartnerApproval(CreateForPartnerApprovalRequest) returns (CreateForPartnerApprovalResponse);
  // A Customer wants to create a sales order with payment information that will automatically charge and activate the items
  rpc CreateWithPaymentIntent(CreateWithPaymentIntentRequest) returns (CreateWithPaymentIntentResponse);
  // PreviewOrderActivations will return the difference between what is already activated for an account and what is going to be activated in an order
  rpc PreviewOrderActivations(PreviewOrderActivationsRequest) returns (PreviewOrderActivationsResponse);
}

// The Actions that can be taken against a sales order without strong authentication
service Public {
  // A Customer approving a sales order
  rpc ApproveSalesOrder (ApproveRequest) returns (ApproveResponse);
  // A Customer declining a sales order
  rpc DeclineSalesOrder (DeclineRequest) returns (DeclineResponse);
  // Get data for a sales order
  rpc GetSalesOrder (GetRequest) returns (GetResponse);
  // Update Answers for an Order
  rpc UpdateOrderFormAnswers(UpdateOrderFormAnswersRequest) returns (UpdateOrderFormAnswersResponse);
  // Get configuration details related to a partner's sales orders (Terms and Conditions, Tax Options, Administration Common Form Section)
  rpc GetConfig(GetConfigurationRequest) returns (GetConfigurationResponse);
  // Given an opaque access token identifying an external user, return an order_recipient temporary IAM session
  // for allowing actions such as reading, approving, and commenting on proposals and orders
  rpc GetOrderRecipientSession(GetOrderRecipientSessionRequest) returns (GetOrderRecipientSessionResponse);
}

// Service for the vendor perspective to interact with the order system
service VendorOrders {
  // Lists orders for a vendor by the vendors partner id
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
}

message SubscribeRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The location the user is subscribing to (this is where the notification links will send them back to)
  SubscriptionLocation location = 3;
}

message UnsubscribeRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
  // The location the user is subscribing from
  SubscriptionLocation location = 3;
}

// Looks up the subscription status of the caller for the order specified
message GetSubscribedLocationsRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message GetSubscribedLocationsResponse {
  // The locations the user is subscribed to
  repeated SubscriptionLocation locations = 1;
}


message GetSubscribersRequest {
  // Unique identifier of an order
  string order_id = 1;
  // The business the order belongs to
  string business_id = 2;
}

message GetSubscribersResponse {
  // The list of subscribers for the given order
  repeated Subscriber subscribers = 1;
}

service Notifications {
  // Subscribe subscribes the calling user to notifications of status changes to the order
  rpc Subscribe(SubscribeRequest) returns (google.protobuf.Empty);
  // Unsubscribe unsubscribes the calling user to notifications of status changes to the order
  rpc Unsubscribe(UnsubscribeRequest) returns (google.protobuf.Empty);
  // GetSubscribedLocations will return the locations the user is subscribed to
  rpc GetSubscribedLocations(GetSubscribedLocationsRequest) returns (GetSubscribedLocationsResponse);
  // GetSubscribers will return the users that are subscribed to the order
  rpc GetSubscribers(GetSubscribersRequest) returns (GetSubscribersResponse);
}

message CustomerGetConfigurationRequest {
  string order_id = 1;
  string business_id = 2;
}

message CreateReasonRequest {
  // The reason the order was declined.
  string reason = 1;
  // Partner the declined reason belongs to
  string partner_id = 2;
}

message CreateReasonResponse {
  // Created declined reason
  DeclinedReason declined_reason = 1;
}

message UpdateReasonRequest {
  // The id of the reason to update.
  string id = 1;
  // The updated reason
  string reason = 2;
}

message UpdateReasonResponse {
  // Updated declined reason
  DeclinedReason declined_reason = 1;
}

message ArchiveReasonRequest {
  // Declined reason that we want to archive
  string id = 1;
}

message ListReasonsRequest {
  message Filters {
    // Option to include archived reasons.
    bool include_archived = 1;
  }
  // Partner ID that the list of declined reasons belong to
  string partner_id = 1;
  // Filters for listing reasons
  Filters filters = 2;
}

message ListReasonsResponse {
  // List of declined reasons for a partner
  repeated DeclinedReason declined_reasons = 1;
}

message GetReasonRequest {
  // ID of the declined reason to retrieve
  string id = 1;
}

message GetReasonResponse {
  // Declined reason retrieved by ID
  DeclinedReason declined_reason = 1;
}

service DeclinedReasons {
  // Create a new declined reason option
  rpc CreateReason(CreateReasonRequest) returns (CreateReasonResponse);
  // Update an existing declined reason option
  rpc UpdateReason(UpdateReasonRequest) returns (UpdateReasonResponse);
  // Archive an existing declined reason option
  rpc ArchiveReason(ArchiveReasonRequest) returns (google.protobuf.Empty);
  // Retrieve a list of reason options created by a partner
  rpc ListReasons(ListReasonsRequest) returns (ListReasonsResponse);
  // Get declined reason option by ID
  rpc GetReason(GetReasonRequest) returns (GetReasonResponse);
}
