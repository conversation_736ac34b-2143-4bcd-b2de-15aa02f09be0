syntax = "proto3";

package meetings.v1;

option go_package = "github.com/vendasta/generated-protos-go/meetings/v1;meetings_v1";
option java_outer_classname = "MeetingGuestProto";
option java_package = "com.vendasta.meetings.v1.generated";

import "meetings/v1/shared.proto";
import "meetings/v1/meeting_type.proto";
import "google/protobuf/timestamp.proto";
import "google/type/datetime.proto";
import "vendasta_types/date_range.proto";
import "meetings/v1/groups_and_services.proto";

message ListAvailableTimeSlotsRequest {
    string host_id = 1;
    string meeting_type_id = 2;
    vendastatypes.DateRange time_span = 3;
    // time zone that the client prefers the available time slots returned in. An empty time zone will be assumed to be UTC
    // The tz database standard name (also known as tzdata, zoneinfo database, IANA time zone database and Olson database)
    google.type.TimeZone time_zone = 4;
    string user_id = 5;
    // Controls whether the time slots should be returned in timestamp or ISO date format.
    bool use_iso_dates = 6;
}

message IsoDateTimeRange {
    string start = 1;
    string end = 2;
}

message ListAvailableTimeSlotsResponse {
    // Ordered by start time UTC and reflects the requested TimeZone
    repeated vendastatypes.DateRange time_slots = 1;
    repeated IsoDateTimeRange iso_time_slots = 2;
}

message BookMeetingRequest {
    string host_id = 1;
    string meeting_type_id = 2;
    // Deprecated in favour of meeting_start and a calculated meeting end based on the event type duration
    vendastatypes.DateRange time_slot = 3 [deprecated = true];
    repeated Contact attendees = 4;
    // start of the meeting must sent in the client's preferred time zone
    google.protobuf.Timestamp start = 5;
    string comment = 6;

    // metadata will include additional fields and be forwarded onto BookMeeting listeners.
    // the client will use this to pass along metadata from url parameters and additional form fields.
    // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Meeting-Metadata
    map<string, string> metadata = 7;
    Answers form_answers = 8;
    string recaptcha_token = 9;
    string location = 10;
    string user_id = 11;
}

message BookMeetingResponse {
    string meeting_id = 1;
    string host_id = 2;
}

message GuestGetBookedMeetingRequest {
    // identifier for the meeting to query
    string meeting_id = 1;
    string auth_token = 2;
}

message GuestGetBookedMeetingResponse {
    // The time at which the meeting was scheduled to start
    google.protobuf.Timestamp start_time = 1;
    // The time at which the meeting was scheduled to end
    google.protobuf.Timestamp end_time = 2;
    // The timezone the meeting was booked in for the contact
    google.type.TimeZone time_zone = 3;
    string meeting_type_id = 4;
    string location = 5;
    MeetingLocationType location_type = 6;
    string location_guideline = 7;
    TeamEventMeetingType meeting_type = 8;
    string display_name = 9;
    string calendar_id = 10; // used during listAvailableTimeSlots api call when multiple host are involved in reschedule flow
}

message GuestCancelMeetingRequest {
    // identifier for the meeting to cancel
    string meeting_id = 1;
    // Used as a weak form of authentication. Must be sent on the request to verify the guest.
    string auth_token = 2;
    // an optional reason for the cancelation
    string cancellation_reason = 3;
}

message GuestRescheduleMeetingRequest {
    // identifier for the meeting to reschedule
    string meeting_id = 1;
    // Used as a weak form of authentication. Must be sent on the request to verify the guest.
    string auth_token = 2;
    // start is the time the meeting should start
    google.protobuf.Timestamp start = 3;
    // the timezone to reschedule the meeting in. A blank timezone will keep the original timezone
    google.type.TimeZone time_zone = 4;
    string location = 5;
}

message GetHostRequest {
    // TODO: Change host_id to be host_slug.
    // host_id here can act as a host_id or a host_slug:
    // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Slugs
    // The actual returned Host will have the correct host_id and the slug.
    string host_id = 1;
}

message GetHostResponse {
    Host host = 1;
}

message GetCalendarRequest {
    // TODO: Change calendar_id to be calendar_slug.
    // host_id here can act as a calendar_id or a calendar_slug:
    // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Slugs
    // The returned calendar will have the actual identifier
    string calendar_id = 1;
}

message GetCalendarResponse {
    Calendar calendar = 1;
}

message GuestIsHostConfiguredRequest {
    string host_id = 1;
}

message GuestIsHostConfiguredResponse {
    bool is_configured = 1;
}

message ListMeetingTypesRequest {
    string host_id = 1;
}

message ListMeetingTypesResponse {
    repeated MeetingType meeting_types = 1;
}

message GetMeetingTypeRequest {
    // Documentation for slugs: https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Slugs

    // Refers to either the calendar_id or the calendar_slug.
    string calendar_slug = 1;

    // Refers to either the meeting_type_id or the meeting_type_slug.
    string meeting_type_slug = 2;
}

message GetMeetingTypeResponse {
    MeetingType meeting_type = 1;
}

message GetGroupRequest{
    string group_id = 1;
    string slug = 2;
    string host_id = 3;
}

message GetGroupResponse{
    Group group = 1;
    string business_display_logo_url = 2;
}
message GetServiceRequest{
  string service_id = 1;
  string slug = 2;
  string host_id = 3;
}

message GetServiceResponse{
  Service service = 1;
  string business_display_logo_url = 2;
}

