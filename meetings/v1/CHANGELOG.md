# Change Log

This file includes v0 and v1 change logs for package `vendastapis/meetings/v1`.
All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

---

## v0


## 0.64.3
 
- Add `is_connected` field to `HostUser` struct

## 0.64.2

- Add `EnsureSessionEventCalendarsExist` in MeetingHost

## 0.64.1

- Add `attendee_count` field in `MeetingType` struct
- Add `registration_cutoff` field in `MeetingType` struct
- Add `session_title` field in `MeetingType` struct

## 0.64.0

- Add `use_iso_dates` to `ListAvailableTimeSlotsRequest`
- Add `iso_time_slots` to `ListAvailableTimeSlotsResponse`

## 0.63.0

- Add `Oragnizer` to `GoogleMeetMeeting`

## 0.62.0

- Add `ListMeetings` RPC to `GoogleMeet` service

## 0.61.1

- Remove `is_client_choice` field in `MeetingType`

## 0.61.0

- Add `OnBoardingState` enum field in `IsHostConfiguredResponse` and `Preferences`

## 0.60.0

- Add `GuestGetBookedMeetingResponse` struct add `MeetingType`, `DisplayName`, `CalendarID` struct fields

## 0.59.1

- Add `MeetingType` struct add `TeamEventTypeMeetingType` enum field
- Add `Meeting` struct add `HostDetails` struct field

## 0.58.0

- Add `date_range` field in `MeetingType`

## 0.57.1

- Add `CalendarSource` in `preference`

# 0.57.0

- Add `connection_label` field in `MeetingSourceInfo` struct

## 0.56.0

- Add `ScheduleMeetingBot` rpc and remove unused RPCs

# 0.55.0

- Add `business_display_logo_url` in `GetGroupResponse` & `GetServiceResponse`

# 0.54.1

- Add `calendar_id` in `meeting_host.proto` - `meeting` struct

# 0.54.0

- Add microsoft as a new meeting source

# 0.53.1

- Add `GetEntityAssociations API` in host

# 0.53.0

- Added `meeting_bot.proto` defining the `MeetingBot` message and `MeetingBotAPI` service.

# 0.52.3

- Reserve `google_meet_scope` field in `MeetingSourceInfo` struct

# 0.52.2

- Add `HostBookMeeting API` in host

# 0.52.1

- Add `google_meet_scope` in MeetingSourceInfo

# 0.52.0

- Add `CheckGroupOrServiceSlugExist API` in host

# 0.51.0

- Add `origin` to `MeetingSourceListRequest` message
  - Used to exclude certain centers from the Meeting Analysis feature

# 0.50.1

- Add `before buffer` field in `MeetingType` struct

# 0.50.0

- Add `location_guideline` field in `GuestGetBookedMeetingResponse` struct
- Add `meeting_location_type` field in `GuestGetBookedMeetingResponse` struct
- Add `location` field in `GuestRescheduleMeetingRequest` struct

# 0.49.9

- Add `location` field in `GuestGetBookedMeetingResponse` struct

# 0.49.8

- Reserve `created` and `updated` fields in `MeetingSourceInfo` struct

# 0.49.7

- Add `hostId` in `GetGroupRequest` and `GetServiceRequest`

# 0.49.6

- Add `GetService API` in host
- Add `GetGroup API` in host

# 0.49.5

- Add `GetService API`
- Add `GetServiceRequest` and `GetServiceResponse`

# 0.49.4

- Add two fields `created` and `updated` in `MeetingSourceInfo` struct

# 0.49.3

- -Add `slug` in `GetGroupRequest` struct
- -Add `booking_url` in `GetGroupResponse` struct

# 0.49.2

- Add `location_type` in `MeetingType` struct
- Add `isVideoLinkDisabled` in `MeetingType` struct
- Remove `is_client_site_meeting` in `MeetingType` struct

# 0.49.1

- Add encoded application context in Calendar struct

# 0.49.0

- Add migration rpc

# 0.48.1

- Add `team_name` in `MeetingType` struct

# 0.48.0

- Add `HostUsers` in `MeetingType` struct

## 0.47.1

-Added `host_id` to `DeleteGroupRequest` and `DeleteServiceRequest`
-Added `hex_color`,`booking_url` to `Group` and `Service`

# 0.47.0

- Created the proto for `CreateGroup`, `UpdateGroup`, `DeleteGroup`, `ListGroups`,`CreateService`, `UpdateService`, `DeleteService`, `ListServices`

# 0.46.0

- Add `namespace` to `MeetingSourceListRequest`
  - This will be used to add a namespace to the connectionURL's state object

# 0.45.0

- Added `calendar_type` properties to MeetingType

# 0.44.0

- Added `is_pinned` properties to MeetingType

# 0.43.0

- Added `email_subject` and `email_description` properties to MeetingType

# 0.42.0

- Added host usernames in `GetMeetingTypeResponse` in guest service

# 0.41.1

- Added `user_id` property to BookMeetingRequest, HostBookMeetingRequest, and ListAvailableTimeSlotsRequest

# 0.41.0

- Added `isClientChoice` property to MeetingType

# 0.40.0

- Changed the data type of calenderId from string to array of string in `IsCalendarConfiguredResponse`

# 0.39.1

- Added `location`, `isClientSiteMeeting` & `locationRadius` to Meeting Type Related Request and Response

# 0.39.0

- Add `preferences` to CreateMeetingTypeRequest and `field_mask` & `preferences` to UpdateMeetingTypeRequest

# 0.38.0

- Removed conversational analysis proto

# 0.37.1

- Add CUD dates to the `ProcessedTranscript` message

# 0.37.0

- Add `ListProcessedTranscripts` rpc and `GetProcessedTranscript` rpc

# 0.36.0

- Add `SendMeetingRequestEmail` rpc

# 0.35.0

- Add the conversation analysis service and create transcript rpc

# 0.34.0

- Deprecate calendar_id on `CreateDefaultMeetingTypes` in favour of multi `calendar_ids`

# 0.33.0

- Add GetMeetingTypesForCalendars RPC

# 0.32.1

- Undeprecate iam_user_id param on MeetingSourceListRequest. Support for grabbing the userID from the context is being removed

# 0.32.0

- Add booking_url to MeetingType as a derived field when it is a response

# 0.31.0

- Add host_user_ids to MeetingType

# 0.30.0

- Add calendar_type to Calendar
- Add EnsurePersonalCalendarExists RPC

# 0.29.0

- Add booking_url to Calendar

# 0.28.0

- Add SELECT FormFieldType. There still is no way to set the options.

# 0.27.0

- Add the messages for Custom Form Fields
  - Add FormAnswers, FormFieldType, Field, and Form messages
  - Add FormAnswers to BookMeetingRequest and Meeting message
  - Add Form to MeetingType message

# 0.26.0

- Add `notice_time` to Preferences object

# 0.25.0

- Add `meeting_id` and `host_id` to BookMeetingResponse

# 0.24.1

- Rename `MeetingType.booking_link` to `MeetingType.meeting_type_slug`
- Add `is_public` field to `MeetingType` message

# 0.24.0

- Add `GetMeetingType` to `Host` and `Guest` APIs

### 0.23.0

- Add `isPrimary` to the `Attendee` message.

### 0.22.0

- Added `buffer_duration_after_meeting` to `Preferences` for the Host.

### 0.21.0

- Add is_primary field to `Contact` message
- Make contact repeated on `BookMeetingRequest`

### 0.20.0

- Add meeting_type_id to `GuestGetBookedMeetingResponse`

### 0.19.0

- Add IsHostConfigured RPC for the GuestService

### 0.18.0

- Added `BookMeeting` and `GetHostMeeting` requests to the HostService

### 0.17.0

- Added `UpdateHostPreferences` and `GetHostPreferences` requests to the HostService

### 0.16.0 - 2020-06-03

- Add timezone to `GuestGetBookedMeetingResponse` and `GuestRescheduleMeetingRequest`

### 0.15.0 - 2020-06-01

- Add `metadata` to the BookMeetingRequest to allow passing along additional data with a saved event.

### 0.15.0 - 2020-05-19

- Add join_meeting_URL field to meeting proto

### 0.14.0 - 2020-05-13

- Add CancelMeeting and RescheduleMeeting RPCs for the GuestService

### 0.13.1 - 2020-05-11

- Add cancellation_reason to the CancelMeeting RPC

### 0.13.0 - 2020-05-09

- Add CancelMeeting and RescheduleMeeting request

### 0.12.0 - 2020-04-30

- Add email and phone number to Attendee

### 0.11.0 - 2020-04-30

- Add Attendees to Meeting message for ListBookedMeetings response

### 0.10.1 - 2020-04-29

- Deprecate `calendar_id` parameter from `GoogleMeetCreateMeetingRequest` message.

### 0.10.0 - 2020-04-27

- Add `MEETING_SOURCE_STATUS_BROKEN` to `MeetingSourceStatus` enum.

## 0.9.0 - 2020-04-27

- Add `time_zone` field to all `MeetingHost` and `MeetingGuest` service rpcs that require a time zone parameter

### 0.8.0 - 2020-04-24

- Add `time_span` field to `Filters` of `ListBookedMeetingsRequest`.

### 0.7.0 - 2020-04-21

- Add `GoogleMeet.CreateMeeting` RPC

### 0.6.0 - 2020-04-21

- Add ability to send comment along with a `bookMeeting` request

### 0.5.0 - 2020-04-16

- Add `GetHost` RPC to `BookMeeting` service
- Add details to `ListMeetingtypes` RPC
- Deprecate time span in `BookMeeting` and replace with meeting start field

### 0.4.0 - 2020-04-16

- Add `HostMeeting` and `BookMeeting` services.

### 0.3.2 - 2020-04-14

- Deprecate `IAMUserID` in request body of `zoom.CreateMeeting` RPC.

### 0.3.1 - 2020-04-13

- Deprecate `IAMUserID` in request body of `MeeintSourceAPI.List` RPC.

### 0.3.0 - 2020-04-07

- Remove RPC `GetOauth2URL` in `Zoom` RPC server as we don't need it anymore.
- AddAdd RPC `CreatMeeting` to `Zoom` RPC server.

### 0.2.0 - 2020-03-30

- Add RPC `List` to `MeetingSource` RPC server.

### 0.1.0 - 2020-03-25

- Initial release.
- Add RPC `GetOauth2URL` to `Zoom` RPC server.
