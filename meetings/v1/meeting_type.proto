syntax = "proto3";

package meetings.v1;

option go_package = "github.com/vendasta/generated-protos-go/meetings/v1;meetings_v1";
option java_outer_classname = "MeetingTypeProto";
option java_package = "com.vendasta.meetings.v1.generated";

import "google/protobuf/duration.proto";
import "meetings/v1/shared.proto";
import "vendasta_types/date_range.proto";

enum TeamEventMeetingType {
  ROUND_ROBIN = 0;
  CLIENT_CHOICE = 1;
  MULTI_HOST = 2;
  PRIORITY_ROUND_ROBIN = 3;
}

enum MeetingLocationType {
  VIDEO = 0;
  IN_PERSON_USER_SITE = 1;
  IN_PERSON_CLIENT_SITE = 2;
}

enum DateRangeType {
  RELATIVE = 0;
  CUSTOM = 1;
}

enum RelativeTimeUnit {
  DAYS = 0;
  WEEKS = 1;
  MONTHS = 2;
}

message RelativeDateRange {
  RelativeTimeUnit unit =1;
  int64 value = 2;
}

message EventTypeDateRange{
  DateRangeType date_range_type = 1;
  oneof date_range {
    vendastatypes.DateRange custom_date_range = 2;
    RelativeDateRange relative_date_range = 3;
  }
}


message MeetingType {
    string id = 1;
    string name = 2;
    google.protobuf.Duration duration = 3;
    string calendar_id = 4;
    string description = 5;
    string hex_color = 6;
    string meeting_type_slug = 7;
    bool is_private = 8;
    Form form = 9;
    bool is_reserve_with_google_service = 10;
    repeated string host_user_ids = 11;
    // Derived booking_url of the form https://bookmenow.info/book/calendar-slug/meeting-type-slug for responses
    string booking_url = 12;
    int64 availability_increment = 13;
    int64 buffer_duration_after_meeting = 14;
    int64 notice_time = 15;
    string location = 16;
    MeetingLocationType location_type = 17;
    string location_guidelines = 18;
    string email_subject=19;
    string email_description=20;
    bool is_pinned=21;
    string calendar_type=22;
    repeated HostUser host_users = 23;
    string team_name=24;
    bool is_video_link_disabled=25;
    int64 buffer_duration_before_meeting = 26;
    EventTypeDateRange date_range=27;
    TeamEventMeetingType meeting_type = 28;
    // These 3 fields are used only for the session type
    int64 attendee_count=29;
    int64 registration_cutoff=30;
    string session_title=31;
}

message Form {
    // The order of fields specified here is the order they will be displayed in
    repeated Field fields = 1;
}

message Field {
    string id = 1;
    string label = 2;
    FormFieldType type = 3;
    bool required = 4;
}
