syntax = "proto3";

package meetings.v1;

option go_package = "github.com/vendasta/generated-protos-go/meetings/v1;meetings_v1";
option java_outer_classname = "GoogleMeetProto";
option java_package = "com.vendasta.meetings.v1.generated";

import "google/protobuf/timestamp.proto";

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1842
message GoogleMeetMeetingAttendee {
    int64 additional_guests = 1;
    string comment = 2;
    string display_name = 3;
    string email = 4;
    string id = 5;
    bool optional = 6;
    bool organizer = 7;
    bool resource = 8;
    string response_status = 9;
    bool self = 10;
}

// https://github.com/googleapis/google-api-go-client/blob/master/calendar/v3/calendar-gen.go#L1059
message GoogleMeetConferenceSolutionKey {
    string type = 1;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L990
message GoogleMeetConferenceRequestStatus {
    string status_code = 1;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1135
message GoogleMeetEntryPoint {
    string access_code = 1;
    repeated string entry_point_features = 2;
    string entry_point_type = 3;
    string label = 4;
    string meeting_code = 5;
    string passcode = 6;
    string password = 7;
    string pin = 8;
    string region_code = 9;
    string uri = 10;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1096
message GoogleMeetCreateConferenceRequest {
    GoogleMeetConferenceSolutionKey conference_solution_key = 1;
    string request_id = 2;
    GoogleMeetConferenceRequestStatus status = 3;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1025
message GoogleMeetConferenceSolution {
    string icon_uri = 1;
    GoogleMeetConferenceSolutionKey key = 2;
    string name = 3;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L829
message GoogleMeetConferenceData {
    string conference_id = 1;
    GoogleMeetConferenceSolution conference_solution = 2;
    GoogleMeetCreateConferenceRequest create_request = 3;
    repeated GoogleMeetEntryPoint entry_points = 4;
    string notes = 5;
    string signature = 6;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1556
message GoogleMeetMeetingCreator {
    string display_name = 1;
    string email = 2;
    string id = 3;
    bool self = 4;
}

// https://github.com/googleapis/google-api-go-client/blob/main/calendar/v3/calendar-gen.go#L1457
message GoogleMeetMeetingOrganizer {
    string display_name = 1;
    string email = 2;
    string id = 3;
    bool self = 4;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1914
message GoogleMeetMeetingDateTime {
    string date = 1;
    string date_time = 2;
    string time_zone = 3;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1961
message GoogleMeetMeetingReminder {
    string method = 1;
    int64 minutes = 2;
}

// https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1734
message GoogleMeetMeetingReminders {
    repeated GoogleMeetMeetingReminder overrides = 1;
    bool use_default = 2;
}

// Google meet meetings (a.k.a events of google calendar)
// Check https://github.com/google/google-api-go-client/tree/master/calendar/v3/calendar-gen.go#L1289 for more details of parameters.
message GoogleMeetMeeting {
    bool anyone_can_add_self = 1;
    repeated GoogleMeetMeetingAttendee attendees = 2;
    bool attendees_omitted = 3;
    string color_id = 4;
    GoogleMeetConferenceData conference_data = 5;
    string description = 6;
    GoogleMeetMeetingDateTime end = 7;
    bool end_time_unspecified = 8;
    string etag = 9;
    string ical_uid = 10;
    string id = 11;
    string kind = 12;
    string location = 13;
    bool locked = 14;
    GoogleMeetMeetingDateTime original_start_time = 15;
    repeated string recurrence = 16;
    string recurring_meeting_id = 17;
    GoogleMeetMeetingReminders reminders = 18;
    int64 sequence = 19;
    GoogleMeetMeetingDateTime start = 20;
    string status = 21;
    string summary = 22;
    string transparency = 23;
    string visibility = 24;
    // Read only properties
    string hangout_link = 25;
    string html_link = 26;
    string created = 27;
    GoogleMeetMeetingCreator creator = 28;
    string updated = 29;
    GoogleMeetMeetingOrganizer organizer = 30;
}

// Request body for creating a google-meet meeting.
message GoogleMeetCreateMeetingRequest {
    // Google Calendar ID. a.k.a Google emails.
    // DEPRECATED: DO NOT use this param, the backend service will save calendar ID with auth token in the database.
    string calender_id = 1;
    GoogleMeetMeeting meeting = 2;
}

// Request body of creating a google-meet meeting.
message GoogleMeetCreateMeetingResponse {
    string iam_user_id = 1;
    string calender_id = 2;
    GoogleMeetMeeting meeting = 3;
}

message GoogleMeetListMeetingsRequest {
  // UserID to list meetings for
  string user_id = 1;
  // The maximum number of meetings to return in the response.
  int64 page_size = 2;
  // A token to specify where to start paginating. This is the nextPageToken returned from a previous response.
  string page_token = 3;
  // Used to get only the changed events since last List call
  string sync_token = 4;
  // Filters for meetings starting after this time.
  google.protobuf.Timestamp time_min = 5;
  // Filters for meetings starting before this time.
  google.protobuf.Timestamp time_max = 6;
}

message GoogleMeetListMeetingsResponse {
  repeated GoogleMeetMeeting meetings = 1;
  // Cursor used to get the next page of results. If set, nextSyncToken will be empty.
  string next_page_token = 2;
  // Token used to get only the changed events since this call. Signifies the end of this paginated set of results. If set, nextPageToken will be empty.
  string next_sync_token = 3;
}
