syntax = "proto3";

package meetings.v1;

option go_package = "github.com/vendasta/generated-protos-go/meetings/v1;meetings_v1";
option java_outer_classname = "SharedProto";
option java_package = "com.vendasta.meetings.v1.generated";

import "meetings/v1/meeting_source.proto";
import "google/type/timeofday.proto";
import "google/type/datetime.proto";

enum CalendarType {
  CALENDAR_TYPE_INVALID = 0;
  CALENDAR_TYPE_PERSONAL = 1;
  CALENDAR_TYPE_GROUP = 2;
}

message Calendar {
  // The immutable, generated, globally unique identifier of a calendar
  // Attempting to set or mutate this will have no effect, use external_id
  string id = 1;
  // The name of the calendar
  string display_name = 2;
  // Squared image for the calendar to be used as an icon
  string display_profile_url = 3;
  // Larger image to be used as a banner for example
  string display_logo_url = 4;
  // A valid, public URL to book a time slot on this calendar
  string booking_url = 5;
  // A mutable namespaced identifier of this calendar for the purposes of building memorable URLs
  string slug = 6;
  // A mutable namespaced identifier of this calendar that has meaning to whoever created it
  // This is effectively the result of BuildHostId at this time
  string external_id = 7;
  // Who created the calendar
  string creator_user_id = 8;
  // Who is a possible, but not necessarily available, host of the calendar (at least 1)
  repeated string host_user_ids = 9;
  // Key-value pairs that provide context to the use of the calendar.
  // There is currently inflexible support for a handful of backend
  // integrations with other Vendasta data models to derive some of
  // the fields of the calendar. These known pairs are:
  //   - external_integration: [v_sales_team, v_business]
  //   - sales_team_id: G-123
  //   - business_id: AG-123
  // For example, if you wanted to contextualize a sales team's calendar you would use:
  //   {'external_integration': 'v_sales_team', 'sales_team_id': 'G-123'}
  // If you wanted to contextualize a business calendar you would use:
  //   {'external_integration': 'v_business', 'business_id': 'AG-123'}
  // If you wanted to contextualize and individual salesperson you would use:
  //   {'user_context': 'sales', 'partner_id': 'ABC', 'market_id': 'DEFAULT'}
  map<string, string> application_context = 10;

  CalendarType calendar_type = 11;

  string encoded_application_context =12;
}

// TimeRange fields need be in UTC.
message TimeRange {
  google.type.TimeOfDay from = 1;
  google.type.TimeOfDay to = 2;
}

message PagedRequestOptions {
  // cursor can be passed to retrieve the next page of results keyed by the cursor
  string cursor = 1;
  // page_size specifies the number of items to return in the next page
  int64 page_size = 2;
}

message PagedResponseMetadata {
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 1;
  // Whether or not more results exist
  bool has_more = 2;
}


// TODO: For v2 of API, we'd like to merge Contact and Attendee into one message type
message Attendee {
  // the first name of the attendee
  string first_name = 1;
  // the last name of the attendee
  string last_name = 2;
  // email of the attendee
  string email = 3;
  // phone number of the attendee
  string phone_number = 4;
  string time_zone = 5;
  bool is_primary = 6;
}

// TODO: For v2 of API, we'd like to merge Contact and Attendee into one message type
message Contact {
  string first_name = 1;
  string last_name = 2;
  string phone_number = 3;
  string email = 4;
  // The preferred timezone for all future contact with this guest
  // Empty timezone assumed to be UTC
  google.type.TimeZone time_zone = 5;
  bool is_primary = 6;
}

message Preferences {
  google.type.TimeZone timezone = 1;
  MeetingSource meeting_integration = 2;

  // How availability is split into smaller pieces - specified in seconds.
  // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/*********/Terminology#Availability-Increments
  int64 availability_increment = 3;

  // The length of time that should be left unscheduled after a meeting - specified in seconds.
  // See https://vendasta.jira.com/wiki/spaces/BOOKME/pages/*********/Terminology#Meeting-Buffer-Time
  int64 buffer_duration_after_meeting = 4;
  // The minimum required duration from now that a meeting can be scheduled - specified in seconds.
  // See https://vendasta.jira.com/wiki/spaces/BOOKME/pages/*********/Terminology#Meeting-Notice-Time
  int64 notice_time = 5;

  // The slug that can be used for routing to the booking client for this calendar.
  // I.e., if this was vendasta the link https://bookmenow.info/vendasta would link to this calendar.
  // slugs are unique to a calendar.
  // DANGER: Changing this slug may BREAK existing links under the previous slug.
  // See https://vendasta.jira.com/wiki/spaces/BOOKME/pages/*********/Terminology#Calendar-Slug
  string calendar_slug = 6;
  int64 buffer_duration_before_meeting = 7;
  CalendarSource calendar_integration =8;
  OnBoardingState on_boarding_state =9;
}

message Answers {
  // The order of fields specified here is the order they will be displayed in
  repeated Answer form_answers = 1;
}

message Answer {
  string id = 1;
  string label = 2;
  FormFieldType type = 3;
  repeated string answer = 4;
}

message Attachment{
  string file_title = 1;
  string file_url = 2;
  string file_type = 3;
}

enum OnBoardingState {
  COMPLETED_SETUP         = 0;
  NOT_YET_STARTED         = 1;
  SLUG_CREATED            = 2;
  CALENDAR_CONNECTED      = 3;
  MEETING_APP_CONNECTED   = 4;
}

enum FormFieldType {
  FORM_FIELD_TYPE_INVALID = 0;
  FORM_FIELD_TYPE_TEXT = 1;
  FORM_FIELD_TYPE_EMAIL = 2;
  FORM_FIELD_TYPE_PHONE_NUMBER = 3;
  FORM_FIELD_TYPE_SELECT = 4;
}
message Host {
  string id = 1;
  string display_name = 2;
  string display_profile_url = 3;
  string display_logo_url = 4;
}

message HostUser {
  string user_id = 1;
  string display_name = 2;
  bool is_configured = 3;
  bool is_connected = 4;
}
