syntax = "proto3";

package meetings.v1;

option go_package = "github.com/vendasta/generated-protos-go/meetings/v1;meetings_v1";
option java_outer_classname = "MeetingHostProto";
option java_package = "com.vendasta.meetings.v1.generated";

import "meetings/v1/shared.proto";
import "meetings/v1/meeting_source.proto";
import "meetings/v1/meeting_type.proto";
import "vendasta_types/date_range.proto";
import "google/type/dayofweek.proto";
import "google/type/datetime.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/field_mask.proto";
import "meetings/v1/groups_and_services.proto";

message BuildHostIdRequest {
    // Application Context properties are properties that hold information for the purposes of a consumer-facing brand
    // used when displaying partner attribution or following platform configurations.
    // This allows users with multiple consumer-facing brands / configurations (application contexts) to provide end-users
    // with the appropriate configuration and branding.
    // In Vendasta, this can take the form of a combination of the following:
    // -> partner_id: The partner id the user belongs to / acting on behalf of (Task Manager)
    // -> market_id: The market the user is operating under.
    // -> business_id: The business the user is operating under.
    // -> user_id: The user_id corresponding to the host.
    // While the properties you initially provide are not important, it is currently important to communicate with Meeting Scheduler
    // using the same set of properties every time. Here are some guidelines:
    // Salesperson: Don't use this. Instead, pass `subject_id` as the salespersonId. This could change in the future.
    // Task Manager: don't use this. Use the current Task Manager id format TM_PID_ABC_MARKET_DEFAULT_USERID. This could change in the future.
    // Partner: provide partner_id only.
    // SMB: provide the business_id only. Determines what features of Meeting Scheduler the user has access to and allows them to perform actions on behalf of that business.
    map<string, string> application_context_properties = 1;

    // The user_id of the user (not subjectId)
    string user_id = 2;
}

message BuildHostIdResponse {
    // The host_id you can use for the rest of the APIs.
    // This is also suitable to use in the booking url (but prefer slugs from the host's preferences).
    string host_id = 1;
}

message DoesCalendarExistRequest {
    // Any valid identifier for a calendar
    string calendar_identifier = 1;
}

message DoesCalendarExistResponse {
    // Whether a calendar with the given identifer exists already
    bool exists = 1;
}

message CreateCalendarRequest {
    Calendar calendar = 1;
}

message CreateCalendarResponse {
    string calendar_id = 1;
}

message HostGetCalendarRequest {
    string calendar_id = 1;
}

message HostGetCalendarResponse {
    Calendar calendar = 1;
}

message UpdateCalendarRequest {
    // Strictly the calendar_id, update will not look up the model with any alternative identifier
    string calendar_id = 1;
    // The updated model, calendar_id cannot be updated
    Calendar calendar_update = 2;
    // Currently supported field masks are [slug, host_user_ids], more may be added over time
    // Any fields specified in the field_mask will replace the current value in the calendar
    // with the value from calendar_update, including repeated or nested fields.
    vendastatypes.FieldMask field_mask = 3;
}

message EnsurePersonalCalendarExistsRequest {
    string user_id = 1;
    // See application_context_properties on BuildHostId / the Calendar
    // This request ensures a calendar with these application context properties exist. If it doesn't one is created.
    map<string, string> application_context_properties = 2;
}

message EnsurePersonalCalendarExistsResponse {
    // The calendar_id belonging to the user having the given application_context_properties
    // subsequence with the same user_id and application_context_properties will return the same calendar_id.
    // A single property change in application_context_properties will result in a new calendar.
    string calendar_id = 1;
}


message EnsureGroupCalendarsExistRequest {
  // external_id can be one of: a sales team id, a business id (agid).
  repeated string external_ids = 1;
}

message EnsureGroupCalendarsExistResponse {
  // The calendar_ids belonging to the calendars with the given external_ids.
  // The order of the returned calendar_ids is guaranteed to match the order the external_ids were given in.
  // For every calendar_id, calendar_ids[i] is the calendar_id for external_ids[i]
  // On subsequent calls this response is guaranteed to be the same.
  repeated string calendar_ids = 1;
}

message EnsureSessionEventCalendarsExistRequest {
  // external_id can be one of: a pid, a business id (agid).
  repeated string external_ids = 1;
}

message EnsureSessionEventCalendarsExistResponse {
  // The calendar_ids belonging to the calendars with the given external_ids.
  // The order of the returned calendar_ids is guaranteed to match the order the external_ids were given in.
  // For every calendar_id, calendar_ids[i] is the calendar_id for external_ids[i]
  // On subsequent calls this response is guaranteed to be the same.
  repeated string calendar_ids = 1;
}

message HostDetails {
  string host_id = 1;
  string name = 2;
}

message Meeting {
    string id = 1;
    string event_type_id = 2;
    string host_id = 3;
    google.protobuf.Timestamp start = 4;
    google.protobuf.Timestamp end = 5;
    google.protobuf.Timestamp created = 6;
    google.protobuf.Timestamp updated = 7;
    repeated Attendee attendees = 8;
    string description = 9;
    string join_meeting_url = 10;
    string topic = 11;
    Answers form_answers = 12;

    // metadata will include additional fields from the url during the booking process (msm_business, utm_campaign)
    // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Meeting-Metadata
    map<string, string> metadata = 13;
    repeated Attachment attachments = 14;
    string location = 15;
    string calendar_id = 16;
    repeated HostDetails host_details = 17;
    TeamEventMeetingType meeting_type = 18;
}

message UpdateMeetingMetadataRequest {
    string meeting_id = 1;
    // The metadata sent in this request will only overwrite / change the same keys in the already-existing metadata.
    // This is because some metadata is only set during a booking.
    // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Meeting-Metadata
    map<string, string> metadata = 2;
}

message WeekdayAvailability {
    google.type.DayOfWeek day = 1;
    repeated TimeRange time_slots = 2;
}

message SetGeneralAvailabilityRequest {
    // The host identifier to set the availability for
    string host_id = 1;
    // an optional meeting type id. Not providing a meeting type id will make the availability work for all types.
    string meeting_type_id = 2;
    // The specific days you wish to set the availabilities for.
    // Deprecated in favour of weekdays_availability
    repeated google.type.DayOfWeek days = 3 [deprecated = true];
    // The time slot the availability is open on all of the given days
    // Deprecated in favour of weekdays_availability
    TimeRange time_slot = 4 [deprecated = true];
    // the time zone of the host's availability. An empty time zone will be assumed to be UTC
    // The tz database standard name (also known as tzdata, zoneinfo database, IANA time zone database and Olson database)
    google.type.TimeZone time_zone = 5;
    // The availability for each day in a week
    // Repeating the same day more than once results in an error
    repeated WeekdayAvailability weekdays_availability = 6;
}

message CreateAvailabilityRequest {
}

message CreateAvailabilityResponse {
}

message UpdateAvailabilityRequest {
}

message DeleteAvailabilityRequest {
}

message ListAvailabilityRequest {
    message Filters {
        string host_id = 1;
        // meeting type for the availability. OPTIONAL - empty will return all
        string meeting_type_id = 2;
    }

    PagedRequestOptions paging_options = 1;
    Filters filters = 2;
    // time zone that the client prefers the booked meeting times returned in. An empty time zone will be assumed to be UTC.
    // The tz database standard name (also known as tzdata, zoneinfo database, IANA time zone database and Olson database)
    google.type.TimeZone time_zone = 3;
}

message AvailabilityRule {
    string host_id = 1;
    string meeting_type_id = 2;
    google.type.DayOfWeek day = 3;
    TimeRange time_slot = 4;
    google.type.TimeZone time_zone = 5;
}

message ListAvailabilityResponse {
    repeated AvailabilityRule availability_rules = 1;
    PagedResponseMetadata metadata = 2;
}


message ListBookedMeetingsRequest {
    message Filters {
        // The identifiers of a meeting types to be filtered on
        repeated string meeting_type_ids = 1;
        // The identifier of the host to list the meetings for
        string host_id = 2;
        // The date range of meetings to filter for - this range must be specific to the client's time zone
        vendastatypes.DateRange time_span = 3;
    }
    // Filters to apply when listing booked meetings
    Filters filters = 1;
    // Options for how to page the response for this request
    PagedRequestOptions paging_options = 2;
    // time zone that the client prefers the booked meeting times returned in. An empty time zone will be assumed to be UTC.
    // The tz database standard name (also known as tzdata, zoneinfo database, IANA time zone database and Olson database)
    google.type.TimeZone time_zone = 3;
}

message ListBookedMeetingsResponse {
    repeated Meeting meetings = 1;
    // Metadata about the paging
    PagedResponseMetadata paging_metadata = 2;
}

message IsHostConfiguredRequest {
    // TODO: Change host_id to be host_slug.
    // host_id here can act as a host_id or a host_slug:
    // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Slugs
    string host_id = 1;
}

message IsHostConfiguredResponse {
    bool is_configured = 1;
    OnBoardingState on_boarding_state = 2;
}

message CancelMeetingRequest {
    // identifier for the meeting to cancel
    string meeting_id = 1;

    // an optional reason for the cancelation
    string cancellation_reason = 2;
}

message RescheduleMeetingRequest {
    // identifier for the meeting to reschedule
    string meeting_id = 1;
    // start is the time the meeting should start
    google.protobuf.Timestamp start = 2;
}

message HostBookMeetingRequest {
    MeetingSource meeting_source = 1;
    string calendar_id = 2;
    string meeting_type_id = 3;
    vendastatypes.DateRange time_slot = 4;
    repeated Contact attendees = 5;
    string topic = 6;
    string user_id = 7;
}

message HostBookMeetingResponse {
    string meeting_id = 1;
}

message GetHostMeetingRequest {
    string meeting_id = 1;
    string location = 2;
}
message GetHostMeetingResponse {
    Meeting meeting = 1;
}

// UpdateHostPreferences will only modify the preferences defined in the field_mask.
// A lack of field_mask resulting in an error is a modified, but acceptable, behaviour from the FieldMask spec.
// See https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
message UpdateHostPreferencesRequest {
    string host_id = 1;
    Preferences preferences = 2;
    vendastatypes.FieldMask field_mask = 3;
}

message GetHostPreferencesRequest {
    string host_id = 1;
}

message GetHostPreferencesResponse {
    Preferences preferences = 1;
}

message CreateMeetingTypeRequest {
  MeetingType meeting_type = 1;
  vendastatypes.FieldMask field_mask = 2;
  string host_id = 3;
}

message CreateMeetingTypeResponse {
  string id = 1;
}

message UpdateMeetingTypeRequest {
  string id = 1;
  MeetingType meeting_type = 2;
  vendastatypes.FieldMask field_mask = 3;
  string host_id = 4;
}

message DeleteMeetingTypeRequest {
    string id = 1;
}

message HostListMeetingTypesRequest {
    string host_id = 1;
}

message HostListMeetingTypesResponse {
    repeated MeetingType meeting_types = 1;
}


message HostGetMeetingTypeRequest {
    // Documentation for slugs: https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Slugs

    // Refers to either the calendar_id or the calendar_slug.
    string calendar_slug = 1;

    // Refers to either the meeting_type_id or the meeting_type_slug.
    string meeting_type_slug = 2;
}

message HostGetMeetingTypeResponse {
    MeetingType meeting_type = 1;
}

message CreateDefaultMeetingTypesRequest {
    // Deprecated in favour of calendar_ids instead
    string calendar_id = 1;

    repeated string calendar_ids = 2;
}

message GetMeetingTypesForCalendarsRequest {
  repeated string calendar_ids = 1;
}

message GetMeetingTypesForCalendarsResponse {
    // map is from string to list of meeting types
    // Each calendar_id passed in the request is in the map - even if there are no meeting types for the calendar
    map<string, MeetingTypeList> calendar_meeting_types_map = 1;
}

// This message is necessary as a map of string to repeated property is not supported in proto3 at this time.
message MeetingTypeList {
  repeated MeetingType meeting_types = 1;
}

message GetHostsForCalendarRequest {
  string calendar_id = 1;
  string timezone = 2;
}

message AvailabilityRuleList {
  repeated AvailabilityRule rules = 1;
}

message GetHostsForCalendarResponse {
  repeated HostUser host_users = 1;
  map<string, AvailabilityRuleList> host_user_availability_rule= 2;
}


message SendMeetingRequestEmailRequest{

  string contact_id=1;
  string user_id=2;
  string meeting_type_id=3;
}

message IsCalendarConfiguredRequest {
  map<string, string> application_context_properties = 1;
  string user_id = 2;
}

message IsCalendarConfiguredResponse {
  bool is_configured = 1;
  repeated string calendar_ids = 2;
}

message CreateGroupRequest {
  Group group = 1;
  vendastatypes.FieldMask field_mask = 2;
  string host_id = 3;
}

message CreateGroupResponse {
  string id = 1;
}

message UpdateGroupRequest {
  Group group = 1;
  vendastatypes.FieldMask field_mask = 2;
  string host_id = 3;
  string id = 4;
}

message CreateServiceRequest {
  Service service = 1;
  vendastatypes.FieldMask field_mask = 2;
  string host_id = 3;
}

message CreateServiceResponse {
  string id = 1;
}

message UpdateServiceRequest {
  Service service = 1;
  vendastatypes.FieldMask field_mask = 2;
  string host_id = 3;
  string id = 4;
}

message DeleteGroupRequest {
  string id = 1;
  string host_id =2;
}

message DeleteServiceRequest {
  string id = 1;
  string host_id = 2;
}

message ListGroupsRequest {
  string host_id = 1;
}

message ListGroupsResponse {
  repeated Group groups = 1;
}

message ListServicesRequest {
  string host_id = 1;
}

message ListServicesResponse {
  repeated Service services = 1;
}

message CalendarMigrationResponse{

 bool status=1;

}

message CheckGroupOrServiceSlugExistRequest {
    string host_id = 1;
    string slug_identifier = 2;
    string category = 3;
}

message CheckGroupOrServiceSlugExistResponse {
    bool exists = 1;
}

message GetEntityAssociationRequest {
  string entity_id = 1; // can be either event_type_id or group_id
}

// Contains names of services and groups which has linked to the entity (event_type or group)
message GetEntityAssociationResponse {
  repeated string groups = 1;
  repeated string services = 2;
}

