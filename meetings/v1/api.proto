syntax = "proto3";

package meetings.v1;

option go_package = "github.com/vendasta/generated-protos-go/meetings/v1;meetings_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.meetings.v1.generated";

import "google/protobuf/empty.proto";
import "meetings/v1/meeting_source.proto";
import "meetings/v1/zoom.proto";
import "meetings/v1/google_meet.proto";
import "meetings/v1/meeting_guest.proto";
import "meetings/v1/meeting_host.proto";
import "reserve_with_google/api/api.proto";

// MeetingSourceAPI os a RPC server for managing all the meeting sources provided by the meetings microservice.
service MeetingSourceAPI {
    // List lists all the available meeting sources for the given user.
    rpc List (MeetingSourceListRequest) returns (MeetingSourceListResponse) {}
}

// GoogleMeet is a RPC service for managing all google-meet related RPCs.
service GoogleMeet {
    rpc CreateMeeting (GoogleMeetCreateMeetingRequest) returns (GoogleMeetCreateMeetingResponse) {}
    // ListMeetings lists the events in the users connected google calendar
    rpc ListMeetings (GoogleMeetListMeetingsRequest) returns (GoogleMeetListMeetingsResponse) {}
}

// Zoom is a RPC service for managing all zoom related RPCs.
service Zoom {
    rpc CreateMeeting (CreateZoomMeetingRequest) returns (CreateZoomMeetingResponse) {}
}

// ReserveWithGoogle is a RPC service that Google calls to interact with Meeting Scheduler as a Reserve with Google backend
service ReserveWithGoogle {
    rpc HealthCheck (google.protobuf.Empty) returns (google.protobuf.Empty) {}
    rpc BatchAvailabilityLookup (ext.maps.booking.partner.v3.BatchAvailabilityLookupRequest) returns (ext.maps.booking.partner.v3.BatchAvailabilityLookupResponse) {}
    rpc CreateBooking (ext.maps.booking.partner.v3.CreateBookingRequest) returns (ext.maps.booking.partner.v3.CreateBookingResponse) {}
    rpc UpdateBooking (ext.maps.booking.partner.v3.UpdateBookingRequest) returns (ext.maps.booking.partner.v3.UpdateBookingResponse) {}
    rpc GetBookingStatus (ext.maps.booking.partner.v3.GetBookingStatusRequest) returns (ext.maps.booking.partner.v3.GetBookingStatusResponse) {}
    rpc ListBookings (ext.maps.booking.partner.v3.ListBookingsRequest) returns (ext.maps.booking.partner.v3.ListBookingsResponse) {}
}

// ========== BELOW SERVICES ARE PART OF A CALENDAR MODULE ========== //

// MeetingHost APIs require an identity. Each identity gets it's own meeting scheduler by default.
// Multi-identity meeting scheduling is not supported at this time.
//
// There are 3 concepts:
// 1. Meeting Type - this can be as simple as the desired duration of the meeting.
//     - Every identity has their own meeting types.
// 2. Availability - A span of time with optional meeting types you will accept during that time.
//     - Every identity has their own availability.
// 3. Booked Meetings - meetings that clients have booked with you.
service MeetingHost {
    rpc BuildHostId(BuildHostIdRequest) returns (BuildHostIdResponse) {}

    rpc DoesCalendarExist(DoesCalendarExistRequest) returns (DoesCalendarExistResponse) {}
    rpc CreateCalendar(CreateCalendarRequest) returns (CreateCalendarResponse) {}
    rpc UpdateCalendar(UpdateCalendarRequest) returns (google.protobuf.Empty) {}
    rpc GetCalendar(HostGetCalendarRequest) returns (HostGetCalendarResponse) {}

    // These Ensure RPCs are used in meeting scheduler during setup.
    // A personal calendar should automatically exist for users who start using Meeting Scheduler.
    // A group calendar should automatically exist for sales teams and businesses.
    // A session event calendar should automatically exist for pids and businesses.
    rpc EnsurePersonalCalendarExists(EnsurePersonalCalendarExistsRequest) returns (EnsurePersonalCalendarExistsResponse) {}
    rpc EnsureGroupCalendarsExist(EnsureGroupCalendarsExistRequest) returns (EnsureGroupCalendarsExistResponse) {}
    rpc EnsureSessionEventCalendarsExist(EnsureSessionEventCalendarsExistRequest) returns (EnsureSessionEventCalendarsExistResponse) {}

    rpc GetHostsForCalendar(GetHostsForCalendarRequest) returns (GetHostsForCalendarResponse) {}

    rpc GetMeetingTypesForCalendars(GetMeetingTypesForCalendarsRequest) returns (GetMeetingTypesForCalendarsResponse) {}

    rpc ListAvailability(ListAvailabilityRequest) returns (ListAvailabilityResponse) {}

    rpc BookMeeting(HostBookMeetingRequest) returns (HostBookMeetingResponse) {}
    rpc GetHostMeeting(GetHostMeetingRequest) returns (GetHostMeetingResponse) {}
    rpc ListBookedMeetings(ListBookedMeetingsRequest) returns (ListBookedMeetingsResponse) {}
    rpc CancelMeeting(CancelMeetingRequest) returns (google.protobuf.Empty) {}
    rpc RescheduleMeeting(RescheduleMeetingRequest) returns (google.protobuf.Empty) {}
    // Will only update metadata fields included in the request and leaves the old ones alone.
    rpc UpdateMeetingMetadata(UpdateMeetingMetadataRequest) returns (google.protobuf.Empty) {}

    rpc SetGeneralAvailability(SetGeneralAvailabilityRequest) returns (google.protobuf.Empty) {}

    rpc CreateAvailability(CreateAvailabilityRequest) returns (CreateAvailabilityResponse) {}
    rpc UpdateAvailability(UpdateAvailabilityRequest) returns (google.protobuf.Empty) {}
    rpc DeleteAvailability(DeleteAvailabilityRequest) returns (google.protobuf.Empty) {}
    rpc IsHostConfigured(IsHostConfiguredRequest) returns (IsHostConfiguredResponse) {}

    rpc GetHostPreferences(GetHostPreferencesRequest) returns (GetHostPreferencesResponse) {}
    rpc UpdateHostPreferences(UpdateHostPreferencesRequest) returns (google.protobuf.Empty) {}

    rpc GetMeetingType(HostGetMeetingTypeRequest) returns (HostGetMeetingTypeResponse) {}
    rpc ListMeetingTypes(HostListMeetingTypesRequest) returns (HostListMeetingTypesResponse) {}
    rpc CreateMeetingType(CreateMeetingTypeRequest) returns (CreateMeetingTypeResponse) {}
    rpc UpdateMeetingType(UpdateMeetingTypeRequest) returns (google.protobuf.Empty) {}
    rpc DeleteMeetingType(DeleteMeetingTypeRequest) returns (google.protobuf.Empty) {}

    // An idempotent operation that ensures the host has initial meeting types.
    // This is a no-op if the calendar already has meeting types
    rpc CreateDefaultMeetingTypes(CreateDefaultMeetingTypesRequest) returns (google.protobuf.Empty) {}
    rpc SendMeetingRequestEmail(SendMeetingRequestEmailRequest) returns (google.protobuf.Empty) {}
    rpc IsCalendarConfigured(IsCalendarConfiguredRequest) returns (IsCalendarConfiguredResponse) {}

    rpc CreateGroup(CreateGroupRequest) returns (CreateGroupResponse) {}
    rpc UpdateGroup(UpdateGroupRequest) returns (google.protobuf.Empty) {}
    rpc DeleteGroup(DeleteGroupRequest) returns (google.protobuf.Empty) {}
    rpc ListGroups(ListGroupsRequest) returns (ListGroupsResponse) {}

    rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse) {}
    rpc UpdateService(UpdateServiceRequest) returns (google.protobuf.Empty) {}
    rpc DeleteService(DeleteServiceRequest) returns (google.protobuf.Empty) {}
    rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {}
    rpc CalendarMigration (google.protobuf.Empty) returns (CalendarMigrationResponse) {}
    rpc GetGroup(GetGroupRequest) returns (GetGroupResponse) {}
    rpc GetService(GetServiceRequest) returns (GetServiceResponse) {}
    rpc CheckGroupOrServiceSlugExist(CheckGroupOrServiceSlugExistRequest) returns (CheckGroupOrServiceSlugExistResponse) {}
    // Allow host to book meeting with clients from My meeting page (CRM)
    rpc HostBookMeeting(BookMeetingRequest) returns (BookMeetingResponse) {}
    // Fetch group & service menus linked to entity (either event type or group)
    rpc GetEntityAssociations(GetEntityAssociationRequest) returns (GetEntityAssociationResponse) {}
}

// MeetingGuest APIs are authentication-optional
// - they will accept an identity being passed, but an identity is not required.
//
// The time slots returned from ListAvailableMeetings are dynamic in that they depend on the meeting type in order to be calculated.
// Different meeting types can have different time durations, so the available time slots are not known until the meeting type is known.
//
// There are two ways the APIs can be used:
//     1. The meeting type is predetermined by the person providing available time slots
//     - the user would only see a screen of available fixed-length time slots.
//     - No API call to ListMeetingTypes is necessary, the client can infer the meeting type by url.
//     2. An API call is made to ListMeetingTypes first, presenting the user with a list available meeting types,
//    - possibly with a range of different durations, and upon selecting a meeting type ListAvailableMeetings is called.
service MeetingGuest {
    rpc ListMeetingTypes(ListMeetingTypesRequest) returns (ListMeetingTypesResponse) {}
    rpc GetMeetingType(GetMeetingTypeRequest) returns (GetMeetingTypeResponse) {}
    rpc ListAvailableTimeSlots(ListAvailableTimeSlotsRequest) returns (ListAvailableTimeSlotsResponse) {}
    rpc BookMeeting(BookMeetingRequest) returns (BookMeetingResponse) {}
    rpc CancelMeeting(GuestCancelMeetingRequest) returns (google.protobuf.Empty) {}
    rpc RescheduleMeeting(GuestRescheduleMeetingRequest) returns (google.protobuf.Empty) {}
    rpc GetGuestBookedMeeting(GuestGetBookedMeetingRequest) returns (GuestGetBookedMeetingResponse) {}

    // Gets branding data for an individual host or calendar (collection of hosts)
    rpc GetCalendar(GetCalendarRequest) returns (GetCalendarResponse) {}
    // Gets branding data only for an individual host; Will 404 on calendarIDs that don't map to an individual host
    rpc GetHost(GetHostRequest) returns (GetHostResponse) {}
    rpc GetGroup(GetGroupRequest) returns (GetGroupResponse) {}
    rpc IsHostConfigured(GuestIsHostConfiguredRequest) returns (GuestIsHostConfiguredResponse) {}
    rpc GetService(GetServiceRequest) returns (GetServiceResponse) {}
}
