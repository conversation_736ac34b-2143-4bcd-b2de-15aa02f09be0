# 1.94.0
- Add `delivery_rate`, `open_rate` and `click_to_open` rate in `GetAttributeEventStatsResponse`

# 1.93.0
- Add `BackfillEmailDomainCreatedSubUser` RPC

# 1.92.0
- Add `unsubscribed` to `Status` on `BatchSearchRecipientStatusResponse`

# 1.91.0
- Add `last_update` to `Status`

# 1.90.0
- Move `UnsubscribeRecipient` to `RecipientStatus`
- Add `ResubscribeRecipient` to `RecipientStatus`

## 1.89.0
- Add `UnsubscribeRecipient` to `Admin`

## 1.88.0
- Add admin scope to RedirectEmail RPC

## 1.87.0
- Add `reply_to` to `CustomSender`

## 1.86.0
- Add `DisposableEmailService` service with `CheckDisposableEmail` RPC. Moved from the prospect microservice.

## 1.85.0
- Add `storage_object_name` and `storage_object_url` to `AttachmentInfo` message

## 1.84.0
- Add `GetReplyEmail` RPC to `InboundMessageMapping` Service

## 1.83.0
- Add bounce_expiry time and infraction_count to the Status message

## 1.82.0
- Add recipient to `SendWhitelabelEmailToUserRequest` to support both users and contacts

## 1.81.0
- Add `GetInboundMessageMapping` RPC to `InboundMessageMapping` Service

## 1.80.0
- Add `subuser_id` to ListEmailsRequest Filters

## 1.79.0
- Add mismatch details to `VerifyDefaultDomainResponse`

## 1.78.0
- Add `business-app` scope to `GetAttributeEventStats` `GetAttributeEventLinkStats`

## 1.77.0
- Add DMARC records to pending domain related protos

## 1.76.0
- Add `InboundMessageMapping` Service and `CreateInboundMessageMapping` RPC

## 1.75.0
- Add DMARC records to `EmailDomain` message

>>>>>>> master
## 1.74.0
- (BREAKING CHANGE) Rename `ReplyTrackingOption` message to `InboundMessageMappingOptions`

## 1.73.0
- Add `InboundMessage` Service and `GetInboundMessage` RPC

## 1.72.0
- Add `ReplyTrackingOption` to `SendFromAuthorizedDomain` rpc

## 1.71.0
- Add `VerifyEmailDomainDmarc` RPC

## 1.70.0
- Add `attachments` and `url_attachments` to `WhiteLabelEmailMessage` message

## 1.69.0
- Add "yesware" as a sender type for `GetBouncedRecipient` and `UnbounceRecipient`
  (I'm pretty sure this is not a breaking change because it's only adding a new integer value) 

## 1.68.0
- Add CustomSender to `SendFromAuthorizedDomain` RPC

## 1.67.0
- Add `SendFromAuthorizedDomain` RPC

## 1.66.0
- Expose "matched_suffix" on `VerifyPendingEmailDomainResponse` RPC

## 1.65.0
- Add `email_address` to the `EmailEvent` message

## 1.64.0
- Add `unique_recipients` for each event on `GetAttributeEventStatsResponse`

## 1.63.0
- Add `unique_count` for each event to GetAttributeEventStatsResponse

## 1.62.0
- Add `BatchSearchRecipientStatus` RPC

## 1.61.0
- Add 'ExportAttributeEventClickStats' to 'EmailEventService'

## 1.60.0
- Allow marking message contents as sensitive in order to be encrypted at-rest.  
  
  This prevents the sensitive email contents from being seen while "at-rest", in vstore and the email viewer in admin center. The contents are only decrypted while the email is being sent by the email service.

## 1.59.0
- Add `EventType` to `ListForAttributes` request

## 1.58.0
- Add `ListLinkClickActivityForAttribute` to `EmailEventService`

## 1.57.0
- Add `GetAttributeEventLinkStats` to `EmailEventService`

## 1.56.0
- Add `links` to `WhiteLabelEmailMessage`

## 1.55.0
- Add `GetMultiMessage` to `MessageService`

## 1.54.0
- Expose more info about mismatched pending domain records

## 1.53.0
- Add `GetAttributeEventStats` to `EmailEventService`

## 1.52.0
- Add `ListForAttributes` to `EmailEventService`

## 1-51.0
- Add `ResetRecipientStatusRequest` and `ResetRecipientStatus`
  - this will allow us to reset a recipientStatus

## 1.50.0
- Add `is_transactional` as an input to `SendWhitelabeEmailToUser` RPC
  - This will enable the sending of transactional whitelabeled emails.

## 1.49.0
- Add Block service

## 1.48.0
- Allow multiple esp_ids when looking up ListEmailsRequest.
- Deprecate esp_id field on ListEmailsRequest

## 1.47.0
- Add normalized url clicked to email event 

## 1.46.0
- Add RPC `QuarantineSender`

## 1.45.0
- Add idempotentcy_key to the SendWhitelabeEmailToUser RPC

## 1.44.0
- Renamed `Email` service to `Send` to overcome an issue with SDK generation 

## 1.43.0
- Add domain to the DeletePendingDomain request

## 1.42.0
- Add `Email` service and `SendWhitelabelEmailToUser` RPC
  - `Email` is the successor to `EmailClient` and should only be used for email
    sending RPCs. 

## 1.41.0
- Add RPC `GetBouncedRecipient` and `UnbounceRecipient`

## 1.40.0
- Add `VerifyDefaultDomain` to `Domain` service

## 1.39.0
- Add `send_from_name` to `ChangeEmailDomainRequest`

## 1.38.0
- Add RPC for getting default domain

## 1.37.0
- Rewrites the `EmailV2` message to use single recipient and non-user ID `From` and `Reply-to` fields

## 1.36.0
- Add RPC for getting sender info

## 1.35.0
- Add `SendV2` RPC which uses User ID participants to replace email/ name participant
  - This new message type also removes the CC and BCC fields
- This also deprecates the old `Send` RPC

## 1.34.0
- Added RPC for creating a new PendingDomain 

## 1.33.0
- Allow "admin" scope to access pending domain RPCs

## 1.32.0
- Add `override_sender` to `RedirectEmailRequest.override` to facilitate testing

## 1.31.0
- Add RPCs for deleting sub user and pending domain

## 1.30.0
- Add flags to "RedirectEmail" RPC to optionally include content and attributes

## 1.29.0
- Add replacement field

##  1.28.0
- Add GetUnsubscribeLink to the Preferences server

## 1.27.0
- Add new event type for resubscribe event

## 1.26.0
- Add `EmailClient` service with `ResubscribeRecipientFromUnsubscribeLink` RPC for resubscribing a user from the Email Client

## 1.25.0
- Add a Preferences service and a GetOptions, GetPreferences and SavePreferences RPCs

## 1.24.1
- Add admin scope to `GetMessage` RPC, so it can be used by Vendasta Center

## 1.24.0
- Add new service to for messages
- Add new RPC to `Message` service: `GetMessage` for getting email by ID 

## 1.23.1
- Reorder RPCs to trick cloudbuild...

## 1.23.0
- Add GetUnsubscribedRecipient RPC and ResubscribeRecipient RPC

## 1.22.0
- Add sendFromUsername to GetPendingEmailDomainResponse for the GetPendingEmail RPC

## 1.21.0
- Add the RPC for GetPendingEmailDomain

## 1.20.0
- Deprecate the paging_metadata on ListEmailEventsResponse and provide the paging data as a next_cursor

## 1.19.0
- Add ServiceProviderInfo to EmailMessage

## 1.18.0
- Add `PartnerEmailQuota` service with `Upsert`, `BulkUpsert`, `Delete`, and `GetMulti` rpcs

## 1.17.0
- Add `partner_id` and `business_id` to `ListEmailEventsFilters` message

## 1.16.0
- Add `reason` to `EmailEvent` message

## 1.15.0
- Add `url_clicked` to `EmailEvent` message

## 1.14.0
- Added URLAttachments message allowing attachments outside GCS

## 1.13.1
- fix Queue management proto

## 1.13.0
- Add Queue Management entries

## 1.12.0
-Add CreateEmailDomainWithSendAs and revert changes to CreateEmailDomain

## 1.11.0
- Add SendWithCustom endpoint

## 1.10.0
- Update CreateEmailDomain endpoint to include Send As information

## 1.9.0
- Add DeleteEmailDomain endpoint 

## 1.8.5
- Refactoring the EmailEventBucket message to be able to pass back the date along with the quantity roll up.

## 1.8.4
- Refactor the EmailEventRollup definition to just contain the quantity. Make the EmailEvent based messages have the same naming format

## 1.8.3
- Refactor the request filters for GetMultiEmailRollups to filter by notification type and bucket by the event_type.

## 1.8.2
- Add GetMultiEmailRollups endpoint

## 1.8.1
- Add GetEmailDomain endpoint
- Add CreateEmailDomain endpoint

## 1.8.0
- Add partner_id, application_id, and business_id to the email to allow for Sendgrid DKIM support

## 1.7.0
- Add idempotency_key to Send RPC

## 1.6.0
- add attached_filename to GCSAttachment

## 1.5.0
- Add attachments to email message

## 1.4.0
- Add simple ListEmailEvents RPC

## 1.3.0
- Add esp_id filter to ListEmailsRequest

## 1.2.0
- Add timestamp to EmailMessage

## 1.1.0
- Add List RPC to EmailService

## 1.0.0
- Initial commit of email protos
