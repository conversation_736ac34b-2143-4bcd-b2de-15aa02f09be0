syntax = "proto3";

package email.v1;

option go_package = "github.com/vendasta/generated-protos-go/email/v1;email_v1";
option java_outer_classname = "ApiProto";
option java_package = "com.vendasta.email.v1.generated";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "vendasta_types/annotations.proto";
import "vendasta_types/date_range.proto";

import "email/v1/email_config.proto";
import "email/v1/email_event.proto";
import "email/v1/email_message.proto";
import "email/v1/partner_email_quota.proto";
import "email/v1/email_inbound_message.proto";
import "email/v1/disposable_email.proto";

enum SenderType {
  SENDER_TYPE_INVALID = 0;
  SENDER_TYPE_PARTNER = 1;
  SENDER_TYPE_BUSINESS = 2;
}

message Sender {
  // E.g. Partner, Business
  SenderType type = 1;
  // E.g. Partner ID, Account Group ID
  string id = 2;
  // E.g. conversation
  string app_namespace = 3;
}

message CustomSender {
  // The custom user name for the email address. ie; <NAME_EMAIL>
  string custom_email_username = 1;
  // The name to send from
  string name = 2;
  // The email replies should send to
  string reply_to = 3;
}

// Contains metadata about the paged response
message PagedResponseMetadata {
  // A cursor that can be provided to retrieve the next page of results
  string next_cursor = 1;
  // Whether or not more results exist
  bool has_more = 2;
  // Total results found by the operation
  int64 total_results = 3;
}

// To provide options for the paged request
message PagedRequestOptions {
  // cursor can be passed to retrieve the next page of results keyed by the cursor
  string cursor = 1;
  // page_size specifies the number of items to return in the next page
  int64 page_size = 2;
}

// List email events matching a set of filters
message ListEmailEventsRequest {
  // Filters to filter the list of email events on
  message ListEmailEventsFilters {
    // Email ID to list events for
    string email_id = 1;
    // The partner context the email event is for
    string partner_id = 2;
    // The business context the email event is for
    string business_id = 3;
  }
  ListEmailEventsFilters filters = 1;
  // Options for how to page the response for this request
  PagedRequestOptions paging_options = 2;
}

// Response containing a list of email events and paging metadata
message ListEmailEventsResponse {
  // The email events matching the lookup query
  repeated EmailEvent events = 1;
  // Metadata about the paging
  // Deprecated: This data is no longer provided. Use cursor.
  PagedResponseMetadata paging_metadata = 3;
  // Cursor for fetching an additional page of results
  string next_cursor = 4;
}

message EmailEventFilter {
  // The partner context the email event is for.
  string partner_id = 1;
  // The market context - under the partner - the email event is for.
  string market_id = 2;
  // The notification type list to filter by
  repeated NotificationType notification_types = 3;
  // The event type list to filter by
  repeated EventType event_types = 4;
  // the date range to filter the data by
  vendastatypes.DateRange date_range = 5;
}

message GetMultiEmailRollupsRequest {
  // the buckets specifying what email data to rollup on.
  repeated Bucket buckets = 1;
  // The filters to apply
  EmailEventFilter filter = 2;

  // Example usage of Buckets for emails based on the campaign focus type
  // [{
  //    bucket_name = open_event
  //
  //    attribute = {
  //      key = event_type
  //      value = open
  //    }
  // },
  // {
  //    bucket_name = click_event
  //
  //    attribute = {
  //      key = event_type
  //      value = event
  //    }
  // }]
}

message GetMultiEmailRollupsResponse {
  // a list of EmailEventBucket by the bucket name
  repeated EmailEventBucket email_rollups = 1;
}

message EmailEventBucket  {
  message EventRollup {
    // the date for the rollup
    google.protobuf.Timestamp date = 1;
    // the quantity of the Email Events for the given date
    int64 quantity = 2;
  }

  // The name of the bucket
  string bucket_name = 1;
  // The Events rolled up into daily counts.
  repeated EventRollup event_rollup = 2;
}


message ListEventsForAttributesRequest {
  Attribute attribute = 1;
  Sender sender = 2;
  string cursor = 3;
  int64 page_size = 4;
  repeated EventType event_types = 5;
}

message ListEventsForAttributesResponse {
  repeated EmailEvent events = 1;
  string cursor = 2;
  bool has_more = 3;
}

// Request for the GetAttributeEventStats RPC
message GetAttributeEventStatsRequest {
  // The sender to get EventStats for
  Sender sender = 1;
  // The attribute to get EventStats for
  Attribute attribute = 2;
}

// Response for the GetAttributeEventStats RPC
message GetAttributeEventStatsResponse {
  // The Event Stats
  message EventStats {
    // Event type
    EventType event_type = 1;
    // Count of events for the event type
    int64 count = 2;
    // Count of unique events for the event type
    int64 unique_count = 3;
    // Count of unique recipients for the event type
    int64 unique_recipients = 4;
  }
  // The list of event stats
  repeated EventStats data = 1;
  // The number of delivered / processed email events
  double delivery_rate = 2;
  // The number of opened / delivered email events
  double open_rate = 3;
  // The number of clicked / opened email events
  double click_to_open_rate = 4;
}

message ExportAttributeEventClickStatsRequest{
  // the sender to export links for
  Sender sender = 1;
  // the url to get stats for
  string normalized_url = 2;
  // the attribute key and value (ex: automation_step_id : id-123)
  Attribute attribute = 3;
  // the id of the request
  string user_id = 4;
}

// Request for the GetAttributeEventLinkStats RPC
message GetAttributeEventLinkStatsRequest {
  // The sender to get LinkStats for
  Sender sender = 1;
  // The attribute to get LinkStats for
  Attribute attribute = 2;
}

message LinkStats {
  // The normalized url
  string url = 1;
  // Total number of times this link has been clicked
  int64 total_clicks = 2;
  // Number of unique clicks on this link
  int64 unique_clicks = 3;
  // Rate of clicks per delivered email
  double click_rate = 4;
}

// Response for the GetAttributeEventLinkStats RPC
message GetAttributeEventLinkStatsResponse {
  // The list of link stats
  repeated LinkStats data = 1;
}

message ListLinkClickActivityForAttributeRequest{
  message PagedRequestOptions {
    // cursor can be passed to retrieve the next page of results keyed by the cursor
    string cursor = 1;
    // page_size specifies the number of items to return in the next page
    int64 page_size = 2;
  }
  // The sender to get LinkClickActivity for
  Sender sender = 1;
  // The attribute to get LinkClickActivity for
  Attribute attribute = 2;
  // paging options
  PagedRequestOptions paging_options = 3;
  // The normalized url to get stats for
  string normalized_url = 4;
  //
}

message LinkClickStats{
  message Recipient {
    // recipient email address
    string email_address = 1;
  }
  // The recipient
  Recipient recipient = 1;
  // The recent click date
  google.protobuf.Timestamp recent_date = 2;
  // The number  of clicks
  int64 count = 3;
}

message ListLinkClickActivityForAttributeResponse{
  PagedResponseMetadata paging_metadata = 1;
  repeated LinkClickStats data = 2;
}

// Service for email events
service EmailEventService {
  // List email events matching a set of filters
  rpc List(ListEmailEventsRequest) returns (ListEmailEventsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Returns a list of email event rollups by the buckets
  rpc GetMultiEmailRollups(GetMultiEmailRollupsRequest) returns (GetMultiEmailRollupsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc ListForAttributes(ListEventsForAttributesRequest) returns (ListEventsForAttributesResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc GetAttributeEventStats(GetAttributeEventStatsRequest) returns (GetAttributeEventStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "business-app"
    };
  };


  rpc GetAttributeEventLinkStats(GetAttributeEventLinkStatsRequest) returns (GetAttributeEventLinkStatsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
      scope: "business-app"
    };
  };

  rpc ListLinkClickActivityForAttribute(ListLinkClickActivityForAttributeRequest) returns (ListLinkClickActivityForAttributeResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc ExportAttributeEventClickStats(ExportAttributeEventClickStatsRequest) returns(google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

// InboundMessageMappingOptions is used to map the inbound message to a specific app and owner.
message InboundMessageMappingOptions {
  // The app namespace to use for this mapping. (e.g. "Inbox, crm, etc")
  string app_namespace = 1;
  // The owner ID to for this mapping. (e.g. "AG-1234, CONV-123, VUNI, MESSAGE-123, etc")
  string owner_id = 2;
  // The attribute arguments to use for this mapping.
  repeated Attribute attributes = 3;
}

message SendRequest {
  // The email message to send
  EmailMessage email = 1;
  // Email service provider ID, must be configured ahead of time
  string esp_id = 2;
  // The idemopotency key, if provided, will be used as part of the hash for determining the uniqueness of an email
  string idempotency_key = 3;
  // The ID of the partner sending the email
  string partner_id = 4;
  // The ID of the application sending the email
  string application_id = 5;
  // The ID of the business sending the email
  string business_id = 6;
}

message SendResponse {
  string email_id = 1;
}

message SendV2Request {
  // The email message to send.
  EmailMessageV2 email = 1;
  // Email service provider ID, must be configured ahead of time
  string esp_id = 2;
  // The idemopotency key, if provided, will be used as part of the hash for determining the uniqueness of an email
  string idempotency_key = 3;
  // The ID of the partner sending the email
  string partner_id = 4;
  // The ID of the application sending the email
  string application_id = 5;
  // The ID of the business sending the email
  string business_id = 6;
}

message SendV2Response {
  string email_id = 1;
}

message SendWithCustomDomainRequest {
  // The email message to send
  EmailWithCustomDomainMessage email = 1;
  // The idemopotency key, if provided, will be used as part of the hash for determining the uniqueness of an email
  string idempotency_key = 2;
  // The ID of the partner sending the email
  string partner_id = 3;
  // The ID of the application sending the email
  string application_id = 4;
  // The ID of the business sending the email
  string business_id = 5;
  // The email to be used if the custom domain is not configured
  string fallback_email = 6;
  // The name to be used if the custom domain is not configured
  string fallback_name = 7;
  // The Email service provider ID to be used with the fallback email and name
  string fallback_esp_id = 8;
}

message SendWithCustomDomainResponse {
  string email_id = 1;
  // result of the send. Indicates whether the custom or the fallback was used to send
  bool custom_domain_used = 2;
}

message ListEmailsRequest {
  message ListEmailsFilters {
    string to = 1;
    string from = 2;
    vendastatypes.DateRange date_range = 3;
    // Deprecated. Use esp_ids instead
    string esp_id = 4 [deprecated = true];
    string partner_id = 5;
    string application_id = 6;
    string business_id = 7;
    repeated string esp_ids = 8;
    string subuser_id = 9;
  }
  // String to use for full text search on all email fields
  string search_term = 1;
  // Exact match filters
  ListEmailsFilters filters = 2;
  // Options for how to page the response for this request
  PagedRequestOptions paging_options = 3;
}

message ListEmailsResponse {
  // The email messages matching the list query
  repeated EmailMessage emails = 1;
  // Paging metadata
  PagedResponseMetadata paging_metadata = 2;
}

message GetEmailQueuesStatusResponse {
  bool running = 1;
}

message GetEmailQueuesStatusRequest {

}

message SetEmailQueuesStatusRequest {
  bool running = 1;
}

message GetEmailDomainRequest {
  string partner_id = 1;
  string application_id = 2;
  string business_id = 3;
}

message GetEmailDomainResponse {
  EmailDomain domain = 1;
}

message BackfillEmailDomainCreatedSubUser {
  string partner_id = 1;
  string application_id = 2;
  string business_id = 3;
  string subuser_id =4;
}


message BackfillEmailDomainCreatedSubUserRequest {
  repeated BackfillEmailDomainCreatedSubUser data = 1;
}


message BackfillEmailDomainCreatedSubUserResponse {
  repeated BackfillEmailDomainCreatedSubUser data = 1;
  string status = 2;
}

message CreateEmailDomainRequest {
  string partner_id = 1;
  string application_id = 2;
  string business_id = 3;
  string user_id = 4;
  string domain = 5;
}

message CreateEmailDomainWithSendAsRequest {
  string partner_id = 1;
  string application_id = 2;
  string business_id = 3;
  string user_id = 4;
  string domain = 5;
  string send_as_email = 6;
  string send_as_name = 7;
}

message VerifyEmailDomainRequest {
  string partner_id = 1;
  string application_id = 2;
  string business_id = 3;
}

message VerifyEmailDomainDMARCRequest {
  Sender sender = 1;
}
message VerifyEmailDomainDMARCResponse {
  bool dmarc_pass = 1;
  string dmarc_record = 2;
  string reason = 3;
}

message DeleteEmailDomainRequest {
  string partner_id = 1;
  string application_id = 2;
  string business_id = 3;
}

enum ChangeEmailDomainStatus {
  CHANGE_EMAIL_DOMAIN_STATUS_INVALID = 0;
  CHANGE_EMAIL_DOMAIN_STATUS_ACCEPTED = 1;
  CHANGE_EMAIL_DOMAIN_STATUS_PENDING = 2;
}

message ChangeEmailDomainRequest {
  // The type of sender that this request is being made on behalf of (e.g. business, partner)
  SenderType sender_type = 1;
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  string sender_id = 2;
  // The domain to send emails from (e.g. for "<EMAIL>", this would be "example.com")
  string send_from_domain = 3;
  // The username to send emails from (e.g. for "<EMAIL>", this would be "test")
  string send_from_username = 4;
  // The complete email address to include as a "reply-to" on outgoing emails
  string reply_to_email = 5;
  // The name of the sender that emails will be sent from (e.g. for "<EMAIL>" can be "Example Corp")
  string send_from_name = 6;
}

message ChangeEmailDomainResponse {
  ChangeEmailDomainStatus status = 1;
}

message GetPendingEmailDomainRequest {
  // The type of sender that this request is being made on behalf of (e.g. business, partner)
  SenderType sender_type = 1;
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  string sender_id = 2;
}

message GetPendingEmailDomainTargetCNameRecords {
  string mail_subdomain = 1;
  string mail_value = 2;
  string dkim1_subdomain = 3;
  string dkim1_value = 4;
  string dkim2_subdomain = 5;
  string dkim2_value = 6;
  string dmarc_subdomain = 7;
  string dmarc_value = 8;
}

message GetPendingEmailDomainResponse {
  // The type of sender that this request is being made on behalf of (e.g. business, partner)
  SenderType sender_type = 1;
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  string sender_id = 2;
  // the domain to send emails from
  string send_from_domain = 3;
  // the SPF record that is desired on the DNS records
  string target_spf = 4;
  // the SPF record that is most recently found on the DNS records
  string current_spf = 5;
  // The username to send emails from (e.g. for "<EMAIL>", this would be "test")
  string send_from_username = 6;
  // The CName targets to set on the domain before it can be used in the platform
  GetPendingEmailDomainTargetCNameRecords targets = 7;
}

// VerifyPendingEmailDomainRequest can be called to check if all of the
// PendingDomain's records match the values on the actual domain.
message VerifyPendingEmailDomainRequest {
  // The type of sender that this request is being made on behalf of (e.g. business, partner)
  SenderType sender_type = 1;
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  string sender_id = 2;
}

message VerifyPendingEmailDomainMismatch {
  // E.g. CNAME, TXT, A
  string dns_record_type = 1;
  string url = 2;
  string expected_value = 3;
  // The record that was found on the domain. Or empty if no record found.
  string actual_value = 4;
  // If a record was found on the domain, this will be the first "chunk" of the
  // record value that matches the expected value. This helps detect typo errors.
  string matched_prefix = 5;
  // If a record was found on the domain, this will be the last "chunk" of the
  // record value that matches the expected value. This helps detect typo errors.
  string matched_suffix = 6;
}

message VerifyPendingEmailDomainResponse {
  bool all_records_match = 1;
  repeated VerifyPendingEmailDomainMismatch mismatches = 2;
}

message DeletePendingEmailDomainRequest {
  // The type of sender that this request is being made on behalf of (e.g. business, partner)
  SenderType sender_type = 1;
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  string sender_id = 2;
  string domain = 3;
}

// Deprecated: This service contains lots of random stuff and legacy endpoints
// that are not aligned with our API vision. Prefer the `Email` service instead.
service EmailService {
  // Send sends an email through sendgrid: Deprecated, use SendV2 instead
  rpc Send(SendRequest) returns (SendResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // SendWithCustomDomain sends an email using a custom domain which must be configured beforehand
  rpc SendWithCustomDomain(SendWithCustomDomainRequest) returns (SendWithCustomDomainResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc SendV2(SendV2Request) returns (SendV2Response) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  // List lists emails that were sent
  rpc List(ListEmailsRequest) returns (ListEmailsResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc GetEmailQueuesStatus(GetEmailQueuesStatusRequest) returns (GetEmailQueuesStatusResponse){
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Deprecated: Use AdminService.SetEmailQueuesStatus
  rpc SetEmailQueuesStatus(SetEmailQueuesStatusRequest) returns (google.protobuf.Empty){
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Returns the email configuration for a domain
  rpc GetEmailDomain(GetEmailDomainRequest) returns (GetEmailDomainResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Backfills email domain with given created on subuser and returns status. if failure returns list of EmailDomain for which backfill fails
  rpc BackfillEmailDomainCreatedSubUser(BackfillEmailDomainCreatedSubUserRequest) returns (BackfillEmailDomainCreatedSubUserResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Create email configuration for a domain
  rpc CreateEmailDomain(CreateEmailDomainRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Create email configuration for a domain
  rpc CreateEmailDomainWithSendAs(CreateEmailDomainWithSendAsRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Attempt to verify a domain
  rpc VerifyEmailDomain(VerifyEmailDomainRequest) returns (GetEmailDomainResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // Delete email configuration for a domain
  rpc DeleteEmailDomain(DeleteEmailDomainRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  rpc ChangeEmailDomain(ChangeEmailDomainRequest) returns (ChangeEmailDomainResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc GetPendingEmailDomain(GetPendingEmailDomainRequest) returns (GetPendingEmailDomainResponse) {
    // admin scope is accepted because this will be called by: Partner Center Client, Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc VerifyPendingEmailDomain(VerifyPendingEmailDomainRequest) returns (VerifyPendingEmailDomainResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc DeletePendingEmailDomain(DeletePendingEmailDomainRequest) returns  (google.protobuf.Empty) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // This service is deprecated. For sending emails, use `Email`. For everything
  // else, use domain-specific services like `Domain`, `Message`, `Sender`, etc.
}

message SendWhitelabelEmailToUserRequest {
  // The email sender
  Sender sender = 1;
  // The email recipient
  UserParticipant recipient_user = 2 [deprecated = true]; // Use recipient instead
  // The Email Message
  WhiteLabelEmailMessage message = 3;
  // The idemopotency key, if provided, will be used as part of the hash for determining the uniqueness of an email. This is used to prevent the email from sending more than once
  string idempotency_key = 4;
  // Set true if the user should reasonably expect to receive this email as a
  // result of interaction with the platform. Set false if this email is
  // marketing content or otherwise something that the user may not expect/want.
  bool transactional = 5;
  oneof recipient {
    // send to a user
    UserParticipant user_participant = 6;
    // send to a contact
    ContactParticipant contact_participant = 7;
  }
}

message SendWhitelabelEmailToUserResponse {
  string email_id = 1;
}

message SendFromAuthorizedDomainRequest {
  // The email sender
  Sender sender = 1;
  // The email recipient
  Participant recipient = 2;
  // The Email message
  WhiteLabelEmailMessage message = 3;
  // The idemopotency key, if provided, will be used as part of the hash for determining the uniqueness of an email. This is used to prevent the email from sending more than once
  string idempotency_key = 4;
  // Set true if the user should reasonably expect to receive this email as a
  // result of interaction with the platform. Set false if this email is
  // marketing content or otherwise something that the user may not expect/want.
  bool transactional = 5;
  // Set true to encrypt the content of the email when it is stored.
  // This means you will not be able to see the content of the email
  // in places like Admin Center or vStore. The content is decrypted
  // immediately before the email leaves our system to be sent.
  bool encrypt_content_at_rest = 6;
  // The custom sender to use for this email. If not provided, the default sender will be used.
  CustomSender custom_sender = 7;
  // The Inbound message mapping options. It could be used to map the reply message to a specific app and owner.
  InboundMessageMappingOptions inbound_message_mapping_options = 8;
}

message SendFromAuthorizedDomainResponse {
  string email_id = 1;
}

service Send {
  rpc SendWhitelabelEmailToUser(SendWhitelabelEmailToUserRequest) returns (SendWhitelabelEmailToUserResponse) {
    option (vendastatypes.access) = {
      scope: "whitelabel-email.send-to-user"
    };
  }
  rpc SendFromAuthorizedDomain(SendFromAuthorizedDomainRequest) returns (SendFromAuthorizedDomainResponse) {}
}

message VerifyDefaultDomainRequest {
  Sender sender = 1;
}

message VerifyDefaultDomainMismatch {
  // E.g. CNAME, TXT, A
  string dns_record_type = 1;
  string url = 2;
  string expected_value = 3;
  // The record that was found on the domain. Or empty if no record found.
  string actual_value = 4;
  // If a record was found on the domain, this will be the first "chunk" of the
  // record value that matches the expected value. This helps detect typo errors.
  string matched_prefix = 5;
  // If a record was found on the domain, this will be the last "chunk" of the
  // record value that matches the expected value. This helps detect typo errors.
  string matched_suffix = 6;
}

message VerifyDefaultDomainResponse {
  bool all_records_match = 1;
  repeated VerifyDefaultDomainMismatch mismatches = 2;
}

service Domain {
  rpc CreatePendingDomain(CreatePendingDomainRequest) returns (GetPendingEmailDomainResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc ListAllDomains(ListAllDomainsRequest) returns (ListAllDomainsResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc GetDefaultDomain(GetDefaultDomainRequest) returns (GetDefaultDomainResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc VerifyDefaultDomain(VerifyDefaultDomainRequest) returns (VerifyDefaultDomainResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc VerifyEmailDomainDMARC(VerifyEmailDomainDMARCRequest) returns (VerifyEmailDomainDMARCResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

message CreatePendingDomainRequest {
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  Sender sender = 1;
  // The domain to send emails from (e.g. for "<EMAIL>", this would be "example.com")
  string domain = 2;
}

message ListAllDomainsRequest {
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  Sender sender = 1;
}

message ListAllDomainsResponse {
  // The set of all domains available for this sender, including "primary" and any pending domains.
  repeated SenderDomain domains = 1;
}

message GetDefaultDomainRequest {
  SenderType sender_type = 1;
}

message GetDefaultDomainResponse {
  string domain = 1;
}

message SenderDomain {
  // the domain to send emails from (e.g. for "<EMAIL>", this would be "example.com")
  string send_from_domain = 1;
  // the SPF record that is desired on the DNS records
  string target_spf = 2;
  // the SPF record that is most recently found on the DNS records
  string current_spf = 3;
  // The CName targets to set on the domain before it can be used in the platform
  GetPendingEmailDomainTargetCNameRecords targets = 4;
  // True if this is the sender's preferred sending address
  bool is_primary = 5;
}

message QuarantineSenderRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
}

service Senders {
  rpc GetSenderInfo(GetSenderInfoRequest) returns (GetSenderInfoResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc QuarantineSender(QuarantineSenderRequest) returns (google.protobuf.Empty) {}
}

message GetSenderInfoRequest {
  // The ID of specific sender that this request is being made on behalf of (e.g. account group ID, partner ID)
  Sender sender = 1;
}

message GetSenderInfoResponse {
  // The domain name (e.g. example.com) that this sender prefers to use for sending platform emails on their behalf
  string domain = 1;
  // A boolean that is only true if the sender has installed our DKIM records on their domain
  // Unauthorized domains are likely to land in spam and put our platform at risk of being block-listed.
  bool is_platform_authorized = 2;
  // A boolean that is true if this is a domain that is owned by the platform, rather than the sender.
  bool is_default_for_sender_type = 3;
  // The username (e.g. -->this<--@example.com) that this sender prefers to use for sending platform emails on their behalf
  string preferred_email_username = 4;
  // The display name (e.g. Joe Smith) that this sender prefers to use for sending platform emails on their behalf
  string preferred_email_display_name = 5;
  // The reply-to email that this sender prefers. This will not necessarily use the same domain.
  string preferred_reply_to_email_address = 6;
}

message UpsertPartnerEmailQuotaRequest {
  // ID of Partner
  string partner_id = 1;
  // Quota
  int64 daily_quota = 2;
}

// Entity and values to upsert
message PartnerEmailQuotaMutation {
  // ID of Partner
  string partner_id = 1;
  // Quota
  int64 daily_quota = 2;
}

message BulkUpsertPartnerEmailQuotaRequest {
  repeated PartnerEmailQuotaMutation partner_email_quota = 1;
}

message DeletePartnerEmailQuotaRequest {
  // ID of the Partner
  string partner_id = 1;
}

message GetMultiPartnerEmailQuotaRequest {
  // ID of the Partners
  repeated string partner_ids = 1;
}
message GetMultiPartnerEmailQuotaResponse {
  // Entities
  repeated PartnerEmailQuota partner_email_quotas = 1;
}

service PartnerEmailQuotaService {
  // Upsert will create or updated a PartnerEmailQuota entity
  rpc Upsert(UpsertPartnerEmailQuotaRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // BulkUpsert will create or update many PartnerEmailQuota entities
  rpc BulkUpsert(BulkUpsertPartnerEmailQuotaRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // Delete will soft delete a PartnerEmailQuota entity
  rpc Delete(DeletePartnerEmailQuotaRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  // GetMulti will retrieve multiple PartnerEmailQuota entities
  rpc GetMulti(GetMultiPartnerEmailQuotaRequest) returns (GetMultiPartnerEmailQuotaResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
}

message ListSendGridTeammatesRequest {}

// SendGridTeammate is a person who has a role defined on Vendasta's SendGrid account
message SendGridTeammate {
  string full_name = 1;
  string email = 2;
  string role = 3;
  string slack_user_name = 4;
  string slack_url = 5;
}

message ListSendGridTeammatesResponse {
  repeated SendGridTeammate teammates = 1;
}

message ListSendGridSubUserReputationsRequest {
  int64 page_size = 1;
  int64 cursor = 2;
}

message SendGridSubUserReputation {
  string username = 1;
  float reputation_percent = 2;
}

message ListSendGridSubUserReputationsResponse {
  repeated SendGridSubUserReputation reputations = 1;
  int64 next_cursor = 2;
}

// SendGridService is used for interacting with SendGrid (or SendGrid-specific
// data) through the email microservice
service SendGridService {
  // ListSendGridTeammates returns a list of people who are "teammates" on
  // Vendasta's SendGrid account.
  rpc ListSendGridTeammates(ListSendGridTeammatesRequest) returns (ListSendGridTeammatesResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc ListSendGridSubUserReputations(ListSendGridSubUserReputationsRequest) returns (ListSendGridSubUserReputationsResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

enum EmailQueueStatus {
  EMAIL_QUEUE_STATUS_UNSPECIFIED = 0;
  EMAIL_QUEUE_STATUS_RUNNING = 1;
  EMAIL_QUEUE_STATUS_DISABLED = 2;
}

message AdminSetEmailQueuesStatusRequest {
  EmailQueueStatus status = 1;
}

message RedirectEmailRequest {
  bool auth_check_only = 1;
  string email_id = 2;
  string new_recipient_email_address = 3;
  // Set to "true" to include the email content (i.e. the body)
  // (WARNING: Ensure the content does not contain sensitive info like password-reset links before sending)
  bool include_content = 4;
  // Set to "true" to include the original message's attributes
  // (WARNING: this may cause sender-facing engagement stats to be surfaces in the platform)
  bool include_attributes = 5;
  // Support currently in development: non-zero values will be applied over the original message
  EmailMessage override = 6;
  Sender override_sender = 7;
}

message RedirectEmailResponse {
  string new_email_id = 1;
}

message GetUnsubscribedRecipientRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string recipient_email = 3;
}

message GetUnsubscribedRecipientResponse {
  // The recipient's email should be included in the response if the recipient is currently unsubscribed, otherwise an empty string and a NotFound error should be returned
  string recipient_email = 1;
}

message ResubscribeRecipientRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string recipient_email = 3;
}

message InitiateEmailDomainRollbackRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
}

message InitiateEmailDomainRectifyRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  bool bypass_frequency_limit = 3;
}

enum Namespace {
  NAMESPACE_INVALID = 0;
  NAMESPACE_EMAIL = 1;
}

message Category {
  Namespace namespace = 1;
  string id = 2;
  string name = 3;
}

message GetCategoriesRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
}

message GetCategoriesResponse {
  repeated Category categories = 1;
}

message GetCategoryPreferencesRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string recipient_id = 3;
}

message GetCategoryPreferencesResponse {
  repeated string opted_in_email_category_ids = 1;
  bool email_preference_initialized = 2;
}

message SaveCategoryPreferencesRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  oneof recipient_id {
    string unverified_email_address = 3;
    string user_id = 4;
  }
  repeated string opted_in_email_category_ids = 6;
}

message GetUnsubscribeLinkRequest {
  string id = 1;
}

message GetUnsubscribeLinkResponse {
  string email_id = 1;
  SenderType sender_type = 2;
  string sender_id = 3;
  repeated string recipient_ids = 4;
}

// Unbounce sender type is used instead of SenderType because the unbounce
// RPC can also be used for interacting with Yesware's SendGrid account.
enum BounceSenderType {
  UNBOUNCE_SENDER_TYPE_INVALID = 0;
  UNBOUNCE_SENDER_TYPE_PARTNER = 1;
  UNBOUNCE_SENDER_TYPE_BUSINESS = 2;
  UNBOUNCE_SENDER_TYPE_YESWARE = 3;
}

message BounceSender {
  BounceSenderType type = 1;
  // E.g. Partner ID, Account Group ID, or empty for Yesware
  string id = 2;
}

message GetBouncedRecipientRequest {
  // ID of sender (e.g. partner, business, Yesware)
  BounceSender sender = 1;
  // email address of recipient
  string recipient_email = 2;
}

message GetBouncedRecipientResponse {
  // returns email of bounced recipient, if the recipient is bounced (else empty string)
  string recipient_email = 1;
}

message UnbounceRecipientRequest {
  // ID of sender (e.g. partner, business, product)
  BounceSender sender = 1;
  // email address of recipient
  string recipient_email = 2;
}

service Admin {
  rpc SetEmailQueuesStatus(AdminSetEmailQueuesStatusRequest) returns (google.protobuf.Empty){
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc RedirectEmail(RedirectEmailRequest) returns (RedirectEmailResponse){
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc ResubscribeRecipient(ResubscribeRecipientRequest) returns (google.protobuf.Empty){
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc GetUnsubscribedRecipient(GetUnsubscribedRecipientRequest) returns (GetUnsubscribedRecipientResponse){
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };
  rpc InitiateEmailDomainRollback(InitiateEmailDomainRollbackRequest) returns (google.protobuf.Empty){
  };
  rpc InitiateEmailDomainRectify(InitiateEmailDomainRectifyRequest) returns (google.protobuf.Empty){
  };


  rpc GetBouncedRecipient(GetBouncedRecipientRequest) returns (GetBouncedRecipientResponse){
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }

  rpc UnbounceRecipient(UnbounceRecipientRequest) returns (google.protobuf.Empty){
    // Un-bounce a recipient email
    option (vendastatypes.access) = {
      scope:"admin"
    };
  }
}

service Preferences {
  rpc GetCategories(GetCategoriesRequest) returns (GetCategoriesResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc GetCategoryPreferences(GetCategoryPreferencesRequest) returns (GetCategoryPreferencesResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc SaveCategoryPreferences(SaveCategoryPreferencesRequest) returns (google.protobuf.Empty);
  rpc GetUnsubscribeLink(GetUnsubscribeLinkRequest) returns (GetUnsubscribeLinkResponse);
}

message InitializeSubUserRequest {
  string sender_type = 1;
  string sender_id = 2;
}

message DeleteSubUserRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
}

service SubUser {
  rpc InitializeSubUser(InitializeSubUserRequest) returns (google.protobuf.Empty) {}
  rpc DeleteSubUser(DeleteSubUserRequest) returns (google.protobuf.Empty) {}
}

message GetMessageRequest {
  string email_id = 1;
}

message GetMessageResponse {
  EmailMessage message = 1;
}

message GetMultiMessageRequest {
  repeated string email_ids = 1;
}

message GetMultiMessageResponse {
  repeated EmailMessage messages = 1;
}

service Message {
  // Get looks up an email using the provided ID
  rpc GetMessage(GetMessageRequest) returns (GetMessageResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  // Get looks up an email using the provided ID
  rpc GetMultiMessage(GetMultiMessageRequest) returns (GetMultiMessageResponse) {
    // admin scope is accepted because this will be called by: Vendasta Center Client
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

message ResubscribeRecipientFromUnsubscribeLinkRequest {
  // Deprecated: use unsubscribe_link_id instead
  string unsubscribe_link_url = 1;
  string unsubscribe_link_id = 2;
}

// EmailClient contains RPCs purpose-built for the Angular project "email-client"
service EmailClient {
  rpc ResubscribeRecipientFromUnsubscribeLink(ResubscribeRecipientFromUnsubscribeLinkRequest) returns (google.protobuf.Empty) {
  }
}

message CreateBlockRequest {
  string block_type = 1;
  string message = 2;
  string conditions = 3;
}

message CreateBlockResponse {
  string block_type = 1;
  string id = 2;
}

message GetBlockRequest {
  string block_type = 1;
  string id = 2;
}

message GetBlockResponse {
  string block_type = 1;
  string id = 2;
  string message = 3;
  string conditions = 4;
}

message DeleteBlockRequest {
  string block_type = 1;
  string id = 2;
}

message ResetRecipientStatusRequest {
  Sender sender = 1;
  string recipient_email = 2;
}


enum RecipientStatusEnum {
  RECIPIENT_STATUS_INVALID = 0;
  RECIPIENT_STATUS_HARD = 1;
  RECIPIENT_STATUS_SOFT = 2;
  RECIPIENT_STATUS_UNKNOWN = 3;
  RECIPIENT_STATUS_RESET = 4;
  RECIPIENT_STATUS_NOT_BLOCKED = 5;
}

message BatchSearchRecipientStatusRequest {
  Sender sender = 1;
  repeated string recipient_emails = 2;
}

message ResubscribeRecipientStatusRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string recipient_email = 3;
}

message UnsubscribeRecipientStatusRequest {
  SenderType sender_type = 1;
  string sender_id = 2;
  string recipient_email = 3;
}

message BatchSearchRecipientStatusResponse {
  repeated Status recipient_statuses = 1;
}

message Status {
  Sender sender = 1;
  string recipient_email = 2;
  RecipientStatusEnum status = 3;
  int64 infraction_count = 4;
  google.protobuf.Timestamp bounce_expiry = 5;
  google.protobuf.Timestamp last_updated = 6;
  bool unsubscribed = 7;
}

service RecipientStatus {
  rpc ResetRecipientStatus(ResetRecipientStatusRequest) returns (google.protobuf.Empty){}
  rpc BatchSearchRecipientStatus(BatchSearchRecipientStatusRequest) returns (BatchSearchRecipientStatusResponse){
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc ResubscribeRecipientStatus(ResubscribeRecipientStatusRequest) returns (google.protobuf.Empty){}
  rpc UnsubscribeRecipientStatus(UnsubscribeRecipientStatusRequest) returns (google.protobuf.Empty){}
}

service Blocks {
  rpc CreateBlock(CreateBlockRequest) returns (CreateBlockResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc GetBlock(GetBlockRequest) returns (GetBlockResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
  rpc DeleteBlock(DeleteBlockRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  }
}

message GetInboundMessageRequest {
  // The email ID of the inbound message to retrieve
  string email_id = 1;
}

message GetInboundMessageResponse {
  InboundMessage inbound_message = 1;
}

service InboundMessageService {
  rpc GetInboundMessage(GetInboundMessageRequest) returns (GetInboundMessageResponse) {}
}

message CreateInboundMessageMappingRequest {
  // The app namespace to use for this mapping. (e.g. "Inbox, CRM, etc")
  string app_namespace = 1;
  // The idempotency key, the owner of the unique mapping record. (e.g. "AG-1234, CONV-123, VUNI, etc")
  string idempotency_key = 2;
  // The attribute arguments to use for this mapping.
  repeated Attribute attributes = 3;
}

message CreateInboundMessageMappingResponse {
  string mapping_id = 1;
}

message GetInboundMessageMappingRequest {
  // The app namespace to use for this mapping. (e.g. "Inbox, CRM, etc")
  string app_namespace = 1;
  // The idempotency key, the owner of the unique mapping record. (e.g. "AG-1234, CONV-123, VUNI, etc")
  string idempotency_key = 2;
}

message GetInboundMessageMappingResponse {
  InboundMessageMapping inbound_message_mapping = 1;
}

message GetReplyEmailRequest {
  // The sender of this request is being made on behalf of (e.g. account group, partner)
  Sender sender = 1;
  // The idempotency key, the owner of the unique mapping record. (e.g. "AG-1234, CONV-123, VUNI, etc")
  string idempotency_key = 2;
}

message GetReplyEmailResponse {
  // The reply address of owner (e.g. "AG-1234, CONV-123, VUNI, etc")
  string reply_address = 1;
}

service InboundMessageMappingService {
  rpc CreateInboundMessageMapping(CreateInboundMessageMappingRequest) returns (CreateInboundMessageMappingResponse) {}
  rpc GetInboundMessageMapping(GetInboundMessageMappingRequest) returns (GetInboundMessageMappingResponse) {}
  rpc GetReplyEmail(GetReplyEmailRequest) returns (GetReplyEmailResponse) {}
}

// DisposableEmail - Service related to emails that are likely to be fake, blacklisted, or temporary, often used for spam or abuse other services.
// Not a guarantee that the email is disposable, but a good indicator.
service DisposableEmail {
  // CheckDisposableEmail checks if the domain of an email is disposable
  rpc CheckDisposableEmail(CheckDisposableEmailRequest) returns (CheckDisposableEmailResponse);
}
